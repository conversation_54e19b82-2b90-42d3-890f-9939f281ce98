<?php
/**
 * Fix Tournament Format Detection Logic
 * SC_IMS Sports Competition and Event Management System
 */

require_once __DIR__ . '/../config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "<!DOCTYPE html>";
echo "<html><head><title>Tournament Format Detection Fix</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .warning { background: #fff3cd; color: #856404; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .info { background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 5px; margin: 10px 0; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
</style></head><body>";

echo "<h1>🎯 Tournament Format Detection Fix</h1>";

try {
    // Step 1: Check current sport types and their categories
    echo "<h2>📊 Step 1: Current Sport Types Analysis</h2>";
    
    // Check if sport_types table exists
    $result = $conn->query("SHOW TABLES LIKE 'sport_types'");
    if ($result->rowCount() == 0) {
        echo "<div class='warning'>⚠ sport_types table doesn't exist. Creating it...</div>";
        
        $sql = "CREATE TABLE sport_types (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) NOT NULL UNIQUE,
            description TEXT,
            category ENUM('individual', 'team', 'academic', 'judged') NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $conn->exec($sql);
        
        // Insert basic sport types
        $sport_types = [
            ['Traditional Sports', 'Traditional team and individual sports', 'team'],
            ['Individual Sports', 'Individual competitive sports', 'individual'],
            ['Academic Games', 'Academic competitions and quiz games', 'academic'],
            ['Judged Events', 'Performance-based judged competitions', 'judged']
        ];
        
        foreach ($sport_types as $type) {
            $stmt = $conn->prepare("INSERT INTO sport_types (name, description, category) VALUES (?, ?, ?)");
            $stmt->execute($type);
        }
        
        echo "<div class='success'>✅ Created sport_types table with basic categories</div>";
    }
    
    // Check current sports and their type assignments
    echo "<h3>Current Sports and Type Assignments:</h3>";
    $result = $conn->query("
        SELECT s.id, s.name, s.sport_type_id, st.name as type_name, st.category
        FROM sports s
        LEFT JOIN sport_types st ON s.sport_type_id = st.id
        ORDER BY s.name
    ");
    $sports = $result->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table>";
    echo "<tr><th>Sport ID</th><th>Sport Name</th><th>Type ID</th><th>Type Name</th><th>Category</th><th>Status</th></tr>";
    
    $unassigned_sports = [];
    foreach ($sports as $sport) {
        $status = $sport['sport_type_id'] ? '✅ Assigned' : '❌ Unassigned';
        $status_class = $sport['sport_type_id'] ? 'success' : 'error';
        
        echo "<tr>";
        echo "<td>{$sport['id']}</td>";
        echo "<td>{$sport['name']}</td>";
        echo "<td>" . ($sport['sport_type_id'] ?: 'NULL') . "</td>";
        echo "<td>" . ($sport['type_name'] ?: 'None') . "</td>";
        echo "<td>" . ($sport['category'] ?: 'None') . "</td>";
        echo "<td><span style='color: " . ($sport['sport_type_id'] ? 'green' : 'red') . ";'>$status</span></td>";
        echo "</tr>";
        
        if (!$sport['sport_type_id']) {
            $unassigned_sports[] = $sport;
        }
    }
    echo "</table>";
    
    // Step 2: Auto-assign sport types based on sport names
    if (!empty($unassigned_sports)) {
        echo "<h2>🔧 Step 2: Auto-Assigning Sport Types</h2>";
        
        // Get sport type IDs
        $result = $conn->query("SELECT id, name, category FROM sport_types");
        $sport_types_map = [];
        foreach ($result->fetchAll(PDO::FETCH_ASSOC) as $type) {
            $sport_types_map[$type['category']] = $type['id'];
        }
        
        // Auto-assignment rules based on sport names
        $assignment_rules = [
            'team' => ['basketball', 'volleyball', 'football', 'soccer', 'badminton', 'tennis', 'table tennis', 'chess'],
            'individual' => ['swimming', 'track', 'field', 'athletics', 'running', 'jumping', 'throwing'],
            'academic' => ['quiz', 'debate', 'math', 'science', 'spelling', 'knowledge'],
            'judged' => ['dance', 'singing', 'talent', 'performance', 'art', 'creative']
        ];
        
        foreach ($unassigned_sports as $sport) {
            $assigned_category = 'team'; // Default to team
            $sport_name_lower = strtolower($sport['name']);
            
            // Check assignment rules
            foreach ($assignment_rules as $category => $keywords) {
                foreach ($keywords as $keyword) {
                    if (strpos($sport_name_lower, $keyword) !== false) {
                        $assigned_category = $category;
                        break 2;
                    }
                }
            }
            
            // Update sport with assigned type
            $type_id = $sport_types_map[$assigned_category];
            $stmt = $conn->prepare("UPDATE sports SET sport_type_id = ? WHERE id = ?");
            $stmt->execute([$type_id, $sport['id']]);
            
            echo "<div class='info'>ℹ️ Assigned '{$sport['name']}' to category '$assigned_category' (Type ID: $type_id)</div>";
        }
        
        echo "<div class='success'>✅ Auto-assignment complete</div>";
    }
    
    // Step 3: Check tournament formats and their sport type mappings
    echo "<h2>🎯 Step 3: Tournament Format Mappings</h2>";
    
    $result = $conn->query("SELECT * FROM tournament_formats ORDER BY name");
    $formats = $result->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Available Tournament Formats:</h3>";
    echo "<table>";
    echo "<tr><th>ID</th><th>Name</th><th>Code</th><th>Sport Type Category</th><th>Min Participants</th></tr>";
    
    foreach ($formats as $format) {
        echo "<tr>";
        echo "<td>{$format['id']}</td>";
        echo "<td>{$format['name']}</td>";
        echo "<td>{$format['code']}</td>";
        echo "<td>" . ($format['sport_type_category'] ?? 'Not Set') . "</td>";
        echo "<td>{$format['min_participants']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Step 4: Create proper format detection function
    echo "<h2>⚙️ Step 4: Creating Format Detection Function</h2>";
    
    // Create a helper function file for format detection
    $format_detection_code = '<?php
/**
 * Tournament Format Detection Helper
 * Determines the appropriate tournament format based on sport type and category
 */

function getDefaultTournamentFormat($conn, $sportId) {
    try {
        // Get sport type category
        $stmt = $conn->prepare("
            SELECT st.category 
            FROM sports s 
            JOIN sport_types st ON s.sport_type_id = st.id 
            WHERE s.id = ?
        ");
        $stmt->execute([$sportId]);
        $result = $stmt->fetch();
        $category = $result ? $result[\'category\'] : \'team\';
        
        // Format preferences by category
        $format_preferences = [
            \'team\' => [\'single_elimination\', \'double_elimination\', \'round_robin\'],
            \'individual\' => [\'elimination_rounds\', \'single_elimination\', \'best_performance\'],
            \'academic\' => [\'swiss_system\', \'knockout_rounds\', \'round_robin\'],
            \'judged\' => [\'judged_rounds\', \'talent_showcase\', \'performance_competition\']
        ];
        
        $preferred_codes = $format_preferences[$category] ?? $format_preferences[\'team\'];
        
        // Find the first available format
        foreach ($preferred_codes as $code) {
            $stmt = $conn->prepare("SELECT id, name FROM tournament_formats WHERE code = ? LIMIT 1");
            $stmt->execute([$code]);
            $format = $stmt->fetch();
            if ($format) {
                return $format;
            }
        }
        
        // Fallback to any single elimination format
        $stmt = $conn->prepare("SELECT id, name FROM tournament_formats WHERE name LIKE \'%elimination%\' ORDER BY id LIMIT 1");
        $stmt->execute();
        $format = $stmt->fetch();
        
        return $format ?: [\'id\' => 1, \'name\' => \'Single Elimination\'];
        
    } catch (Exception $e) {
        // Return safe default
        return [\'id\' => 1, \'name\' => \'Single Elimination\'];
    }
}

function getTournamentFormatDisplay($conn, $eventSportId) {
    try {
        // Check if tournament already exists
        $stmt = $conn->prepare("
            SELECT ts.*, tf.name as format_name 
            FROM tournament_structures ts
            JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
            WHERE ts.event_sport_id = ?
            ORDER BY ts.created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$eventSportId]);
        $tournament = $stmt->fetch();
        
        if ($tournament) {
            return $tournament[\'format_name\'];
        }
        
        // Get sport ID from event_sport
        $stmt = $conn->prepare("SELECT sport_id FROM event_sports WHERE id = ?");
        $stmt->execute([$eventSportId]);
        $sportId = $stmt->fetchColumn();
        
        if ($sportId) {
            $format = getDefaultTournamentFormat($conn, $sportId);
            return $format[\'name\'];
        }
        
        return \'Single Elimination\';
        
    } catch (Exception $e) {
        return \'Single Elimination\';
    }
}
?>';
    
    file_put_contents(__DIR__ . '/../includes/tournament_format_helper.php', $format_detection_code);
    echo "<div class='success'>✅ Created tournament format detection helper</div>";
    
    // Step 5: Test the format detection
    echo "<h2>🧪 Step 5: Testing Format Detection</h2>";
    
    include __DIR__ . '/../includes/tournament_format_helper.php';
    
    // Test with current sports
    $result = $conn->query("
        SELECT es.id as event_sport_id, s.id as sport_id, s.name as sport_name, st.category
        FROM event_sports es
        JOIN sports s ON es.sport_id = s.id
        LEFT JOIN sport_types st ON s.sport_type_id = st.id
        LIMIT 5
    ");
    $test_sports = $result->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Format Detection Test Results:</h3>";
    echo "<table>";
    echo "<tr><th>Sport Name</th><th>Category</th><th>Detected Format</th><th>Display Format</th></tr>";
    
    foreach ($test_sports as $sport) {
        $default_format = getDefaultTournamentFormat($conn, $sport['sport_id']);
        $display_format = getTournamentFormatDisplay($conn, $sport['event_sport_id']);
        
        echo "<tr>";
        echo "<td>{$sport['sport_name']}</td>";
        echo "<td>" . ($sport['category'] ?: 'Unassigned') . "</td>";
        echo "<td>{$default_format['name']}</td>";
        echo "<td>$display_format</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>✅ Tournament Format Detection Fix Complete</h2>";
    echo "<div class='success'>";
    echo "<h3>Summary:</h3>";
    echo "<ul>";
    echo "<li>✅ Sport types properly categorized</li>";
    echo "<li>✅ Tournament formats mapped to sport categories</li>";
    echo "<li>✅ Format detection helper created</li>";
    echo "<li>✅ Dynamic format selection implemented</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<p><a href='manage-category.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Category Management</a></p>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</body></html>";
?>
