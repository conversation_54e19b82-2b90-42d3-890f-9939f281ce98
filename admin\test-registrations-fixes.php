<?php
/**
 * Test Registrations Fixes
 * Verify all registration-related fixes are working properly
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Registrations Fixes</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .test-pass { background: #d4edda; color: #155724; }
        .test-fail { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Test Registrations Fixes</h1>

    <div class="section info">
        <h2>1. Test getRegistrations Function</h2>
        <?php
        $test_results = [];
        
        try {
            // Get a test event_sport_id
            $stmt = $conn->prepare("SELECT id FROM event_sports LIMIT 1");
            $stmt->execute();
            $event_sport_id = $stmt->fetchColumn();
            
            if ($event_sport_id) {
                echo '<p><strong>Testing with event_sport_id:</strong> ' . $event_sport_id . '</p>';
                
                // Test the function
                $registrations = getRegistrations($conn, $event_sport_id);
                
                if (is_array($registrations)) {
                    $test_results[] = ['test' => 'getRegistrations returns array', 'result' => 'PASS'];
                    echo '<div class="test-result test-pass">✓ getRegistrations returns array</div>';
                    
                    if (!empty($registrations)) {
                        $first_reg = $registrations[0];
                        
                        // Test for created_at field
                        if (isset($first_reg['created_at'])) {
                            $test_results[] = ['test' => 'created_at field present', 'result' => 'PASS'];
                            echo '<div class="test-result test-pass">✓ created_at field present</div>';
                        } else {
                            $test_results[] = ['test' => 'created_at field present', 'result' => 'FAIL'];
                            echo '<div class="test-result test-fail">✗ created_at field missing</div>';
                        }
                        
                        // Test participants field
                        if (isset($first_reg['participants'])) {
                            $test_results[] = ['test' => 'participants field present', 'result' => 'PASS'];
                            echo '<div class="test-result test-pass">✓ participants field present</div>';
                            
                            $participants = json_decode($first_reg['participants'], true);
                            if (json_last_error() === JSON_ERROR_NONE) {
                                $test_results[] = ['test' => 'participants JSON valid', 'result' => 'PASS'];
                                echo '<div class="test-result test-pass">✓ participants JSON is valid</div>';
                            } else {
                                $test_results[] = ['test' => 'participants JSON valid', 'result' => 'FAIL'];
                                echo '<div class="test-result test-fail">✗ participants JSON is invalid</div>';
                            }
                        } else {
                            $test_results[] = ['test' => 'participants field present', 'result' => 'FAIL'];
                            echo '<div class="test-result test-fail">✗ participants field missing</div>';
                        }
                        
                        echo '<h4>Sample Registration Data:</h4>';
                        echo '<table>';
                        echo '<tr><th>Field</th><th>Value</th></tr>';
                        foreach ($first_reg as $key => $value) {
                            if (!is_numeric($key)) {
                                if ($key === 'participants') {
                                    $decoded = json_decode($value, true);
                                    if (json_last_error() === JSON_ERROR_NONE) {
                                        echo '<tr><td>' . htmlspecialchars($key) . '</td><td>JSON Array (' . count($decoded) . ' items)</td></tr>';
                                    } else {
                                        echo '<tr><td>' . htmlspecialchars($key) . '</td><td>Invalid JSON</td></tr>';
                                    }
                                } else {
                                    echo '<tr><td>' . htmlspecialchars($key) . '</td><td>' . htmlspecialchars($value ?? 'NULL') . '</td></tr>';
                                }
                            }
                        }
                        echo '</table>';
                        
                    } else {
                        echo '<div class="test-result test-pass">ℹ No registrations found (this is OK for testing)</div>';
                    }
                } else {
                    $test_results[] = ['test' => 'getRegistrations returns array', 'result' => 'FAIL'];
                    echo '<div class="test-result test-fail">✗ getRegistrations does not return array</div>';
                }
            } else {
                echo '<div class="test-result test-fail">✗ No event_sports found for testing</div>';
            }
            
        } catch (Exception $e) {
            $test_results[] = ['test' => 'getRegistrations function execution', 'result' => 'FAIL'];
            echo '<div class="test-result test-fail">✗ Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        ?>
    </div>

    <div class="section info">
        <h2>2. Test Date Formatting Functions</h2>
        <?php
        try {
            // Test formatDateTime with valid date
            $valid_date = '2024-01-15 14:30:00';
            $formatted = formatDateTime($valid_date);
            if ($formatted !== 'Not set') {
                $test_results[] = ['test' => 'formatDateTime with valid date', 'result' => 'PASS'];
                echo '<div class="test-result test-pass">✓ formatDateTime with valid date: ' . htmlspecialchars($formatted) . '</div>';
            } else {
                $test_results[] = ['test' => 'formatDateTime with valid date', 'result' => 'FAIL'];
                echo '<div class="test-result test-fail">✗ formatDateTime failed with valid date</div>';
            }
            
            // Test formatDateTime with null
            $null_formatted = formatDateTime(null);
            if ($null_formatted === 'Not set') {
                $test_results[] = ['test' => 'formatDateTime with null', 'result' => 'PASS'];
                echo '<div class="test-result test-pass">✓ formatDateTime with null: ' . htmlspecialchars($null_formatted) . '</div>';
            } else {
                $test_results[] = ['test' => 'formatDateTime with null', 'result' => 'FAIL'];
                echo '<div class="test-result test-fail">✗ formatDateTime with null returned: ' . htmlspecialchars($null_formatted) . '</div>';
            }
            
            // Test formatDateTime with empty string
            $empty_formatted = formatDateTime('');
            if ($empty_formatted === 'Not set') {
                $test_results[] = ['test' => 'formatDateTime with empty string', 'result' => 'PASS'];
                echo '<div class="test-result test-pass">✓ formatDateTime with empty string: ' . htmlspecialchars($empty_formatted) . '</div>';
            } else {
                $test_results[] = ['test' => 'formatDateTime with empty string', 'result' => 'FAIL'];
                echo '<div class="test-result test-fail">✗ formatDateTime with empty string returned: ' . htmlspecialchars($empty_formatted) . '</div>';
            }
            
        } catch (Exception $e) {
            $test_results[] = ['test' => 'Date formatting functions', 'result' => 'FAIL'];
            echo '<div class="test-result test-fail">✗ Error testing date functions: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        ?>
    </div>

    <div class="section info">
        <h2>3. Test Participants Data Handling</h2>
        <?php
        try {
            // Test different participant data formats
            $test_participants = [
                'string_array' => json_encode(['Player 1', 'Player 2', 'Player 3']),
                'object_array' => json_encode([
                    ['name' => 'Player 1', 'position' => 'Captain'],
                    ['name' => 'Player 2', 'position' => 'Member']
                ]),
                'mixed_array' => json_encode([
                    'Player 1',
                    ['name' => 'Player 2', 'position' => 'Captain']
                ]),
                'invalid_json' => 'not json',
                'empty_string' => '',
                'null_value' => null
            ];
            
            echo '<h4>Testing different participant data formats:</h4>';
            foreach ($test_participants as $format => $data) {
                echo '<h5>' . ucfirst(str_replace('_', ' ', $format)) . ':</h5>';
                
                if (!empty($data)) {
                    $participants = json_decode($data, true);
                    if (is_array($participants) && !empty($participants)) {
                        echo '<div class="test-result test-pass">✓ Valid format - would display ' . count($participants) . ' participants</div>';
                        foreach ($participants as $participant) {
                            if (is_array($participant)) {
                                $name = $participant['name'] ?? $participant['participant_name'] ?? 'Unknown';
                                $position = !empty($participant['position']) ? ' (' . $participant['position'] . ')' : '';
                                echo '<div style="margin-left: 20px;">- ' . htmlspecialchars($name . $position) . '</div>';
                            } else {
                                echo '<div style="margin-left: 20px;">- ' . htmlspecialchars($participant) . '</div>';
                            }
                        }
                    } else {
                        echo '<div class="test-result test-fail">✗ Invalid format - would not display</div>';
                    }
                } else {
                    echo '<div class="test-result test-pass">✓ Empty data - would not display (correct behavior)</div>';
                }
            }
            
        } catch (Exception $e) {
            echo '<div class="test-result test-fail">✗ Error testing participant data: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        ?>
    </div>

    <div class="section success">
        <h2>4. Test Summary</h2>
        <?php
        $total_tests = count($test_results);
        $passed_tests = count(array_filter($test_results, function($test) { return $test['result'] === 'PASS'; }));
        $failed_tests = $total_tests - $passed_tests;
        
        echo '<h4>Overall Results:</h4>';
        echo '<ul>';
        echo '<li><strong>Total Tests:</strong> ' . $total_tests . '</li>';
        echo '<li><strong>Passed:</strong> <span style="color: #155724;">' . $passed_tests . '</span></li>';
        echo '<li><strong>Failed:</strong> <span style="color: #721c24;">' . $failed_tests . '</span></li>';
        echo '</ul>';
        
        if ($failed_tests === 0) {
            echo '<div class="test-result test-pass"><strong>🎉 All tests passed! The registration fixes are working correctly.</strong></div>';
        } else {
            echo '<div class="test-result test-fail"><strong>⚠ Some tests failed. Please review the issues above.</strong></div>';
        }
        
        echo '<h4>Detailed Test Results:</h4>';
        echo '<table>';
        echo '<tr><th>Test</th><th>Result</th></tr>';
        foreach ($test_results as $test) {
            $class = $test['result'] === 'PASS' ? 'test-pass' : 'test-fail';
            echo '<tr class="' . $class . '">';
            echo '<td>' . htmlspecialchars($test['test']) . '</td>';
            echo '<td>' . htmlspecialchars($test['result']) . '</td>';
            echo '</tr>';
        }
        echo '</table>';
        ?>
    </div>

    <div class="section info">
        <h2>5. Next Steps</h2>
        <p>If all tests passed, you can now:</p>
        <ul>
            <li>✅ Visit the registrations page without PHP errors</li>
            <li>✅ See properly formatted registration dates</li>
            <li>✅ View participant lists correctly (both string and object formats)</li>
            <li>✅ Use the registration management features safely</li>
        </ul>
        
        <p><strong>Quick Links:</strong></p>
        <p>
            <a href="registrations.php?event_sport_id=18" target="_blank" style="background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;">Test Registrations Page</a>
            <a href="debug-registrations-schema.php" target="_blank" style="background: #6c757d; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;">Debug Schema</a>
        </p>
    </div>

</body>
</html>
