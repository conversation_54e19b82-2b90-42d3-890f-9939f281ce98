<?php
/**
 * Debug Event Sports Schema
 * Check the current structure of event_sports table
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Event Sports Schema</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Event Sports Schema Debug</h1>

    <div class="section info">
        <h2>1. Event Sports Table Structure</h2>
        <?php
        try {
            $stmt = $conn->prepare("DESCRIBE event_sports");
            $stmt->execute();
            $columns = $stmt->fetchAll();
            
            echo "<table>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } catch (Exception $e) {
            echo '<p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>

    <div class="section info">
        <h2>2. Sample Event Sports Data</h2>
        <?php
        try {
            $stmt = $conn->prepare("SELECT * FROM event_sports LIMIT 5");
            $stmt->execute();
            $data = $stmt->fetchAll();
            
            if (empty($data)) {
                echo "<p>No data found in event_sports table.</p>";
            } else {
                echo "<table>";
                $first = true;
                foreach ($data as $row) {
                    if ($first) {
                        echo "<tr>";
                        foreach (array_keys($row) as $key) {
                            if (!is_numeric($key)) {
                                echo "<th>" . htmlspecialchars($key) . "</th>";
                            }
                        }
                        echo "</tr>";
                        $first = false;
                    }
                    echo "<tr>";
                    foreach ($row as $key => $value) {
                        if (!is_numeric($key)) {
                            echo "<td>" . htmlspecialchars($value) . "</td>";
                        }
                    }
                    echo "</tr>";
                }
                echo "</table>";
            }
        } catch (Exception $e) {
            echo '<p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>

    <div class="section info">
        <h2>3. Tournament Formats Table Structure</h2>
        <?php
        try {
            $stmt = $conn->prepare("DESCRIBE tournament_formats");
            $stmt->execute();
            $columns = $stmt->fetchAll();
            
            echo "<table>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } catch (Exception $e) {
            echo '<p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>

    <div class="section info">
        <h2>4. Available Tournament Formats</h2>
        <?php
        try {
            $stmt = $conn->prepare("SELECT id, name, code, description FROM tournament_formats ORDER BY name");
            $stmt->execute();
            $formats = $stmt->fetchAll();
            
            if (empty($formats)) {
                echo "<p>No tournament formats found.</p>";
            } else {
                echo "<table>";
                echo "<tr><th>ID</th><th>Name</th><th>Code</th><th>Description</th></tr>";
                foreach ($formats as $format) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($format['id']) . "</td>";
                    echo "<td>" . htmlspecialchars($format['name']) . "</td>";
                    echo "<td>" . htmlspecialchars($format['code']) . "</td>";
                    echo "<td>" . htmlspecialchars($format['description']) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } catch (Exception $e) {
            echo '<p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>

    <div class="section info">
        <h2>5. Check for Missing tournament_format_id Column</h2>
        <?php
        try {
            $stmt = $conn->prepare("SHOW COLUMNS FROM event_sports LIKE 'tournament_format_id'");
            $stmt->execute();
            $column = $stmt->fetch();
            
            if ($column) {
                echo '<p class="success">✓ tournament_format_id column exists in event_sports table</p>';
                echo '<pre>' . print_r($column, true) . '</pre>';
            } else {
                echo '<p class="error">✗ tournament_format_id column is missing from event_sports table</p>';
                echo '<p>This explains why tournament format data is not being saved properly.</p>';
            }
        } catch (Exception $e) {
            echo '<p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>

    <div class="section info">
        <h2>6. Test Query for Category Page Data</h2>
        <?php
        try {
            // Get a sample event_sport_id for testing
            $stmt = $conn->prepare("SELECT id FROM event_sports LIMIT 1");
            $stmt->execute();
            $event_sport = $stmt->fetch();

            if ($event_sport) {
                $event_sport_id = $event_sport['id'];
                echo "<p>Testing with event_sport_id: {$event_sport_id}</p>";

                // Test the query used in manage-category.php
                $sql = "SELECT
                            sc.*,
                            es.event_id,
                            es.sport_id,
                            es.max_teams,
                            es.venue as event_venue,
                            es.status as event_sport_status,
                            e.name as event_name,
                            s.name as sport_name,
                            s.type as sport_type,
                            s.scoring_method,
                            s.bracket_format
                        FROM sport_categories sc
                        JOIN event_sports es ON sc.event_sport_id = es.id
                        JOIN events e ON es.event_id = e.id
                        JOIN sports s ON es.sport_id = s.id
                        WHERE sc.event_sport_id = ?";

                $stmt = $conn->prepare($sql);
                $stmt->execute([$event_sport_id]);
                $result = $stmt->fetch();

                if ($result) {
                    echo '<p class="success">✓ Query successful</p>';
                    echo '<pre>' . print_r($result, true) . '</pre>';
                } else {
                    echo '<p class="error">✗ No data found for this event_sport_id</p>';
                }
            } else {
                echo '<p class="error">No event_sports records found to test with</p>';
            }
        } catch (Exception $e) {
            echo '<p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>

    <div class="section info">
        <h2>7. Fix Database Schema</h2>
        <?php
        if (isset($_POST['fix_schema'])) {
            try {
                echo '<h4>Step-by-step schema fix:</h4>';

                // Step 1: Check if column exists
                $stmt = $conn->prepare("SHOW COLUMNS FROM event_sports LIKE 'tournament_format_id'");
                $stmt->execute();
                $column_exists = $stmt->fetch();

                if (!$column_exists) {
                    // Add tournament_format_id column to event_sports table
                    $stmt = $conn->prepare("ALTER TABLE event_sports ADD COLUMN tournament_format_id INT NULL AFTER sport_id");
                    $stmt->execute();
                    echo '<p class="success">✓ Added tournament_format_id column to event_sports table</p>';
                } else {
                    echo '<p class="info">ℹ tournament_format_id column already exists</p>';
                }

                // Step 2: Ensure we have at least one tournament format
                $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_formats");
                $stmt->execute();
                $format_count = $stmt->fetchColumn();

                if ($format_count == 0) {
                    // Create a default tournament format
                    $stmt = $conn->prepare("INSERT INTO tournament_formats (name, code, description) VALUES ('Single Elimination', 'single_elimination', 'Standard single elimination tournament')");
                    $stmt->execute();
                    echo '<p class="success">✓ Created default tournament format</p>';
                }

                // Step 3: Update existing records with valid tournament format
                $stmt = $conn->prepare("SELECT id FROM tournament_formats LIMIT 1");
                $stmt->execute();
                $default_format_id = $stmt->fetchColumn();

                if ($default_format_id) {
                    $stmt = $conn->prepare("UPDATE event_sports SET tournament_format_id = ? WHERE tournament_format_id IS NULL");
                    $stmt->execute([$default_format_id]);
                    $updated = $stmt->rowCount();
                    echo "<p class=\"success\">✓ Updated {$updated} existing records with tournament format ID {$default_format_id}</p>";
                }

                // Step 4: Check if foreign key constraint exists
                $stmt = $conn->prepare("
                    SELECT CONSTRAINT_NAME
                    FROM information_schema.KEY_COLUMN_USAGE
                    WHERE TABLE_SCHEMA = DATABASE()
                    AND TABLE_NAME = 'event_sports'
                    AND COLUMN_NAME = 'tournament_format_id'
                    AND REFERENCED_TABLE_NAME = 'tournament_formats'
                ");
                $stmt->execute();
                $fk_exists = $stmt->fetch();

                if (!$fk_exists) {
                    // Add foreign key constraint
                    $stmt = $conn->prepare("ALTER TABLE event_sports ADD CONSTRAINT fk_event_sports_tournament_format FOREIGN KEY (tournament_format_id) REFERENCES tournament_formats(id) ON DELETE SET NULL");
                    $stmt->execute();
                    echo '<p class="success">✓ Added foreign key constraint for tournament_format_id</p>';
                } else {
                    echo '<p class="info">ℹ Foreign key constraint already exists</p>';
                }

                echo '<p class="success"><strong>✓ Database schema fix completed successfully!</strong></p>';

            } catch (Exception $e) {
                echo '<p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
                echo '<p class="info">Attempting to continue with partial fix...</p>';
            }
        }
        ?>
        <form method="POST">
            <button type="submit" name="fix_schema" class="btn btn-primary" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
                Fix Database Schema
            </button>
        </form>
    </div>

    <div class="section info">
        <h2>8. Create Test Data (if needed)</h2>
        <?php
        if (isset($_POST['create_test_data'])) {
            try {
                echo '<h4>Creating test data:</h4>';

                // Check if we have tournament formats
                $stmt = $conn->prepare("SELECT COUNT(*) FROM tournament_formats");
                $stmt->execute();
                $format_count = $stmt->fetchColumn();

                if ($format_count == 0) {
                    // Create basic tournament formats
                    $formats = [
                        ['Single Elimination', 'single_elimination', 'Standard single elimination tournament'],
                        ['Double Elimination', 'double_elimination', 'Double elimination with winner and loser brackets'],
                        ['Round Robin', 'round_robin', 'All teams play against each other'],
                        ['Swiss System', 'swiss_system', 'Swiss system tournament format']
                    ];

                    foreach ($formats as $format) {
                        $stmt = $conn->prepare("INSERT INTO tournament_formats (name, code, description) VALUES (?, ?, ?)");
                        $stmt->execute($format);
                    }
                    echo '<p class="success">✓ Created ' . count($formats) . ' tournament formats</p>';
                } else {
                    echo '<p class="info">ℹ Tournament formats already exist (' . $format_count . ' found)</p>';
                }

                // Check if we have events
                $stmt = $conn->prepare("SELECT COUNT(*) FROM events");
                $stmt->execute();
                $event_count = $stmt->fetchColumn();

                if ($event_count == 0) {
                    // Create a test event
                    $stmt = $conn->prepare("INSERT INTO events (name, description, start_date, end_date, status) VALUES ('Test Event', 'Test event for category management', NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY), 'upcoming')");
                    $stmt->execute();
                    echo '<p class="success">✓ Created test event</p>';
                }

                // Check if we have sports
                $stmt = $conn->prepare("SELECT COUNT(*) FROM sports");
                $stmt->execute();
                $sport_count = $stmt->fetchColumn();

                if ($sport_count == 0) {
                    // Create a test sport
                    $stmt = $conn->prepare("INSERT INTO sports (name, type, scoring_method, bracket_format) VALUES ('Test Sport', 'traditional', 'point_based', 'single_elimination')");
                    $stmt->execute();
                    echo '<p class="success">✓ Created test sport</p>';
                }

                // Check if we have event_sports
                $stmt = $conn->prepare("SELECT COUNT(*) FROM event_sports");
                $stmt->execute();
                $event_sport_count = $stmt->fetchColumn();

                if ($event_sport_count == 0) {
                    // Create test event_sport
                    $stmt = $conn->prepare("SELECT id FROM events LIMIT 1");
                    $stmt->execute();
                    $event_id = $stmt->fetchColumn();

                    $stmt = $conn->prepare("SELECT id FROM sports LIMIT 1");
                    $stmt->execute();
                    $sport_id = $stmt->fetchColumn();

                    $stmt = $conn->prepare("SELECT id FROM tournament_formats LIMIT 1");
                    $stmt->execute();
                    $format_id = $stmt->fetchColumn();

                    if ($event_id && $sport_id && $format_id) {
                        $stmt = $conn->prepare("INSERT INTO event_sports (event_id, sport_id, tournament_format_id, max_teams, status) VALUES (?, ?, ?, 8, 'registration')");
                        $stmt->execute([$event_id, $sport_id, $format_id]);
                        echo '<p class="success">✓ Created test event_sport with tournament format</p>';
                    }
                }

                // Check if we have sport_categories
                $stmt = $conn->prepare("SELECT COUNT(*) FROM sport_categories");
                $stmt->execute();
                $category_count = $stmt->fetchColumn();

                if ($category_count == 0) {
                    // Create test sport category
                    $stmt = $conn->prepare("SELECT id FROM event_sports LIMIT 1");
                    $stmt->execute();
                    $event_sport_id = $stmt->fetchColumn();

                    if ($event_sport_id) {
                        $stmt = $conn->prepare("INSERT INTO sport_categories (event_sport_id, category_name, category_type, max_participants, status) VALUES (?, 'Test Category', 'open', 16, 'active')");
                        $stmt->execute([$event_sport_id]);
                        echo '<p class="success">✓ Created test sport category</p>';
                    }
                }

                echo '<p class="success"><strong>✓ Test data creation completed!</strong></p>';

            } catch (Exception $e) {
                echo '<p class="error">Error creating test data: ' . htmlspecialchars($e->getMessage()) . '</p>';
            }
        }
        ?>
        <form method="POST">
            <button type="submit" name="create_test_data" class="btn btn-success" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
                Create Test Data
            </button>
        </form>
    </div>

</body>
</html>
