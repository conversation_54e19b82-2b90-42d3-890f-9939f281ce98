<?php
/**
 * End-to-End Tournament System Test
 * Comprehensive testing of all tournament functionality
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/tournament_manager.php';

// Require admin authentication
requireAdmin();

$database = new Database();
$conn = $database->getConnection();

$test_results = [];
$overall_success = true;

// Test 1: Database Schema Verification
function testDatabaseSchema($conn) {
    global $test_results, $overall_success;
    
    $required_tables = [
        'tournament_formats',
        'tournament_structures', 
        'tournament_rounds',
        'tournament_participants',
        'sport_types'
    ];
    
    $missing_tables = [];
    foreach ($required_tables as $table) {
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if (!$stmt->fetch()) {
            $missing_tables[] = $table;
        }
    }
    
    $success = empty($missing_tables);
    $test_results['database_schema'] = [
        'success' => $success,
        'message' => $success ? 'All required tables exist' : 'Missing tables: ' . implode(', ', $missing_tables),
        'details' => $missing_tables
    ];
    
    if (!$success) $overall_success = false;
    return $success;
}

// Test 2: Tournament Manager Class
function testTournamentManager($conn) {
    global $test_results, $overall_success;
    
    try {
        $manager = new TournamentManager($conn);
        
        // Test getting available formats
        $formats = $manager->getAvailableFormats('team');
        $formats_count = count($formats);
        
        $success = $formats_count > 0;
        $test_results['tournament_manager'] = [
            'success' => $success,
            'message' => $success ? "TournamentManager working, found {$formats_count} formats" : 'No tournament formats available',
            'details' => ['formats_count' => $formats_count]
        ];
        
        if (!$success) $overall_success = false;
        return $success;
        
    } catch (Exception $e) {
        $test_results['tournament_manager'] = [
            'success' => false,
            'message' => 'TournamentManager error: ' . $e->getMessage(),
            'details' => ['error' => $e->getMessage()]
        ];
        $overall_success = false;
        return false;
    }
}

// Test 3: Tournament Format Data
function testTournamentFormats($conn) {
    global $test_results, $overall_success;
    
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM tournament_formats");
        $result = $stmt->fetch();
        $count = $result['count'];
        
        // Check for specific format types
        $stmt = $conn->query("SELECT sport_type_category, COUNT(*) as count FROM tournament_formats GROUP BY sport_type_category");
        $categories = $stmt->fetchAll();
        
        $success = $count >= 5; // Should have at least 5 tournament formats
        $test_results['tournament_formats'] = [
            'success' => $success,
            'message' => $success ? "Found {$count} tournament formats" : "Insufficient tournament formats ({$count})",
            'details' => ['total_count' => $count, 'categories' => $categories]
        ];
        
        if (!$success) $overall_success = false;
        return $success;
        
    } catch (Exception $e) {
        $test_results['tournament_formats'] = [
            'success' => false,
            'message' => 'Error checking tournament formats: ' . $e->getMessage(),
            'details' => ['error' => $e->getMessage()]
        ];
        $overall_success = false;
        return false;
    }
}

// Test 4: AJAX Endpoints
function testAjaxEndpoints() {
    global $test_results, $overall_success;
    
    $endpoints = [
        'ajax/tournament-management.php',
        'admin/ajax/tournament-management.php'
    ];
    
    $working_endpoints = [];
    $missing_endpoints = [];
    
    foreach ($endpoints as $endpoint) {
        if (file_exists($endpoint)) {
            $working_endpoints[] = $endpoint;
        } else {
            $missing_endpoints[] = $endpoint;
        }
    }
    
    $success = !empty($working_endpoints);
    $test_results['ajax_endpoints'] = [
        'success' => $success,
        'message' => $success ? 'AJAX endpoints available' : 'No AJAX endpoints found',
        'details' => ['working' => $working_endpoints, 'missing' => $missing_endpoints]
    ];
    
    if (!$success) $overall_success = false;
    return $success;
}

// Test 5: Tournament Management Page
function testTournamentPage() {
    global $test_results, $overall_success;
    
    $page_path = 'manage-category.php';
    $exists = file_exists($page_path);
    
    if ($exists) {
        $content = file_get_contents($page_path);
        $size = strlen($content);
        
        // Check for key components
        $components = [
            'tournament-section' => 'Tournament section',
            'createTournament' => 'Tournament creation function',
            'switchTab' => 'Tab switching functionality',
            'modal' => 'Modal dialogs'
        ];
        
        $found_components = [];
        foreach ($components as $component => $description) {
            if (strpos($content, $component) !== false) {
                $found_components[] = $description;
            }
        }
        
        $success = $size > 10000 && count($found_components) >= 3; // Should be substantial page with key components
        $test_results['tournament_page'] = [
            'success' => $success,
            'message' => $success ? "Tournament page ready ({$size} bytes)" : 'Tournament page incomplete',
            'details' => ['size' => $size, 'components' => $found_components]
        ];
    } else {
        $success = false;
        $test_results['tournament_page'] = [
            'success' => false,
            'message' => 'Tournament management page not found',
            'details' => ['path' => $page_path]
        ];
    }
    
    if (!$success) $overall_success = false;
    return $success;
}

// Test 6: Sample Data Availability
function testSampleData($conn) {
    global $test_results, $overall_success;
    
    try {
        // Check for events
        $stmt = $conn->query("SELECT COUNT(*) as count FROM events WHERE status != 'cancelled'");
        $events_count = $stmt->fetch()['count'];
        
        // Check for sports
        $stmt = $conn->query("SELECT COUNT(*) as count FROM sports");
        $sports_count = $stmt->fetch()['count'];
        
        // Check for departments
        $stmt = $conn->query("SELECT COUNT(*) as count FROM departments");
        $departments_count = $stmt->fetch()['count'];
        
        // Check for event_sports
        $stmt = $conn->query("SELECT COUNT(*) as count FROM event_sports");
        $event_sports_count = $stmt->fetch()['count'];
        
        $success = $events_count > 0 && $sports_count > 0 && $departments_count > 0;
        $test_results['sample_data'] = [
            'success' => $success,
            'message' => $success ? 'Sample data available for testing' : 'Insufficient sample data',
            'details' => [
                'events' => $events_count,
                'sports' => $sports_count, 
                'departments' => $departments_count,
                'event_sports' => $event_sports_count
            ]
        ];
        
        if (!$success) $overall_success = false;
        return $success;
        
    } catch (Exception $e) {
        $test_results['sample_data'] = [
            'success' => false,
            'message' => 'Error checking sample data: ' . $e->getMessage(),
            'details' => ['error' => $e->getMessage()]
        ];
        $overall_success = false;
        return false;
    }
}

// Run all tests
testDatabaseSchema($conn);
testTournamentManager($conn);
testTournamentFormats($conn);
testAjaxEndpoints();
testTournamentPage();
testSampleData($conn);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tournament System End-to-End Test | SC_IMS Admin</title>
    <?php include 'includes/admin-styles.php'; ?>
    <style>
        .test-result {
            margin: 1rem 0;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .test-success {
            background: #f0fdf4;
            border-color: #22c55e;
            color: #166534;
        }
        .test-failure {
            background: #fef2f2;
            border-color: #ef4444;
            color: #991b1b;
        }
        .test-details {
            margin-top: 0.5rem;
            font-size: 0.9rem;
            opacity: 0.8;
        }
        .overall-status {
            padding: 2rem;
            text-align: center;
            border-radius: 12px;
            margin: 2rem 0;
        }
        .status-success {
            background: linear-gradient(135deg, #22c55e, #16a34a);
            color: white;
        }
        .status-failure {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="admin-content">
            <div class="page-header">
                <h1><i class="fas fa-check-circle"></i> Tournament System End-to-End Test</h1>
                <p>Comprehensive verification of tournament management functionality</p>
            </div>
            
            <!-- Overall Status -->
            <div class="overall-status <?php echo $overall_success ? 'status-success' : 'status-failure'; ?>">
                <div style="font-size: 3rem; margin-bottom: 1rem;">
                    <?php echo $overall_success ? '✅' : '❌'; ?>
                </div>
                <h2><?php echo $overall_success ? 'All Tests Passed!' : 'Some Tests Failed'; ?></h2>
                <p><?php echo $overall_success ? 'Tournament system is ready for use' : 'Please address the failing tests below'; ?></p>
            </div>
            
            <!-- Test Results -->
            <div class="card">
                <div class="card-header">
                    <h3>Test Results</h3>
                </div>
                <div class="card-body">
                    <?php foreach ($test_results as $test_name => $result): ?>
                        <div class="test-result <?php echo $result['success'] ? 'test-success' : 'test-failure'; ?>">
                            <h4>
                                <?php echo $result['success'] ? '✅' : '❌'; ?>
                                <?php echo ucwords(str_replace('_', ' ', $test_name)); ?>
                            </h4>
                            <p><?php echo htmlspecialchars($result['message']); ?></p>
                            <?php if (!empty($result['details'])): ?>
                                <div class="test-details">
                                    <strong>Details:</strong>
                                    <pre><?php echo htmlspecialchars(json_encode($result['details'], JSON_PRETTY_PRINT)); ?></pre>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- Next Steps -->
            <div class="card">
                <div class="card-header">
                    <h3>Next Steps</h3>
                </div>
                <div class="card-body">
                    <?php if ($overall_success): ?>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                            <a href="test-manage-category.php" class="btn btn-primary">
                                <i class="fas fa-play"></i> Test Tournament Page
                            </a>
                            <a href="events.php" class="btn btn-secondary">
                                <i class="fas fa-calendar"></i> Manage Events
                            </a>
                            <a href="sports.php" class="btn btn-info">
                                <i class="fas fa-futbol"></i> Manage Sports
                            </a>
                            <a href="index.php" class="btn btn-success">
                                <i class="fas fa-home"></i> Go to Dashboard
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <h4>⚠️ Action Required</h4>
                            <p>Some tests failed. Please address the issues above before using the tournament system.</p>
                            <div style="margin-top: 1rem;">
                                <a href="tournament-setup.php" class="btn btn-warning">
                                    <i class="fas fa-cog"></i> Run Tournament Setup
                                </a>
                                <a href="test-tournament-system.php" class="btn btn-info">
                                    <i class="fas fa-info-circle"></i> System Status
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
