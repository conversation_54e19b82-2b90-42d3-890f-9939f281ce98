<?php
/**
 * Definitive Database Fix
 * SC_IMS Sports Competition and Event Management System
 * 
 * This script addresses the actual root cause of the tournament_structure_id error
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Definitive Database Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .success { color: #28a745; background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .step { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Definitive Database Fix</h1>
        <p><strong>Fix Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <div class="warning">
            <h3>🎯 Root Cause Analysis</h3>
            <p>The tournament_structure_id error occurs because:</p>
            <ul>
                <li>The matches table is missing required tournament columns</li>
                <li>The tournament_manager.php createMatch method expects these columns to exist</li>
                <li>Previous fixes may not have been applied to the actual database</li>
            </ul>
        </div>
        
        <?php
        $overall_success = true;
        $changes_made = [];
        $errors = [];
        
        try {
            // Disable foreign key checks temporarily
            $conn->exec("SET FOREIGN_KEY_CHECKS = 0");
            
            // Step 1: Check current matches table structure
            echo '<div class="step">';
            echo '<h2>Step 1: Current Matches Table Analysis</h2>';
            
            $stmt = $conn->prepare("DESCRIBE matches");
            $stmt->execute();
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo '<table>';
            echo '<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>';
            $existing_columns = [];
            foreach ($columns as $column) {
                echo '<tr>';
                echo '<td>' . $column['Field'] . '</td>';
                echo '<td>' . $column['Type'] . '</td>';
                echo '<td>' . $column['Null'] . '</td>';
                echo '<td>' . $column['Key'] . '</td>';
                echo '<td>' . $column['Default'] . '</td>';
                echo '</tr>';
                $existing_columns[] = $column['Field'];
            }
            echo '</table>';
            
            echo '</div>';
            
            // Step 2: Add missing columns to matches table
            echo '<div class="step">';
            echo '<h2>Step 2: Add Missing Tournament Columns</h2>';
            
            $required_columns = [
                'tournament_structure_id' => 'INT NULL',
                'tournament_round_id' => 'INT NULL',
                'bracket_position' => 'VARCHAR(50) NULL',
                'is_bye_match' => 'BOOLEAN DEFAULT FALSE'
            ];
            
            foreach ($required_columns as $column => $definition) {
                if (!in_array($column, $existing_columns)) {
                    try {
                        $sql = "ALTER TABLE matches ADD COLUMN $column $definition";
                        $conn->exec($sql);
                        echo '<p style="color: green;">✅ Added column: ' . $column . '</p>';
                        $changes_made[] = "Added $column column to matches table";
                    } catch (Exception $e) {
                        echo '<p style="color: red;">❌ Failed to add column ' . $column . ': ' . htmlspecialchars($e->getMessage()) . '</p>';
                        $errors[] = "Failed to add column $column: " . $e->getMessage();
                        $overall_success = false;
                    }
                } else {
                    echo '<p style="color: blue;">✓ Column ' . $column . ' already exists</p>';
                }
            }
            
            echo '</div>';
            
            // Step 3: Ensure tournament tables exist
            echo '<div class="step">';
            echo '<h2>Step 3: Ensure Tournament Tables Exist</h2>';
            
            // Check tournament_formats table
            $stmt = $conn->prepare("SHOW TABLES LIKE 'tournament_formats'");
            $stmt->execute();
            if (!$stmt->fetch()) {
                $sql = "CREATE TABLE tournament_formats (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    code VARCHAR(50) NOT NULL UNIQUE,
                    description TEXT,
                    sport_types VARCHAR(255) DEFAULT 'team,individual',
                    min_participants INT DEFAULT 2,
                    max_participants INT NULL,
                    algorithm_class VARCHAR(100) DEFAULT 'SingleEliminationAlgorithm',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )";
                $conn->exec($sql);
                echo '<p style="color: green;">✅ Created tournament_formats table</p>';
                $changes_made[] = "Created tournament_formats table";
            } else {
                echo '<p style="color: blue;">✓ tournament_formats table already exists</p>';
            }
            
            // Check tournament_structures table
            $stmt = $conn->prepare("SHOW TABLES LIKE 'tournament_structures'");
            $stmt->execute();
            if (!$stmt->fetch()) {
                $sql = "CREATE TABLE tournament_structures (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    event_sport_id INT NOT NULL,
                    tournament_format_id INT NOT NULL,
                    name VARCHAR(255) NOT NULL,
                    status ENUM('setup', 'registration', 'seeding', 'in_progress', 'completed', 'cancelled') DEFAULT 'setup',
                    participant_count INT DEFAULT 0,
                    total_rounds INT DEFAULT 0,
                    current_round INT DEFAULT 0,
                    seeding_method ENUM('random', 'ranking', 'manual', 'hybrid') DEFAULT 'random',
                    bracket_data JSON,
                    advancement_rules JSON,
                    scoring_config JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )";
                $conn->exec($sql);
                echo '<p style="color: green;">✅ Created tournament_structures table</p>';
                $changes_made[] = "Created tournament_structures table";
            } else {
                echo '<p style="color: blue;">✓ tournament_structures table already exists</p>';
            }
            
            // Check tournament_rounds table
            $stmt = $conn->prepare("SHOW TABLES LIKE 'tournament_rounds'");
            $stmt->execute();
            if (!$stmt->fetch()) {
                $sql = "CREATE TABLE tournament_rounds (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    tournament_structure_id INT NOT NULL,
                    round_number INT NOT NULL,
                    round_name VARCHAR(100) NOT NULL,
                    round_type ENUM('group', 'elimination', 'final', 'consolation') DEFAULT 'elimination',
                    status ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending',
                    start_date DATETIME,
                    end_date DATETIME,
                    matches_count INT DEFAULT 0,
                    completed_matches INT DEFAULT 0,
                    advancement_criteria JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )";
                $conn->exec($sql);
                echo '<p style="color: green;">✅ Created tournament_rounds table</p>';
                $changes_made[] = "Created tournament_rounds table";
            } else {
                echo '<p style="color: blue;">✓ tournament_rounds table already exists</p>';
            }
            
            echo '</div>';
            
            // Step 4: Insert default tournament formats
            echo '<div class="step">';
            echo '<h2>Step 4: Insert Default Tournament Formats</h2>';
            
            $default_formats = [
                ['Single Elimination', 'single_elimination', 'Traditional knockout tournament', 'team,individual', 2, null, 'SingleEliminationAlgorithm'],
                ['Double Elimination', 'double_elimination', 'Two-bracket system', 'team,individual', 3, null, 'DoubleEliminationAlgorithm'],
                ['Round Robin', 'round_robin', 'Every participant plays every other', 'team,individual', 3, 16, 'RoundRobinAlgorithm'],
                ['Swiss System', 'swiss_system', 'Pairing system for academic competitions', 'academic', 4, null, 'SwissSystemAlgorithm'],
                ['Judged Rounds', 'judged_rounds', 'Multiple judged rounds', 'judged,performance', 3, null, 'JudgedRoundsAlgorithm']
            ];
            
            foreach ($default_formats as $format) {
                try {
                    $stmt = $conn->prepare("
                        INSERT IGNORE INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants, algorithm_class)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute($format);
                    if ($stmt->rowCount() > 0) {
                        echo '<p style="color: green;">✅ Inserted format: ' . $format[0] . '</p>';
                        $changes_made[] = "Inserted tournament format: " . $format[0];
                    } else {
                        echo '<p style="color: blue;">✓ Format already exists: ' . $format[0] . '</p>';
                    }
                } catch (Exception $e) {
                    echo '<p style="color: red;">❌ Failed to insert format ' . $format[0] . ': ' . htmlspecialchars($e->getMessage()) . '</p>';
                    $errors[] = "Failed to insert format " . $format[0] . ": " . $e->getMessage();
                }
            }
            
            echo '</div>';
            
            // Re-enable foreign key checks
            $conn->exec("SET FOREIGN_KEY_CHECKS = 1");
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Fix error: ' . htmlspecialchars($e->getMessage()) . '</div>';
            $errors[] = $e->getMessage();
            $overall_success = false;
        }
        ?>
        
        <!-- Results Summary -->
        <div class="step">
            <h2>📊 Fix Results</h2>
            
            <?php if ($overall_success): ?>
                <div class="success">
                    <h3>🎉 DATABASE FIX SUCCESSFUL!</h3>
                    <p><strong>✅ All required columns have been added to matches table</strong></p>
                    <p><strong>✅ All tournament tables have been created</strong></p>
                    <p><strong>✅ Default tournament formats have been inserted</strong></p>
                </div>
            <?php else: ?>
                <div class="error">
                    <h3>❌ Some Issues Occurred</h3>
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($changes_made)): ?>
                <div class="info">
                    <h3>✅ Changes Applied:</h3>
                    <ul>
                        <?php foreach ($changes_made as $change): ?>
                            <li><?php echo htmlspecialchars($change); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Test the Fix -->
        <div class="step">
            <h2>🧪 Test the Fix</h2>
            <p><a href="check-database-state.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Test Database State</a></p>
            <p><a href="test-tournament-creation.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Test Tournament Creation</a></p>
        </div>
    </div>
</body>
</html>
