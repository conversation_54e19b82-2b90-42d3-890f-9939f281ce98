<?php
/**
 * Comprehensive Tournament Database Schema Fix
 * SC_IMS Sports Competition and Event Management System
 */

require_once __DIR__ . '/../config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "<!DOCTYPE html>";
echo "<html><head><title>Tournament Database Schema Fix</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .warning { background: #fff3cd; color: #856404; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .info { background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 5px; margin: 10px 0; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
</style></head><body>";

echo "<h1>🔧 Tournament Database Schema Fix</h1>";

try {
    // Step 1: Check current matches table structure
    echo "<h2>📋 Step 1: Current Database State</h2>";
    
    $result = $conn->query("DESCRIBE matches");
    $columns = $result->fetchAll(PDO::FETCH_ASSOC);
    $column_names = array_column($columns, 'Field');
    
    echo "<h3>Current Matches Table Columns:</h3>";
    echo "<table><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr><td>{$column['Field']}</td><td>{$column['Type']}</td><td>{$column['Null']}</td><td>{$column['Key']}</td><td>{$column['Default']}</td></tr>";
    }
    echo "</table>";
    
    // Check for required columns
    $required_columns = ['tournament_structure_id', 'tournament_round_id', 'bracket_position', 'is_bye_match'];
    $missing_columns = [];
    
    foreach ($required_columns as $col) {
        if (!in_array($col, $column_names)) {
            $missing_columns[] = $col;
        }
    }
    
    if (empty($missing_columns)) {
        echo "<div class='success'>✅ All required tournament columns exist in matches table</div>";
    } else {
        echo "<div class='error'>❌ Missing columns: " . implode(', ', $missing_columns) . "</div>";
    }
    
    // Step 2: Check related tables
    echo "<h2>🔍 Step 2: Related Tables Check</h2>";
    
    $tables_to_check = [
        'tournament_structures' => 'Tournament structures table',
        'tournament_rounds' => 'Tournament rounds table', 
        'tournament_formats' => 'Tournament formats table',
        'tournament_participants' => 'Tournament participants table'
    ];
    
    $missing_tables = [];
    foreach ($tables_to_check as $table => $description) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->rowCount() > 0) {
            $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
            $count = $count_result->fetch(PDO::FETCH_ASSOC)['count'];
            echo "<div class='success'>✅ $description exists ($count records)</div>";
        } else {
            echo "<div class='error'>❌ $description missing</div>";
            $missing_tables[] = $table;
        }
    }
    
    // Step 3: Fix missing columns
    if (!empty($missing_columns)) {
        echo "<h2>🔧 Step 3: Adding Missing Columns</h2>";
        
        foreach ($missing_columns as $column) {
            echo "<p>Adding column: $column</p>";
            
            switch ($column) {
                case 'tournament_structure_id':
                    $conn->exec("ALTER TABLE matches ADD COLUMN tournament_structure_id INT NULL");
                    echo "<div class='success'>✅ Added tournament_structure_id column</div>";
                    break;
                    
                case 'tournament_round_id':
                    $conn->exec("ALTER TABLE matches ADD COLUMN tournament_round_id INT NULL");
                    echo "<div class='success'>✅ Added tournament_round_id column</div>";
                    break;
                    
                case 'bracket_position':
                    $conn->exec("ALTER TABLE matches ADD COLUMN bracket_position VARCHAR(50) NULL");
                    echo "<div class='success'>✅ Added bracket_position column</div>";
                    break;
                    
                case 'is_bye_match':
                    $conn->exec("ALTER TABLE matches ADD COLUMN is_bye_match BOOLEAN DEFAULT FALSE");
                    echo "<div class='success'>✅ Added is_bye_match column</div>";
                    break;
            }
        }
    }
    
    // Step 4: Create missing tables
    if (!empty($missing_tables)) {
        echo "<h2>🏗️ Step 4: Creating Missing Tables</h2>";
        
        foreach ($missing_tables as $table) {
            echo "<p>Creating table: $table</p>";
            
            switch ($table) {
                case 'tournament_structures':
                    $sql = "CREATE TABLE tournament_structures (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        event_sport_id INT NOT NULL,
                        tournament_format_id INT NOT NULL,
                        name VARCHAR(255) NOT NULL,
                        status ENUM('setup', 'registration', 'seeding', 'in_progress', 'completed', 'cancelled') DEFAULT 'setup',
                        participant_count INT DEFAULT 0,
                        total_rounds INT DEFAULT 0,
                        current_round INT DEFAULT 0,
                        seeding_method ENUM('random', 'ranking', 'manual', 'hybrid') DEFAULT 'random',
                        bracket_data JSON,
                        advancement_rules JSON,
                        scoring_config JSON,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
                        FOREIGN KEY (tournament_format_id) REFERENCES tournament_formats(id) ON DELETE CASCADE
                    )";
                    $conn->exec($sql);
                    echo "<div class='success'>✅ Created tournament_structures table</div>";
                    break;
                    
                case 'tournament_rounds':
                    $sql = "CREATE TABLE tournament_rounds (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        tournament_structure_id INT NOT NULL,
                        round_number INT NOT NULL,
                        round_name VARCHAR(100) NOT NULL,
                        round_type ENUM('group', 'elimination', 'final', 'consolation') DEFAULT 'elimination',
                        status ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending',
                        start_date DATETIME,
                        end_date DATETIME,
                        matches_count INT DEFAULT 0,
                        completed_matches INT DEFAULT 0,
                        advancement_criteria JSON,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE,
                        UNIQUE KEY unique_tournament_round (tournament_structure_id, round_number)
                    )";
                    $conn->exec($sql);
                    echo "<div class='success'>✅ Created tournament_rounds table</div>";
                    break;
                    
                case 'tournament_participants':
                    $sql = "CREATE TABLE tournament_participants (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        tournament_structure_id INT NOT NULL,
                        registration_id INT NOT NULL,
                        seed_number INT,
                        group_assignment VARCHAR(10),
                        current_status ENUM('active', 'eliminated', 'bye', 'withdrawn') DEFAULT 'active',
                        points DECIMAL(10,2) DEFAULT 0,
                        wins INT DEFAULT 0,
                        losses INT DEFAULT 0,
                        draws INT DEFAULT 0,
                        performance_data JSON,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE,
                        UNIQUE KEY unique_tournament_participant (tournament_structure_id, registration_id)
                    )";
                    $conn->exec($sql);
                    echo "<div class='success'>✅ Created tournament_participants table</div>";
                    break;
            }
        }
    }
    
    // Step 5: Test tournament creation
    echo "<h2>🧪 Step 5: Test Tournament Creation</h2>";
    
    // Test the exact SQL that was failing
    $test_sql = "INSERT INTO matches
                (event_sport_id, tournament_structure_id, tournament_round_id, team1_id, team2_id,
                 round_number, bracket_position, is_bye_match, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    try {
        $stmt = $conn->prepare($test_sql);
        echo "<div class='success'>✅ Tournament match INSERT statement prepared successfully</div>";
        
        // Test with dummy data (don't execute)
        echo "<div class='info'>ℹ️ SQL statement syntax is valid</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ SQL preparation failed: " . $e->getMessage() . "</div>";
    }
    
    echo "<h2>✅ Database Schema Fix Complete</h2>";
    echo "<div class='success'>";
    echo "<h3>Summary:</h3>";
    echo "<ul>";
    echo "<li>✅ All required tournament columns are now present in matches table</li>";
    echo "<li>✅ All required tournament tables exist</li>";
    echo "<li>✅ Tournament creation SQL statements are valid</li>";
    echo "<li>✅ Database is ready for tournament management</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<p><a href='manage-category.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Return to Category Management</a></p>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</body></html>";
?>
