<?php
/**
 * Test Tournament Creation Flow
 * Tests the complete tournament creation process
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/tournament_manager.php';

// Require admin authentication
requireAdmin();

$database = new Database();
$conn = $database->getConnection();

$test_results = [];
$created_tournament_id = null;

// Test tournament creation process
if ($_POST['action'] ?? '' === 'test_create_tournament') {
    try {
        // Get test data
        $stmt = $conn->query("SELECT es.id as event_sport_id FROM event_sports es LIMIT 1");
        $event_sport = $stmt->fetch();
        
        if (!$event_sport) {
            throw new Exception('No event sports found for testing');
        }
        
        $event_sport_id = $event_sport['event_sport_id'];
        
        // Get a tournament format
        $stmt = $conn->query("SELECT id FROM tournament_formats WHERE sport_type_category = 'team' LIMIT 1");
        $format = $stmt->fetch();
        
        if (!$format) {
            throw new Exception('No tournament formats found');
        }
        
        $format_id = $format['id'];
        
        // Create test registrations if none exist
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM registrations WHERE event_sport_id = ?");
        $stmt->execute([$event_sport_id]);
        $reg_count = $stmt->fetch()['count'];
        
        if ($reg_count < 2) {
            // Create test registrations
            $stmt = $conn->query("SELECT id FROM departments LIMIT 4");
            $departments = $stmt->fetchAll();
            
            foreach ($departments as $dept) {
                $stmt = $conn->prepare("INSERT IGNORE INTO registrations (event_sport_id, department_id, status, participants) VALUES (?, ?, 'confirmed', ?)");
                $participants = json_encode([['name' => 'Test Player 1'], ['name' => 'Test Player 2']]);
                $stmt->execute([$event_sport_id, $dept['id'], $participants]);
            }
        }
        
        // Create tournament using TournamentManager
        $tournamentManager = new TournamentManager($conn);
        
        $config = [
            'seeding_method' => 'random',
            'scoring_config' => [
                'points_win' => 3,
                'points_draw' => 1,
                'points_loss' => 0
            ]
        ];
        
        $tournament_name = 'Test Tournament ' . date('Y-m-d H:i:s');
        $created_tournament_id = $tournamentManager->createTournament($event_sport_id, $format_id, $tournament_name, $config);
        
        $test_results['tournament_creation'] = [
            'success' => true,
            'message' => "Tournament created successfully with ID: {$created_tournament_id}",
            'tournament_id' => $created_tournament_id
        ];
        
        // Test bracket generation
        $stmt = $conn->prepare("SELECT bracket_data FROM tournament_structures WHERE id = ?");
        $stmt->execute([$created_tournament_id]);
        $tournament = $stmt->fetch();
        
        if ($tournament && $tournament['bracket_data']) {
            $bracket_data = json_decode($tournament['bracket_data'], true);
            $test_results['bracket_generation'] = [
                'success' => true,
                'message' => 'Bracket generated successfully',
                'participants_count' => count($bracket_data['participants'] ?? [])
            ];
        } else {
            $test_results['bracket_generation'] = [
                'success' => false,
                'message' => 'Bracket generation failed'
            ];
        }
        
        // Test tournament participants
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_participants WHERE tournament_structure_id = ?");
        $stmt->execute([$created_tournament_id]);
        $participants_count = $stmt->fetch()['count'];
        
        $test_results['participants'] = [
            'success' => $participants_count > 0,
            'message' => "Tournament has {$participants_count} participants",
            'count' => $participants_count
        ];
        
        // Test matches generation
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM matches WHERE tournament_structure_id = ?");
        $stmt->execute([$created_tournament_id]);
        $matches_count = $stmt->fetch()['count'];
        
        $test_results['matches'] = [
            'success' => $matches_count > 0,
            'message' => "Tournament has {$matches_count} matches generated",
            'count' => $matches_count
        ];
        
    } catch (Exception $e) {
        $test_results['tournament_creation'] = [
            'success' => false,
            'message' => 'Tournament creation failed: ' . $e->getMessage(),
            'error' => $e->getMessage()
        ];
    }
}

// Get existing tournaments for display
$existing_tournaments = [];
try {
    $stmt = $conn->query("SELECT ts.*, tf.name as format_name, COUNT(tp.id) as participants_count 
                         FROM tournament_structures ts 
                         JOIN tournament_formats tf ON ts.tournament_format_id = tf.id 
                         LEFT JOIN tournament_participants tp ON ts.id = tp.tournament_structure_id 
                         GROUP BY ts.id 
                         ORDER BY ts.created_at DESC 
                         LIMIT 10");
    $existing_tournaments = $stmt->fetchAll();
} catch (Exception $e) {
    // Ignore errors for display
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tournament Creation Flow Test | SC_IMS Admin</title>
    <?php include 'includes/admin-styles.php'; ?>
    <style>
        .test-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        .test-result {
            margin: 1rem 0;
            padding: 1rem;
            border-radius: 6px;
            border-left: 4px solid;
        }
        .test-success {
            background: #f0fdf4;
            border-color: #22c55e;
            color: #166534;
        }
        .test-failure {
            background: #fef2f2;
            border-color: #ef4444;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="admin-content">
            <div class="page-header">
                <h1><i class="fas fa-cogs"></i> Tournament Creation Flow Test</h1>
                <p>Test the complete tournament creation and management process</p>
            </div>
            
            <!-- Test Tournament Creation -->
            <div class="test-section">
                <h3><i class="fas fa-plus-circle"></i> Create Test Tournament</h3>
                <p>This will create a test tournament with sample data to verify the system works correctly.</p>
                
                <form method="POST" style="margin: 1rem 0;">
                    <input type="hidden" name="action" value="test_create_tournament">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-play"></i> Run Tournament Creation Test
                    </button>
                </form>
                
                <?php if (!empty($test_results)): ?>
                    <h4>Test Results:</h4>
                    <?php foreach ($test_results as $test_name => $result): ?>
                        <div class="test-result <?php echo $result['success'] ? 'test-success' : 'test-failure'; ?>">
                            <strong><?php echo $result['success'] ? '✅' : '❌'; ?> <?php echo ucwords(str_replace('_', ' ', $test_name)); ?></strong>
                            <p><?php echo htmlspecialchars($result['message']); ?></p>
                            <?php if (isset($result['count'])): ?>
                                <small>Count: <?php echo $result['count']; ?></small>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                    
                    <?php if ($created_tournament_id): ?>
                        <div style="margin-top: 1rem; padding: 1rem; background: #f0f9ff; border-radius: 6px;">
                            <h5>🎯 Tournament Created Successfully!</h5>
                            <p>Tournament ID: <strong><?php echo $created_tournament_id; ?></strong></p>
                            <p>You can now test the tournament management interface.</p>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
            
            <!-- Existing Tournaments -->
            <div class="test-section">
                <h3><i class="fas fa-list"></i> Existing Tournaments</h3>
                <?php if (empty($existing_tournaments)): ?>
                    <p>No tournaments found. Create a test tournament above to get started.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Format</th>
                                    <th>Status</th>
                                    <th>Participants</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($existing_tournaments as $tournament): ?>
                                <tr>
                                    <td><?php echo $tournament['id']; ?></td>
                                    <td><?php echo htmlspecialchars($tournament['name']); ?></td>
                                    <td><?php echo htmlspecialchars($tournament['format_name']); ?></td>
                                    <td>
                                        <span class="badge badge-<?php echo $tournament['status'] === 'completed' ? 'success' : ($tournament['status'] === 'in_progress' ? 'warning' : 'secondary'); ?>">
                                            <?php echo ucfirst($tournament['status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo $tournament['participants_count']; ?></td>
                                    <td><?php echo date('M j, Y', strtotime($tournament['created_at'])); ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-info" onclick="viewTournamentDetails(<?php echo $tournament['id']; ?>)">
                                            <i class="fas fa-eye"></i> View
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Quick Actions -->
            <div class="test-section">
                <h3><i class="fas fa-tools"></i> Quick Actions</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <a href="test-tournament-end-to-end.php" class="btn btn-primary">
                        <i class="fas fa-check-circle"></i> Run Full Test Suite
                    </a>
                    <a href="test-manage-category.php" class="btn btn-secondary">
                        <i class="fas fa-trophy"></i> Test Tournament Page
                    </a>
                    <a href="events.php" class="btn btn-info">
                        <i class="fas fa-calendar"></i> Manage Events
                    </a>
                    <a href="tournament-setup.php" class="btn btn-warning">
                        <i class="fas fa-cog"></i> Tournament Setup
                    </a>
                </div>
            </div>
        </main>
    </div>
    
    <script>
        function viewTournamentDetails(tournamentId) {
            // Simple alert for now - could be expanded to show modal with details
            alert('Tournament ID: ' + tournamentId + '\n\nIn a full implementation, this would show detailed tournament information, bracket visualization, and management options.');
        }
    </script>
</body>
</html>
