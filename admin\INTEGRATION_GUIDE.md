# Tournament Category Management Integration Guide

## Overview

This guide provides instructions for integrating the streamlined tournament category management system into the existing SC_IMS codebase. The new system features three main tabs: Overview, Fixtures, and Standing.

## Files Included

### 1. Core Files
- `admin/manage-category-new.php` - Main tournament management page
- `api/category-management.php` - API endpoints for AJAX operations

### 2. Database Requirements

#### Required Tables
```sql
-- Tournament participants table
CREATE TABLE IF NOT EXISTS tournament_participants (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    club VARCHAR(255) NOT NULL,
    seed INT NULL,
    status ENUM('active', 'eliminated', 'waiting') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES sport_categories(id) ON DELETE CASCADE,
    UNIQUE KEY unique_seed_per_category (category_id, seed)
);

-- Tournament matches table
CREATE TABLE IF NOT EXISTS tournament_matches (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT NOT NULL,
    round INT NOT NULL,
    match_number INT NOT NULL,
    player_a_id INT NULL,
    player_b_id INT NULL,
    score_data JSON NULL,
    status ENUM('scheduled', 'in_progress', 'completed', 'cancelled') DEFAULT 'scheduled',
    scheduled_time DATETIME NULL,
    venue VARCHAR(255) NULL,
    winner_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES sport_categories(id) ON DELETE CASCADE,
    FOREIGN KEY (player_a_id) REFERENCES tournament_participants(id) ON DELETE SET NULL,
    FOREIGN KEY (player_b_id) REFERENCES tournament_participants(id) ON DELETE SET NULL,
    FOREIGN KEY (winner_id) REFERENCES tournament_participants(id) ON DELETE SET NULL
);

-- Add tournament settings to sport_categories table
ALTER TABLE sport_categories 
ADD COLUMN tournament_format ENUM('single_elimination', 'double_elimination', 'round_robin', 'swiss_system') DEFAULT 'single_elimination',
ADD COLUMN tournament_settings JSON NULL;
```

## Integration Steps

### Step 1: File Placement
1. Copy `manage-category-new.php` to `admin/` directory
2. Copy `category-management.php` to `api/` directory
3. Create the `api/` directory if it doesn't exist

### Step 2: Database Setup
1. Run the SQL commands above to create required tables
2. Update existing sport_categories table with new columns
3. Ensure proper foreign key relationships

### Step 3: URL Routing
Update your existing routing to handle the new API endpoints:

```php
// In your main router or .htaccess
RewriteRule ^api/category/([0-9]+)/(.*)$ api/category-management.php [QSA,L]
```

### Step 4: Authentication Integration
Ensure the API endpoints use your existing authentication system:

```php
// In api/category-management.php, update the auth require
require_once '../includes/auth.php';
requireAdmin(); // Use your existing admin auth function
```

### Step 5: Replace Existing Page
1. Backup your current `manage-category.php`
2. Replace with `manage-category-new.php` or integrate the features
3. Update navigation links to point to the new page

## Features Overview

### 1. Overview Tab
- **Category Settings Form**: Edit category name, sport format, max participants
- **Tournament Settings**: Configure third-place match, auto-seeding, scoring rules
- **Participants Management**: Add, edit, remove participants with seeding
- **Real-time Validation**: Form validation and AJAX persistence

### 2. Fixtures Tab
- **Dynamic Bracket Display**: Visual tournament bracket based on format
- **Inline Score Editing**: Update match scores directly in the bracket
- **Match Scheduling**: Reschedule matches with date/time picker
- **Match Status Tracking**: Visual indicators for match completion

### 3. Standing Tab
- **Live Standings Table**: Real-time rankings with sortable columns
- **Filtering Options**: Show all, top 8, or active participants only
- **Tournament Progress**: Visual progress indicators and statistics
- **Export Functionality**: Download standings in various formats

## API Endpoints

### Category Settings
- `GET /api/category/{id}/settings` - Get category settings
- `POST /api/category/{id}/settings` - Update category settings

### Participants Management
- `GET /api/category/{id}/participants` - Get participants list
- `POST /api/category/{id}/participants` - Add new participant
- `PUT /api/category/{id}/participants/{participant_id}` - Update participant
- `DELETE /api/category/{id}/participants/{participant_id}` - Remove participant

### Match Management
- `GET /api/category/{id}/match` - Get all matches for category
- `PUT /api/category/{id}/match/{match_id}` - Update match score or schedule

## Customization Options

### 1. Tournament Formats
Add new tournament formats by updating the `$tournament_formats` array:

```php
$tournament_formats = [
    'single_elimination' => 'Single Elimination',
    'double_elimination' => 'Double Elimination',
    'round_robin' => 'Round Robin',
    'swiss_system' => 'Swiss System',
    'custom_format' => 'Custom Format' // Add your format here
];
```

### 2. Scoring Systems
Modify the scoring inputs in the fixtures section:

```javascript
// Update score input generation for different sports
function generateScoreInputs(sportType) {
    switch(sportType) {
        case 'badminton':
            return generateBadmintonScores();
        case 'basketball':
            return generateBasketballScores();
        // Add more sports
    }
}
```

### 3. Styling Customization
The page uses inline CSS for easy customization. Key style sections:

- **Color Scheme**: Update CSS custom properties for brand colors
- **Layout**: Modify grid layouts and responsive breakpoints
- **Components**: Customize button styles, table appearance, modal design

## Testing

### Sample Data
The page includes sample data for testing:
- 5 sample participants with different seeding
- 3 sample matches in different states
- Complete standings table with rankings

### Test Scenarios
1. **Add Participant**: Test form validation and duplicate seed handling
2. **Edit Scores**: Update match scores and verify standings recalculation
3. **Reschedule Match**: Test date/time picker and venue updates
4. **Filter Standings**: Test different filter options and sorting
5. **Responsive Design**: Test on mobile and tablet devices

## Security Considerations

### 1. Input Validation
- All form inputs are validated on both client and server side
- SQL injection protection through prepared statements
- XSS prevention through proper output escaping

### 2. Authentication
- Admin authentication required for all operations
- Activity logging for audit trails
- CSRF protection for form submissions

### 3. Data Integrity
- Foreign key constraints ensure data consistency
- Transaction handling for complex operations
- Backup procedures for tournament data

## Performance Optimization

### 1. Database Indexing
```sql
-- Add indexes for better performance
CREATE INDEX idx_participants_category ON tournament_participants(category_id);
CREATE INDEX idx_matches_category ON tournament_matches(category_id);
CREATE INDEX idx_matches_round ON tournament_matches(category_id, round);
```

### 2. Caching
- Implement caching for frequently accessed tournament data
- Use browser caching for static assets
- Consider Redis for session management

### 3. AJAX Optimization
- Minimize API calls through intelligent data loading
- Implement debouncing for real-time updates
- Use pagination for large participant lists

## Troubleshooting

### Common Issues
1. **API Endpoints Not Working**: Check URL rewriting and file permissions
2. **Database Errors**: Verify table structure and foreign keys
3. **Authentication Failures**: Ensure auth functions are properly included
4. **JavaScript Errors**: Check browser console for debugging information

### Debug Mode
Enable debug mode by adding to the top of files:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## Future Enhancements

### Planned Features
- Real-time updates using WebSockets
- Advanced bracket visualization
- Mobile app integration
- Multi-language support
- Advanced analytics and reporting

### Extension Points
- Plugin system for custom tournament formats
- API webhooks for external integrations
- Custom scoring algorithms
- Advanced seeding methods

This integration guide provides a complete roadmap for implementing the streamlined tournament management system in your SC_IMS application.
