<?php
/**
 * Test Category Data Integration
 * Quick test to verify database connectivity and data retrieval
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>SC_IMS Category Data Test</h1>";

// Test 1: Get all sport categories
echo "<h2>1. Sport Categories</h2>";
try {
    $sql = "SELECT
                sc.id as category_id,
                sc.category_name,
                sc.category_type,
                sc.venue,
                sc.max_participants,
                sc.status as category_status,
                es.id as event_sport_id,
                s.name as sport_name,
                s.type as sport_type,
                e.name as event_name
            FROM sport_categories sc
            JOIN event_sports es ON sc.event_sport_id = es.id
            JOIN sports s ON es.sport_id = s.id
            JOIN events e ON es.event_id = e.id
            ORDER BY e.name, s.name, sc.category_name
            LIMIT 10";

    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $categories = $stmt->fetchAll();

    if (empty($categories)) {
        echo "<p style='color: orange;'>No sport categories found. You may need to create some test data.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Event</th><th>Sport</th><th>Category</th><th>Type</th><th>Venue</th><th>Max Participants</th><th>Status</th></tr>";
        foreach ($categories as $cat) {
            echo "<tr>";
            echo "<td>{$cat['category_id']}</td>";
            echo "<td>{$cat['event_name']}</td>";
            echo "<td>{$cat['sport_name']}</td>";
            echo "<td>{$cat['category_name']}</td>";
            echo "<td>{$cat['category_type']}</td>";
            echo "<td>{$cat['venue']}</td>";
            echo "<td>{$cat['max_participants']}</td>";
            echo "<td>{$cat['category_status']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// Test 2: Get registrations for first category
echo "<h2>2. Registrations (for first category)</h2>";
if (!empty($categories)) {
    $first_category = $categories[0];
    try {
        $sql = "SELECT
                    r.id as registration_id,
                    r.department_id,
                    d.name as department_name,
                    d.abbreviation as department_abbr,
                    d.color_code,
                    r.team_name,
                    r.participants,
                    r.status as registration_status,
                    r.registration_date
                FROM registrations r
                JOIN departments d ON r.department_id = d.id
                WHERE r.event_sport_id = ?
                ORDER BY d.name";

        $stmt = $conn->prepare($sql);
        $stmt->execute([$first_category['event_sport_id']]);
        $registrations = $stmt->fetchAll();

        if (empty($registrations)) {
            echo "<p style='color: orange;'>No registrations found for {$first_category['sport_name']} - {$first_category['category_name']}.</p>";
        } else {
            echo "<p><strong>Registrations for {$first_category['sport_name']} - {$first_category['category_name']}:</strong></p>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Department</th><th>Team Name</th><th>Participants</th><th>Status</th><th>Date</th></tr>";
            foreach ($registrations as $reg) {
                $participants_count = 0;
                if ($reg['participants']) {
                    $participants = json_decode($reg['participants'], true);
                    $participants_count = is_array($participants) ? count($participants) : 0;
                }
                echo "<tr>";
                echo "<td>{$reg['registration_id']}</td>";
                echo "<td>{$reg['department_name']} ({$reg['department_abbr']})</td>";
                echo "<td>" . ($reg['team_name'] ?: 'Default') . "</td>";
                echo "<td>{$participants_count} participants</td>";
                echo "<td>{$reg['registration_status']}</td>";
                echo "<td>" . date('M j, Y', strtotime($reg['registration_date'])) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    }
}

// Test 3: Get tournament formats
echo "<h2>3. Tournament Formats</h2>";
try {
    $sql = "SELECT * FROM tournament_formats ORDER BY sport_type_category, name LIMIT 10";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $formats = $stmt->fetchAll();

    if (empty($formats)) {
        echo "<p style='color: orange;'>No tournament formats found. You may need to run the tournament schema update.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Code</th><th>Category</th><th>Min Participants</th><th>Max Participants</th><th>Description</th></tr>";
        foreach ($formats as $format) {
            echo "<tr>";
            echo "<td>{$format['id']}</td>";
            echo "<td>{$format['name']}</td>";
            echo "<td>{$format['code']}</td>";
            echo "<td>{$format['sport_type_category']}</td>";
            echo "<td>{$format['min_participants']}</td>";
            echo "<td>" . ($format['max_participants'] ?: 'Unlimited') . "</td>";
            echo "<td>" . substr($format['description'], 0, 50) . "...</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// Test 4: Get existing tournaments
echo "<h2>4. Existing Tournaments</h2>";
try {
    $sql = "SELECT
                ts.id as tournament_id,
                ts.name as tournament_name,
                ts.status,
                ts.participant_count,
                ts.current_round,
                ts.total_rounds,
                tf.name as format_name,
                e.name as event_name,
                s.name as sport_name
            FROM tournament_structures ts
            LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
            JOIN event_sports es ON ts.event_sport_id = es.id
            JOIN sports s ON es.sport_id = s.id
            JOIN events e ON es.event_id = e.id
            ORDER BY ts.created_at DESC
            LIMIT 10";

    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $tournaments = $stmt->fetchAll();

    if (empty($tournaments)) {
        echo "<p style='color: orange;'>No tournaments found. Create a tournament to test the system.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Event</th><th>Sport</th><th>Tournament</th><th>Format</th><th>Status</th><th>Participants</th><th>Progress</th></tr>";
        foreach ($tournaments as $tournament) {
            echo "<tr>";
            echo "<td>{$tournament['tournament_id']}</td>";
            echo "<td>{$tournament['event_name']}</td>";
            echo "<td>{$tournament['sport_name']}</td>";
            echo "<td>{$tournament['tournament_name']}</td>";
            echo "<td>{$tournament['format_name']}</td>";
            echo "<td>{$tournament['status']}</td>";
            echo "<td>{$tournament['participant_count']}</td>";
            echo "<td>Round {$tournament['current_round']}/{$tournament['total_rounds']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// Test 5: Database table status
echo "<h2>5. Database Table Status</h2>";
$required_tables = [
    'events', 'sports', 'sport_categories', 'event_sports', 'departments', 
    'registrations', 'tournament_formats', 'tournament_structures', 
    'tournament_participants', 'matches'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Table</th><th>Status</th><th>Row Count</th></tr>";

foreach ($required_tables as $table) {
    try {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM `$table`");
        $stmt->execute();
        $result = $stmt->fetch();
        $count = $result['count'];
        $status = "✓ Exists";
        $color = "green";
    } catch (Exception $e) {
        $count = "N/A";
        $status = "✗ Missing";
        $color = "red";
    }
    
    echo "<tr>";
    echo "<td>$table</td>";
    echo "<td style='color: $color;'>$status</td>";
    echo "<td>$count</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>Test Complete</h2>";
echo "<p><a href='manage-category-new.php?category_id=" . ($categories[0]['category_id'] ?? 1) . "'>→ Go to Tournament Management</a></p>";
echo "<p><a href='events.php'>→ Back to Events</a></p>";
?>
