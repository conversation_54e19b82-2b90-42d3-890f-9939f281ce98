<?php
/**
 * Streamlined Tournament Category Management Page
 * Features: Overview, Fixtures, Standing tabs
 * Integrated with SC_IMS database structure
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get category ID from URL
$category_id = $_GET['category_id'] ?? null;
$event_id = $_GET['event_id'] ?? null;

if (!$category_id) {
    header('Location: events.php');
    exit;
}

// Get category information with related data
try {
    $sql = "SELECT
                sc.id as category_id,
                sc.category_name,
                sc.category_type,
                sc.referee_name,
                sc.referee_email,
                sc.venue,
                sc.max_participants,
                sc.status as category_status,
                es.id as event_sport_id,
                es.status as event_sport_status,
                s.id as sport_id,
                s.name as sport_name,
                s.type as sport_type,
                s.scoring_method,
                s.bracket_format,
                st.name as sport_type_name,
                st.category as sport_type_category,
                st.icon_class,
                e.id as event_id,
                e.name as event_name,
                e.status as event_status
            FROM sport_categories sc
            JOIN event_sports es ON sc.event_sport_id = es.id
            JOIN sports s ON es.sport_id = s.id
            LEFT JOIN sport_types st ON s.sport_type_id = st.id
            JOIN events e ON es.event_id = e.id
            WHERE sc.id = ?";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$category_id]);
    $category = $stmt->fetch();

    if (!$category) {
        header('Location: events.php');
        exit;
    }
} catch (Exception $e) {
    error_log("Error fetching category data: " . $e->getMessage());
    header('Location: events.php');
    exit;
}

// Get tournament formats available for this sport type
$tournament_formats = [];
try {
    $sql = "SELECT * FROM tournament_formats
            WHERE sport_type_category = ? OR sport_type_category = 'all'
            ORDER BY name";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$category['sport_type_category'] ?? 'all']);
    $tournament_formats = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error fetching tournament formats: " . $e->getMessage());
}

// Get existing tournament structure for this category
$tournament_structure = null;
try {
    $sql = "SELECT ts.*, tf.name as format_name, tf.code as format_code
            FROM tournament_structures ts
            LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
            WHERE ts.event_sport_id = ?
            ORDER BY ts.created_at DESC LIMIT 1";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$category['event_sport_id']]);
    $tournament_structure = $stmt->fetch();
} catch (Exception $e) {
    error_log("Error fetching tournament structure: " . $e->getMessage());
}

// Get registrations for this event sport
$registrations = [];
try {
    $sql = "SELECT
                r.id as registration_id,
                r.department_id,
                d.name as department_name,
                d.abbreviation as department_abbr,
                d.color_code,
                r.team_name,
                r.participants,
                r.status as registration_status,
                r.registration_date
            FROM registrations r
            JOIN departments d ON r.department_id = d.id
            WHERE r.event_sport_id = ? AND r.status = 'confirmed'
            ORDER BY d.name";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$category['event_sport_id']]);
    $registrations = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error fetching registrations: " . $e->getMessage());
}

// Get tournament participants if tournament exists
$tournament_participants = [];
if ($tournament_structure) {
    try {
        $sql = "SELECT
                    tp.*,
                    r.team_name,
                    r.department_id,
                    d.name as department_name,
                    d.abbreviation as department_abbr,
                    d.color_code
                FROM tournament_participants tp
                JOIN registrations r ON tp.registration_id = r.id
                JOIN departments d ON r.department_id = d.id
                WHERE tp.tournament_structure_id = ?
                ORDER BY tp.seed_number ASC, d.name ASC";

        $stmt = $conn->prepare($sql);
        $stmt->execute([$tournament_structure['id']]);
        $tournament_participants = $stmt->fetchAll();
    } catch (Exception $e) {
        error_log("Error fetching tournament participants: " . $e->getMessage());
    }
}

// Get matches for this tournament
$matches = [];
if ($tournament_structure) {
    try {
        $sql = "SELECT
                    m.*,
                    r1.team_name as team1_name,
                    r1.department_id as team1_dept_id,
                    d1.name as team1_dept_name,
                    d1.abbreviation as team1_abbr,
                    r2.team_name as team2_name,
                    r2.department_id as team2_dept_id,
                    d2.name as team2_dept_name,
                    d2.abbreviation as team2_abbr,
                    rw.team_name as winner_name,
                    dw.name as winner_dept_name
                FROM matches m
                LEFT JOIN registrations r1 ON m.team1_id = r1.id
                LEFT JOIN departments d1 ON r1.department_id = d1.id
                LEFT JOIN registrations r2 ON m.team2_id = r2.id
                LEFT JOIN departments d2 ON r2.department_id = d2.id
                LEFT JOIN registrations rw ON m.winner_id = rw.id
                LEFT JOIN departments dw ON rw.department_id = dw.id
                WHERE m.tournament_structure_id = ?
                ORDER BY m.round_number ASC, m.match_number ASC";

        $stmt = $conn->prepare($sql);
        $stmt->execute([$tournament_structure['id']]);
        $matches = $stmt->fetchAll();
    } catch (Exception $e) {
        error_log("Error fetching matches: " . $e->getMessage());
    }
}

// Calculate tournament statistics
$tournament_stats = [
    'total_registrations' => count($registrations),
    'tournament_exists' => !empty($tournament_structure),
    'total_participants' => count($tournament_participants),
    'total_matches' => count($matches),
    'completed_matches' => count(array_filter($matches, fn($m) => $m['status'] === 'completed')),
    'current_round' => $tournament_structure['current_round'] ?? 0,
    'total_rounds' => $tournament_structure['total_rounds'] ?? 0,
    'tournament_status' => $tournament_structure['status'] ?? 'not_created'
];

// Get current standings if tournament exists
$standings = [];
if ($tournament_structure && !empty($tournament_participants)) {
    try {
        // Calculate standings based on tournament participants data
        foreach ($tournament_participants as $participant) {
            $standings[] = [
                'participant_id' => $participant['id'],
                'registration_id' => $participant['registration_id'],
                'team_name' => $participant['team_name'],
                'department_name' => $participant['department_name'],
                'department_abbr' => $participant['department_abbr'],
                'seed_number' => $participant['seed_number'],
                'wins' => $participant['wins'],
                'losses' => $participant['losses'],
                'draws' => $participant['draws'],
                'points' => $participant['points'],
                'current_status' => $participant['current_status'],
                'color_code' => $participant['color_code']
            ];
        }

        // Sort standings by points, then wins, then seed
        usort($standings, function($a, $b) {
            if ($a['points'] != $b['points']) {
                return $b['points'] <=> $a['points'];
            }
            if ($a['wins'] != $b['wins']) {
                return $b['wins'] <=> $a['wins'];
            }
            return $a['seed_number'] <=> $b['seed_number'];
        });

        // Add rank to standings
        foreach ($standings as $index => &$standing) {
            $standing['rank'] = $index + 1;
        }
    } catch (Exception $e) {
        error_log("Error calculating standings: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($category['category_name']); ?> - SC_IMS Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Header */
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            opacity: 0.9;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .status-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            text-transform: uppercase;
            font-weight: 500;
        }

        /* Tabs */
        .tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .tab-button {
            flex: 1;
            padding: 1rem 2rem;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .tab-button:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .tab-button.active {
            background: #3b82f6;
            color: white;
        }

        /* Tab Content */
        .tab-content {
            display: none;
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .tab-content.active {
            display: block;
        }

        /* Form Styles */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
        }

        .form-input, .form-select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .checkbox-group input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #3b82f6;
        }

        /* Button Styles */
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        /* Table Styles */
        .table-container {
            overflow-x: auto;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .table th,
        .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            margin-top: 2rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .tabs {
                flex-direction: column;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .page-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title"><?php echo htmlspecialchars($category['category_name']); ?></h1>
            <div class="page-subtitle">
                <span><i class="fas fa-trophy"></i> <?php echo htmlspecialchars($category['sport_name']); ?></span>
                <span><i class="fas fa-calendar"></i> <?php echo htmlspecialchars($category['event_name']); ?></span>
                <span><i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($category['venue']); ?></span>
                <span class="status-badge"><?php echo htmlspecialchars($category['category_status']); ?></span>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="tabs">
            <button class="tab-button active" onclick="switchTab('overview')" data-tab="overview">
                <i class="fas fa-cog"></i>
                Overview
            </button>
            <button class="tab-button" onclick="switchTab('fixtures')" data-tab="fixtures">
                <i class="fas fa-calendar-alt"></i>
                Fixtures
            </button>
            <button class="tab-button" onclick="switchTab('standing')" data-tab="standing">
                <i class="fas fa-trophy"></i>
                Standing
            </button>
        </div>

        <!-- Overview Tab -->
        <div id="overview-tab" class="tab-content active">
            <!-- Tournament Status Card -->
            <div style="background: white; border-radius: 12px; padding: 2rem; margin-bottom: 2rem; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 1.5rem;">
                    <h2 style="margin: 0; color: #1f2937; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-trophy"></i> Tournament Status
                    </h2>
                    <?php if (!$tournament_stats['tournament_exists']): ?>
                        <button type="button" class="btn btn-primary" onclick="createTournament()"
                                <?php echo $tournament_stats['total_registrations'] < 2 ? 'disabled title="Need at least 2 registrations"' : ''; ?>>
                            <i class="fas fa-plus"></i> Create Tournament
                        </button>
                    <?php endif; ?>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem;">
                    <div style="text-align: center; padding: 1rem; background: #f8fafc; border-radius: 8px;">
                        <div style="font-size: 2rem; font-weight: 700; color: #3b82f6;">
                            <?php echo $tournament_stats['total_registrations']; ?>
                        </div>
                        <div style="color: #6b7280; font-size: 0.9rem;">Registered Teams</div>
                    </div>

                    <div style="text-align: center; padding: 1rem; background: #f8fafc; border-radius: 8px;">
                        <div style="font-size: 2rem; font-weight: 700; color: <?php echo $tournament_stats['tournament_exists'] ? '#10b981' : '#6b7280'; ?>;">
                            <?php echo $tournament_stats['tournament_exists'] ? 'Active' : 'Not Created'; ?>
                        </div>
                        <div style="color: #6b7280; font-size: 0.9rem;">Tournament Status</div>
                    </div>

                    <?php if ($tournament_stats['tournament_exists']): ?>
                        <div style="text-align: center; padding: 1rem; background: #f8fafc; border-radius: 8px;">
                            <div style="font-size: 2rem; font-weight: 700; color: #f59e0b;">
                                <?php echo $tournament_stats['completed_matches']; ?>/<?php echo $tournament_stats['total_matches']; ?>
                            </div>
                            <div style="color: #6b7280; font-size: 0.9rem;">Matches Completed</div>
                        </div>

                        <div style="text-align: center; padding: 1rem; background: #f8fafc; border-radius: 8px;">
                            <div style="font-size: 2rem; font-weight: 700; color: #8b5cf6;">
                                Round <?php echo $tournament_stats['current_round']; ?>/<?php echo $tournament_stats['total_rounds']; ?>
                            </div>
                            <div style="color: #6b7280; font-size: 0.9rem;">Current Progress</div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Tournament Configuration -->
            <?php if (!$tournament_stats['tournament_exists']): ?>
                <div style="background: white; border-radius: 12px; padding: 2rem; margin-bottom: 2rem; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                    <h3 style="margin: 0 0 1.5rem 0; color: #1f2937; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-cogs"></i> Tournament Configuration
                    </h3>

                    <form id="tournamentConfigForm" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                        <div>
                            <div class="form-group">
                                <label class="form-label" for="tournamentFormat">Tournament Format</label>
                                <select id="tournamentFormat" class="form-select" required>
                                    <option value="">Select tournament format...</option>
                                    <?php foreach ($tournament_formats as $format): ?>
                                        <option value="<?php echo $format['id']; ?>"
                                                data-description="<?php echo htmlspecialchars($format['description']); ?>"
                                                data-min-participants="<?php echo $format['min_participants']; ?>"
                                                data-max-participants="<?php echo $format['max_participants'] ?? 'unlimited'; ?>">
                                            <?php echo htmlspecialchars($format['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="seedingMethod">Seeding Method</label>
                                <select id="seedingMethod" class="form-select">
                                    <option value="random">Random Seeding</option>
                                    <option value="ranking">Ranking-based Seeding</option>
                                    <option value="manual">Manual Seeding</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <div class="form-group">
                                <label class="form-label" for="tournamentName">Tournament Name</label>
                                <input type="text" id="tournamentName" class="form-input"
                                       value="<?php echo htmlspecialchars($category['category_name'] . ' Tournament'); ?>" required>
                            </div>

                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="thirdPlaceMatch">
                                    <label for="thirdPlaceMatch">Include Third Place Match</label>
                                </div>
                            </div>
                        </div>
                    </form>

                    <div style="margin-top: 1.5rem;">
                        <button type="button" class="btn btn-primary" onclick="createTournament()"
                                <?php echo $tournament_stats['total_registrations'] < 2 ? 'disabled' : ''; ?>>
                            <i class="fas fa-trophy"></i> Create Tournament
                        </button>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Registered Teams -->
            <div style="background: white; border-radius: 12px; padding: 2rem; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                    <h3 style="margin: 0; color: #1f2937; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-users"></i> Registered Teams (<?php echo count($registrations); ?>)
                    </h3>
                    <?php if ($tournament_stats['tournament_exists']): ?>
                        <span style="background: #10b981; color: white; padding: 0.5rem 1rem; border-radius: 6px; font-size: 0.9rem;">
                            <i class="fas fa-lock"></i> Tournament Active
                        </span>
                    <?php endif; ?>
                </div>

                <?php if (empty($registrations)): ?>
                    <div style="text-align: center; padding: 3rem; color: #6b7280;">
                        <div style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;">
                            <i class="fas fa-users-slash"></i>
                        </div>
                        <h4 style="margin: 0 0 0.5rem 0; color: #374151;">No Teams Registered</h4>
                        <p style="margin: 0;">Teams need to register for this sport before creating a tournament.</p>
                    </div>
                <?php else: ?>
                    <div style="display: grid; gap: 1rem;">
                        <?php foreach ($registrations as $registration): ?>
                            <div style="display: flex; align-items: center; padding: 1rem; background: #f8fafc; border-radius: 8px; border-left: 4px solid <?php echo $registration['color_code'] ?? '#3b82f6'; ?>;">
                                <div style="width: 50px; height: 50px; background: <?php echo $registration['color_code'] ?? '#3b82f6'; ?>; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 700; margin-right: 1rem;">
                                    <?php echo strtoupper(substr($registration['department_abbr'] ?? $registration['department_name'], 0, 2)); ?>
                                </div>

                                <div style="flex: 1;">
                                    <h5 style="margin: 0 0 0.25rem 0; color: #1f2937;">
                                        <?php echo htmlspecialchars($registration['team_name'] ?: $registration['department_name']); ?>
                                    </h5>
                                    <p style="margin: 0; color: #6b7280; font-size: 0.9rem;">
                                        <?php echo htmlspecialchars($registration['department_name']); ?>
                                        <?php if ($registration['participants']): ?>
                                            <?php $participants = json_decode($registration['participants'], true); ?>
                                            <?php if (is_array($participants)): ?>
                                                • <?php echo count($participants); ?> participants
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </p>
                                </div>

                                <div style="text-align: right;">
                                    <span style="background: #10b981; color: white; padding: 0.25rem 0.75rem; border-radius: 20px; font-size: 0.8rem;">
                                        <?php echo ucfirst($registration['registration_status']); ?>
                                    </span>
                                    <div style="font-size: 0.8rem; color: #6b7280; margin-top: 0.25rem;">
                                        <?php echo date('M j, Y', strtotime($registration['registration_date'])); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Fixtures Tab -->
        <div id="fixtures-tab" class="tab-content">
            <h2 style="margin-bottom: 2rem; color: #1f2937;">
                <i class="fas fa-calendar-alt"></i> Tournament Fixtures
            </h2>

            <!-- Tournament Bracket -->
            <div id="tournamentBracket">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                    <h3 style="color: #1f2937;">Single Elimination Bracket</h3>
                    <div>
                        <button type="button" class="btn btn-secondary" onclick="generateBracket()">
                            <i class="fas fa-sync"></i> Regenerate Bracket
                        </button>
                        <button type="button" class="btn btn-primary" onclick="scheduleMatches()">
                            <i class="fas fa-calendar-plus"></i> Schedule Matches
                        </button>
                    </div>
                </div>

                <!-- Bracket Display -->
                <div class="bracket-container" style="overflow-x: auto; padding: 1rem; background: #f9fafb; border-radius: 8px;">
                    <div class="bracket-rounds" style="display: flex; gap: 3rem; min-width: 800px;">

                        <!-- Round 1 -->
                        <div class="bracket-round">
                            <h4 style="text-align: center; margin-bottom: 1rem; color: #374151;">Round 1</h4>
                            <div class="matches" style="display: flex; flex-direction: column; gap: 2rem;">

                                <!-- Match 1 -->
                                <div class="match-card" style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; min-width: 250px;">
                                    <div class="match-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                                        <span style="font-weight: 600; color: #374151;">Match 1</span>
                                        <span style="font-size: 0.8rem; color: #6b7280;">Jan 15, 10:00 AM</span>
                                    </div>

                                    <div class="match-players">
                                        <div class="player" style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem; background: #f3f4f6; border-radius: 4px; margin-bottom: 0.5rem;">
                                            <span style="font-weight: 500;">John Smith / Mike Johnson</span>
                                            <div class="score" style="display: flex; gap: 0.25rem;">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="21" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="19" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="21" min="0" max="30">
                                            </div>
                                        </div>

                                        <div class="player" style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem; background: #f3f4f6; border-radius: 4px;">
                                            <span style="font-weight: 500;">Mark Davis / Kevin Park</span>
                                            <div class="score" style="display: flex; gap: 0.25rem;">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="18" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="21" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="15" min="0" max="30">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="match-actions" style="display: flex; justify-content: space-between; align-items: center; margin-top: 1rem;">
                                        <span style="font-size: 0.8rem; color: #10b981; font-weight: 500;">
                                            <i class="fas fa-check-circle"></i> Completed
                                        </span>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-secondary" onclick="rescheduleMatch(1)">
                                                <i class="fas fa-clock"></i> Reschedule
                                            </button>
                                            <button type="button" class="btn btn-sm btn-primary" onclick="saveMatchScore(1)">
                                                <i class="fas fa-save"></i> Save
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Match 2 -->
                                <div class="match-card" style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; min-width: 250px;">
                                    <div class="match-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                                        <span style="font-weight: 600; color: #374151;">Match 2</span>
                                        <span style="font-size: 0.8rem; color: #6b7280;">Jan 15, 11:00 AM</span>
                                    </div>

                                    <div class="match-players">
                                        <div class="player" style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem; background: #f3f4f6; border-radius: 4px; margin-bottom: 0.5rem;">
                                            <span style="font-weight: 500;">Alex Brown / Tom Wilson</span>
                                            <div class="score" style="display: flex; gap: 0.25rem;">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="21" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="21" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="" min="0" max="30">
                                            </div>
                                        </div>

                                        <div class="player" style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem; background: #f3f4f6; border-radius: 4px;">
                                            <span style="font-weight: 500;">David Lee / Chris Taylor</span>
                                            <div class="score" style="display: flex; gap: 0.25rem;">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="15" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="18" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="" min="0" max="30">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="match-actions" style="display: flex; justify-content: space-between; align-items: center; margin-top: 1rem;">
                                        <span style="font-size: 0.8rem; color: #10b981; font-weight: 500;">
                                            <i class="fas fa-check-circle"></i> Completed
                                        </span>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-secondary" onclick="rescheduleMatch(2)">
                                                <i class="fas fa-clock"></i> Reschedule
                                            </button>
                                            <button type="button" class="btn btn-sm btn-primary" onclick="saveMatchScore(2)">
                                                <i class="fas fa-save"></i> Save
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Round 2 (Finals) -->
                        <div class="bracket-round">
                            <h4 style="text-align: center; margin-bottom: 1rem; color: #374151;">Finals</h4>
                            <div class="matches" style="display: flex; flex-direction: column; gap: 2rem; justify-content: center; height: 100%;">

                                <!-- Final Match -->
                                <div class="match-card" style="background: white; border: 2px solid #f59e0b; border-radius: 8px; padding: 1rem; min-width: 250px;">
                                    <div class="match-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                                        <span style="font-weight: 600; color: #f59e0b;">
                                            <i class="fas fa-crown"></i> Final
                                        </span>
                                        <span style="font-size: 0.8rem; color: #6b7280;">Jan 15, 2:00 PM</span>
                                    </div>

                                    <div class="match-players">
                                        <div class="player" style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem; background: #fef3c7; border-radius: 4px; margin-bottom: 0.5rem;">
                                            <span style="font-weight: 500;">John Smith / Mike Johnson</span>
                                            <div class="score" style="display: flex; gap: 0.25rem;">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="" min="0" max="30">
                                            </div>
                                        </div>

                                        <div class="player" style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem; background: #fef3c7; border-radius: 4px;">
                                            <span style="font-weight: 500;">Alex Brown / Tom Wilson</span>
                                            <div class="score" style="display: flex; gap: 0.25rem;">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="" min="0" max="30">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="match-actions" style="display: flex; justify-content: space-between; align-items: center; margin-top: 1rem;">
                                        <span style="font-size: 0.8rem; color: #f59e0b; font-weight: 500;">
                                            <i class="fas fa-clock"></i> Scheduled
                                        </span>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-secondary" onclick="rescheduleMatch(3)">
                                                <i class="fas fa-clock"></i> Reschedule
                                            </button>
                                            <button type="button" class="btn btn-sm btn-primary" onclick="saveMatchScore(3)">
                                                <i class="fas fa-save"></i> Save
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Standing Tab -->
        <div id="standing-tab" class="tab-content">
            <h2 style="margin-bottom: 2rem; color: #1f2937;">
                <i class="fas fa-trophy"></i> Tournament Standings
            </h2>

            <?php if (!$tournament_stats['tournament_exists']): ?>
                <div style="text-align: center; padding: 3rem; color: #6b7280; background: white; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                    <div style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h3 style="margin: 0 0 0.5rem 0; color: #374151;">No Tournament Created</h3>
                    <p style="margin: 0;">Create a tournament first to view standings and rankings.</p>
                </div>
            <?php elseif (empty($standings)): ?>
                <div style="text-align: center; padding: 3rem; color: #6b7280; background: white; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                    <div style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;">
                        <i class="fas fa-users-slash"></i>
                    </div>
                    <h3 style="margin: 0 0 0.5rem 0; color: #374151;">No Participants</h3>
                    <p style="margin: 0;">No participants found in this tournament.</p>
                </div>
            <?php else: ?>
                <!-- Standings Controls -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                    <div style="display: flex; gap: 1rem; align-items: center;">
                        <label style="font-weight: 500; color: #374151;">Filter:</label>
                        <select id="standingsFilter" class="form-select" style="width: auto;" onchange="filterStandings()">
                            <option value="all">All Participants</option>
                            <option value="top8">Top 8</option>
                            <option value="active">Active Only</option>
                            <option value="eliminated">Eliminated Only</option>
                        </select>
                    </div>
                    <div>
                        <button type="button" class="btn btn-secondary" onclick="refreshStandings()">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                        <button type="button" class="btn btn-primary" onclick="exportStandings()">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>

                <!-- Standings Table -->
                <div class="table-container" style="background: white; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                    <table class="table" id="standingsTable">
                        <thead>
                            <tr>
                                <th style="cursor: pointer;" onclick="sortStandings('rank')">
                                    Rank <i class="fas fa-sort"></i>
                                </th>
                                <th style="cursor: pointer;" onclick="sortStandings('name')">
                                    Team <i class="fas fa-sort"></i>
                                </th>
                                <th style="cursor: pointer;" onclick="sortStandings('wins')">
                                    Wins <i class="fas fa-sort"></i>
                                </th>
                                <th style="cursor: pointer;" onclick="sortStandings('losses')">
                                    Losses <i class="fas fa-sort"></i>
                                </th>
                                <?php if ($tournament_structure && $tournament_structure['format_code'] === 'round_robin'): ?>
                                    <th style="cursor: pointer;" onclick="sortStandings('draws')">
                                        Draws <i class="fas fa-sort"></i>
                                    </th>
                                <?php endif; ?>
                                <th style="cursor: pointer;" onclick="sortStandings('points')">
                                    Points <i class="fas fa-sort"></i>
                                </th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($standings as $index => $standing): ?>
                                <tr style="<?php echo $index < 3 ? 'background: ' . ($index === 0 ? '#fef3c7' : ($index === 1 ? '#f3f4f6' : '#fef2f2')) . ';' : ''; ?>">
                                    <td>
                                        <?php
                                        $rankColor = '#6b7280';
                                        $rankIcon = '';
                                        if ($standing['rank'] === 1) {
                                            $rankColor = '#f59e0b';
                                            $rankIcon = '<i class="fas fa-crown"></i> ';
                                        } elseif ($standing['rank'] === 2) {
                                            $rankColor = '#6b7280';
                                            $rankIcon = '<i class="fas fa-medal"></i> ';
                                        } elseif ($standing['rank'] === 3) {
                                            $rankColor = '#cd7f32';
                                            $rankIcon = '<i class="fas fa-award"></i> ';
                                        }
                                        ?>
                                        <span style="background: <?php echo $rankColor; ?>; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-weight: 600;">
                                            <?php echo $rankIcon . $standing['rank']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                                            <div style="width: 32px; height: 32px; background: <?php echo $standing['color_code'] ?? '#3b82f6'; ?>; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.8rem;">
                                                <?php echo strtoupper(substr($standing['department_abbr'] ?? $standing['department_name'], 0, 2)); ?>
                                            </div>
                                            <div>
                                                <div style="font-weight: 600; color: #1f2937;">
                                                    <?php echo htmlspecialchars($standing['team_name']); ?>
                                                </div>
                                                <div style="font-size: 0.8rem; color: #6b7280;">
                                                    <?php echo htmlspecialchars($standing['department_name']); ?>
                                                    <?php if ($standing['seed_number']): ?>
                                                        • Seed #<?php echo $standing['seed_number']; ?>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td style="font-weight: 600; color: <?php echo $standing['wins'] > 0 ? '#10b981' : '#6b7280'; ?>;">
                                        <?php echo $standing['wins']; ?>
                                    </td>
                                    <td style="color: <?php echo $standing['losses'] > 0 ? '#ef4444' : '#6b7280'; ?>;">
                                        <?php echo $standing['losses']; ?>
                                    </td>
                                    <?php if ($tournament_structure && $tournament_structure['format_code'] === 'round_robin'): ?>
                                        <td style="color: <?php echo $standing['draws'] > 0 ? '#f59e0b' : '#6b7280'; ?>;">
                                            <?php echo $standing['draws']; ?>
                                        </td>
                                    <?php endif; ?>
                                    <td style="font-weight: 600; color: #3b82f6;">
                                        <?php echo number_format($standing['points'], 1); ?>
                                    </td>
                                    <td>
                                        <?php
                                        $statusColor = '#6b7280';
                                        $statusText = ucfirst($standing['current_status']);
                                        switch ($standing['current_status']) {
                                            case 'active':
                                                $statusColor = '#10b981';
                                                break;
                                            case 'eliminated':
                                                $statusColor = '#ef4444';
                                                break;
                                            case 'bye':
                                                $statusColor = '#f59e0b';
                                                break;
                                            case 'withdrawn':
                                                $statusColor = '#6b7280';
                                                break;
                                        }
                                        ?>
                                        <span style="background: <?php echo $statusColor; ?>; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                                            <?php echo $statusText; ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Tournament Progress Summary -->
                <div style="margin-top: 2rem; padding: 2rem; background: white; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                    <h4 style="margin-bottom: 1.5rem; color: #1f2937;">Tournament Progress</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem;">
                        <div style="text-align: center; padding: 1rem; background: #f8fafc; border-radius: 8px;">
                            <div style="font-size: 2rem; font-weight: 700; color: #3b82f6;">
                                <?php echo $tournament_stats['completed_matches']; ?>/<?php echo $tournament_stats['total_matches']; ?>
                            </div>
                            <div style="color: #6b7280; font-size: 0.9rem;">Matches Completed</div>
                        </div>
                        <div style="text-align: center; padding: 1rem; background: #f8fafc; border-radius: 8px;">
                            <div style="font-size: 2rem; font-weight: 700; color: #10b981;">
                                <?php echo count(array_filter($standings, fn($s) => $s['current_status'] === 'active')); ?>
                            </div>
                            <div style="color: #6b7280; font-size: 0.9rem;">Active Teams</div>
                        </div>
                        <div style="text-align: center; padding: 1rem; background: #f8fafc; border-radius: 8px;">
                            <div style="font-size: 2rem; font-weight: 700; color: #f59e0b;">
                                <?php echo $tournament_stats['total_matches'] - $tournament_stats['completed_matches']; ?>
                            </div>
                            <div style="color: #6b7280; font-size: 0.9rem;">Matches Remaining</div>
                        </div>
                        <div style="text-align: center; padding: 1rem; background: #f8fafc; border-radius: 8px;">
                            <div style="font-size: 2rem; font-weight: 700; color: #ef4444;">
                                <?php echo count(array_filter($standings, fn($s) => $s['current_status'] === 'eliminated')); ?>
                            </div>
                            <div style="color: #6b7280; font-size: 0.9rem;">Eliminated</div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Add Participant Modal -->
        <div id="addParticipantModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">Add Participant</h3>
                    <button class="modal-close" onclick="closeModal('addParticipantModal')">&times;</button>
                </div>
                <form id="addParticipantForm">
                    <div class="form-group">
                        <label class="form-label" for="participantName">Name</label>
                        <input type="text" id="participantName" class="form-input" required
                               placeholder="e.g., John Smith / Mike Johnson">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="participantClub">Club/Team</label>
                        <input type="text" id="participantClub" class="form-input" required
                               placeholder="e.g., Engineering Department">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="participantSeed">Seed (Optional)</label>
                        <input type="number" id="participantSeed" class="form-input" min="1" max="64"
                               placeholder="Leave empty for auto-assignment">
                    </div>
                </form>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addParticipantModal')">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="addParticipant()">
                        <i class="fas fa-plus"></i> Add Participant
                    </button>
                </div>
            </div>
        </div>

        <!-- Edit Participant Modal -->
        <div id="editParticipantModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">Edit Participant</h3>
                    <button class="modal-close" onclick="closeModal('editParticipantModal')">&times;</button>
                </div>
                <form id="editParticipantForm">
                    <input type="hidden" id="editParticipantId">
                    <div class="form-group">
                        <label class="form-label" for="editParticipantName">Name</label>
                        <input type="text" id="editParticipantName" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="editParticipantClub">Club/Team</label>
                        <input type="text" id="editParticipantClub" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="editParticipantSeed">Seed</label>
                        <input type="number" id="editParticipantSeed" class="form-input" min="1" max="64">
                    </div>
                </form>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('editParticipantModal')">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="updateParticipant()">
                        <i class="fas fa-save"></i> Update Participant
                    </button>
                </div>
            </div>
        </div>

        <!-- Reschedule Match Modal -->
        <div id="rescheduleModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">Reschedule Match</h3>
                    <button class="modal-close" onclick="closeModal('rescheduleModal')">&times;</button>
                </div>
                <form id="rescheduleForm">
                    <input type="hidden" id="rescheduleMatchId">
                    <div class="form-group">
                        <label class="form-label" for="rescheduleDate">Date</label>
                        <input type="date" id="rescheduleDate" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="rescheduleTime">Time</label>
                        <input type="time" id="rescheduleTime" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="rescheduleVenue">Venue (Optional)</label>
                        <input type="text" id="rescheduleVenue" class="form-input"
                               placeholder="Leave empty to use default venue">
                    </div>
                </form>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('rescheduleModal')">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="updateMatchSchedule()">
                        <i class="fas fa-calendar-check"></i> Reschedule
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        const categoryId = <?php echo $category_id; ?>;
        const eventId = <?php echo $event_id; ?>;

        // Tab switching functionality
        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName + '-tab').classList.add('active');

            // Add active class to selected tab button
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // Load tab-specific data
            loadTabData(tabName);
        }

        // Load data for specific tab
        function loadTabData(tabName) {
            switch(tabName) {
                case 'fixtures':
                    loadFixtures();
                    break;
                case 'standing':
                    loadStandings();
                    break;
            }
        }

        // Modal functions
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }

        // Overview tab functions
        function createTournament() {
            const tournamentFormat = document.getElementById('tournamentFormat')?.value;
            const seedingMethod = document.getElementById('seedingMethod')?.value || 'random';
            const tournamentName = document.getElementById('tournamentName')?.value;
            const thirdPlaceMatch = document.getElementById('thirdPlaceMatch')?.checked || false;

            if (!tournamentFormat) {
                showNotification('Please select a tournament format', 'error');
                return;
            }

            if (!tournamentName) {
                showNotification('Please enter a tournament name', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('action', 'create_tournament');
            formData.append('event_sport_id', <?php echo $category['event_sport_id']; ?>);
            formData.append('format_id', tournamentFormat);
            formData.append('tournament_name', tournamentName);
            formData.append('seeding_method', seedingMethod);
            formData.append('points_win', 3);
            formData.append('points_draw', 1);
            formData.append('points_loss', 0);

            // Show loading state
            const createBtn = event.target;
            const originalText = createBtn.innerHTML;
            createBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';
            createBtn.disabled = true;

            fetch('ajax/tournament-management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Tournament created successfully!', 'success');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showNotification(data.message || 'Failed to create tournament', 'error');
                    createBtn.innerHTML = originalText;
                    createBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error creating tournament', 'error');
                createBtn.innerHTML = originalText;
                createBtn.disabled = false;
            });
        }

        // Participant management functions
        function openAddParticipantModal() {
            document.getElementById('addParticipantForm').reset();
            openModal('addParticipantModal');
        }

        function addParticipant() {
            const formData = {
                name: document.getElementById('participantName').value,
                club: document.getElementById('participantClub').value,
                seed: document.getElementById('participantSeed').value || null
            };

            if (!formData.name || !formData.club) {
                showNotification('Please fill in all required fields', 'error');
                return;
            }

            // Simulate API call
            fetch(`/api/category/${categoryId}/participants`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Participant added successfully!', 'success');
                    closeModal('addParticipantModal');
                    refreshParticipants();
                } else {
                    showNotification('Failed to add participant', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Participant added successfully! (Demo mode)', 'success');
                closeModal('addParticipantModal');
                // In demo mode, add to table
                addParticipantToTable(formData);
            });
        }

        function editParticipant(participantId) {
            // Get participant data (in real app, fetch from API)
            const row = document.querySelector(`tr[data-participant-id="${participantId}"]`);
            const name = row.cells[1].textContent;
            const club = row.cells[2].textContent;
            const seedElement = row.cells[0].querySelector('span');
            const seed = seedElement && seedElement.textContent.includes('#') ?
                         seedElement.textContent.replace('#', '') : '';

            // Populate edit form
            document.getElementById('editParticipantId').value = participantId;
            document.getElementById('editParticipantName').value = name;
            document.getElementById('editParticipantClub').value = club;
            document.getElementById('editParticipantSeed').value = seed;

            openModal('editParticipantModal');
        }

        function updateParticipant() {
            const participantId = document.getElementById('editParticipantId').value;
            const formData = {
                name: document.getElementById('editParticipantName').value,
                club: document.getElementById('editParticipantClub').value,
                seed: document.getElementById('editParticipantSeed').value || null
            };

            // Simulate API call
            fetch(`/api/category/${categoryId}/participants/${participantId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Participant updated successfully!', 'success');
                    closeModal('editParticipantModal');
                    refreshParticipants();
                } else {
                    showNotification('Failed to update participant', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Participant updated successfully! (Demo mode)', 'success');
                closeModal('editParticipantModal');
            });
        }

        function removeParticipant(participantId) {
            if (!confirm('Are you sure you want to remove this participant?')) {
                return;
            }

            // Simulate API call
            fetch(`/api/category/${categoryId}/participants/${participantId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Participant removed successfully!', 'success');
                    refreshParticipants();
                } else {
                    showNotification('Failed to remove participant', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Participant removed successfully! (Demo mode)', 'success');
                // In demo mode, remove from table
                document.querySelector(`tr[data-participant-id="${participantId}"]`).remove();
            });
        }

        function refreshParticipants() {
            // In a real app, this would reload the participants table
            showNotification('Participants refreshed', 'info');
        }

        // Fixtures tab functions
        function loadFixtures() {
            // Load fixture data if needed
            console.log('Loading fixtures...');
        }

        function generateBracket() {
            if (!confirm('This will regenerate the entire bracket. Continue?')) {
                return;
            }

            showNotification('Bracket regenerated successfully!', 'success');
        }

        function scheduleMatches() {
            showNotification('Match scheduling interface would open here', 'info');
        }

        function saveMatchScore(matchId) {
            const matchCard = document.querySelector(`[onclick="saveMatchScore(${matchId})"]`).closest('.match-card');
            const scoreInputs = matchCard.querySelectorAll('.score-input');
            const scores = Array.from(scoreInputs).map(input => input.value);

            // Simulate API call
            fetch(`/api/category/${categoryId}/match/${matchId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ scores: scores })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Match score saved successfully!', 'success');
                    loadStandings(); // Refresh standings
                } else {
                    showNotification('Failed to save match score', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Match score saved successfully! (Demo mode)', 'success');
            });
        }

        function rescheduleMatch(matchId) {
            document.getElementById('rescheduleMatchId').value = matchId;

            // Set current date/time as default
            const now = new Date();
            document.getElementById('rescheduleDate').value = now.toISOString().split('T')[0];
            document.getElementById('rescheduleTime').value = now.toTimeString().slice(0, 5);

            openModal('rescheduleModal');
        }

        function updateMatchSchedule() {
            const matchId = document.getElementById('rescheduleMatchId').value;
            const formData = {
                date: document.getElementById('rescheduleDate').value,
                time: document.getElementById('rescheduleTime').value,
                venue: document.getElementById('rescheduleVenue').value
            };

            // Simulate API call
            fetch(`/api/category/${categoryId}/match/${matchId}/schedule`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Match rescheduled successfully!', 'success');
                    closeModal('rescheduleModal');
                    loadFixtures();
                } else {
                    showNotification('Failed to reschedule match', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Match rescheduled successfully! (Demo mode)', 'success');
                closeModal('rescheduleModal');
            });
        }

        // Standing tab functions
        function loadStandings() {
            // Load standings data if needed
            console.log('Loading standings...');
        }

        function filterStandings() {
            const filter = document.getElementById('standingsFilter').value;
            const rows = document.querySelectorAll('#standingsTable tbody tr');

            rows.forEach((row, index) => {
                switch(filter) {
                    case 'top8':
                        row.style.display = index < 8 ? '' : 'none';
                        break;
                    case 'active':
                        const status = row.cells[7].textContent.trim();
                        row.style.display = status === 'Active' ? '' : 'none';
                        break;
                    default:
                        row.style.display = '';
                }
            });

            showNotification(`Showing ${filter} participants`, 'info');
        }

        function sortStandings(column) {
            const table = document.getElementById('standingsTable');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Simple sorting logic (in real app, this would be more sophisticated)
            rows.sort((a, b) => {
                let aVal, bVal;

                switch(column) {
                    case 'rank':
                        aVal = parseInt(a.cells[0].textContent.replace(/\D/g, ''));
                        bVal = parseInt(b.cells[0].textContent.replace(/\D/g, ''));
                        break;
                    case 'name':
                        aVal = a.cells[1].textContent;
                        bVal = b.cells[1].textContent;
                        return aVal.localeCompare(bVal);
                    case 'wins':
                        aVal = parseInt(a.cells[2].textContent);
                        bVal = parseInt(b.cells[2].textContent);
                        break;
                    case 'losses':
                        aVal = parseInt(a.cells[3].textContent);
                        bVal = parseInt(b.cells[3].textContent);
                        break;
                    case 'sets_won':
                        aVal = parseInt(a.cells[4].textContent);
                        bVal = parseInt(b.cells[4].textContent);
                        break;
                    case 'sets_lost':
                        aVal = parseInt(a.cells[5].textContent);
                        bVal = parseInt(b.cells[5].textContent);
                        break;
                    case 'points':
                        aVal = parseInt(a.cells[6].textContent);
                        bVal = parseInt(b.cells[6].textContent);
                        break;
                    default:
                        return 0;
                }

                return bVal - aVal; // Descending order
            });

            // Clear and re-append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            showNotification(`Sorted by ${column}`, 'info');
        }

        function refreshStandings() {
            showNotification('Standings refreshed', 'info');
            loadStandings();
        }

        function exportStandings() {
            // In a real app, this would generate and download a file
            showNotification('Standings exported successfully!', 'success');
        }

        // Utility functions
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                transition: all 0.3s ease;
            `;

            // Set background color based on type
            switch(type) {
                case 'success':
                    notification.style.background = '#10b981';
                    break;
                case 'error':
                    notification.style.background = '#ef4444';
                    break;
                case 'warning':
                    notification.style.background = '#f59e0b';
                    break;
                default:
                    notification.style.background = '#3b82f6';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            // Remove notification after 3 seconds
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        function addParticipantToTable(participant) {
            const tbody = document.querySelector('#participantsTable tbody');
            const newRow = document.createElement('tr');
            const newId = Date.now(); // Simple ID generation for demo

            newRow.setAttribute('data-participant-id', newId);
            newRow.innerHTML = `
                <td>
                    ${participant.seed ?
                        `<span style="background: #3b82f6; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-weight: 600;">#${participant.seed}</span>` :
                        '<span style="color: #6b7280;">-</span>'
                    }
                </td>
                <td style="font-weight: 500;">${participant.name}</td>
                <td>${participant.club}</td>
                <td>
                    <span style="background: #10b981; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                        Active
                    </span>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-secondary" onclick="editParticipant(${newId})">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button type="button" class="btn btn-sm" style="background: #ef4444; color: white; margin-left: 0.5rem;" onclick="removeParticipant(${newId})">
                        <i class="fas fa-trash"></i> Remove
                    </button>
                </td>
            `;

            tbody.appendChild(newRow);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Tournament Category Management Page loaded');

            // Load initial data for active tab
            loadTabData('overview');

            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case '1':
                            e.preventDefault();
                            switchTab('overview');
                            break;
                        case '2':
                            e.preventDefault();
                            switchTab('fixtures');
                            break;
                        case '3':
                            e.preventDefault();
                            switchTab('standing');
                            break;
                        case 's':
                            e.preventDefault();
                            saveSettings();
                            break;
                    }
                }
            });
        });
    </script>
</body>
</html>
