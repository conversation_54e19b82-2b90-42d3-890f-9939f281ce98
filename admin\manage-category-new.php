<?php
/**
 * Streamlined Tournament Category Management Page
 * Features: Overview, Fixtures, Standing tabs
 */

require_once '../includes/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get category ID from URL
$category_id = $_GET['category_id'] ?? 1;
$event_id = $_GET['event_id'] ?? 1;

// Sample data for demonstration
$category = [
    'id' => $category_id,
    'category_name' => 'Mens Doubles',
    'sport_name' => 'Badminton',
    'sport_type' => 'team',
    'event_name' => 'TEST 33',
    'venue' => 'SC Audi',
    'category_status' => 'ongoing',
    'max_participants' => 16,
    'current_format' => 'single_elimination',
    'settings' => [
        'third_place_match' => true,
        'auto_seeding' => false,
        'sets_to_win' => 2,
        'points_per_set' => 21
    ]
];

$participants = [
    ['id' => 1, 'name' => '<PERSON> / <PERSON>', 'club' => 'Engineering Dept', 'seed' => 1, 'status' => 'active'],
    ['id' => 2, 'name' => '<PERSON> / <PERSON>', 'club' => 'Business Dept', 'seed' => 2, 'status' => 'active'],
    ['id' => 3, 'name' => 'David Lee / Chris Taylor', 'club' => 'Arts & Sciences', 'seed' => 3, 'status' => 'active'],
    ['id' => 4, 'name' => 'Mark Davis / Kevin Park', 'club' => 'Medicine Dept', 'seed' => 4, 'status' => 'active'],
    ['id' => 5, 'name' => 'Robert Jones / James Miller', 'club' => 'Law Dept', 'seed' => null, 'status' => 'active']
];

$matches = [
    [
        'id' => 1, 'round' => 1, 'match_number' => 1,
        'player_a_id' => 1, 'player_b_id' => 4,
        'player_a_name' => 'John Smith / Mike Johnson',
        'player_b_name' => 'Mark Davis / Kevin Park',
        'score_a' => [21, 19, 21], 'score_b' => [18, 21, 15],
        'sets_a' => 2, 'sets_b' => 1, 'status' => 'completed',
        'scheduled_time' => '2024-01-15 10:00:00', 'winner_id' => 1
    ],
    [
        'id' => 2, 'round' => 1, 'match_number' => 2,
        'player_a_id' => 2, 'player_b_id' => 3,
        'player_a_name' => 'Alex Brown / Tom Wilson',
        'player_b_name' => 'David Lee / Chris Taylor',
        'score_a' => [21, 21], 'score_b' => [15, 18],
        'sets_a' => 2, 'sets_b' => 0, 'status' => 'completed',
        'scheduled_time' => '2024-01-15 11:00:00', 'winner_id' => 2
    ],
    [
        'id' => 3, 'round' => 2, 'match_number' => 1,
        'player_a_id' => 1, 'player_b_id' => 2,
        'player_a_name' => 'John Smith / Mike Johnson',
        'player_b_name' => 'Alex Brown / Tom Wilson',
        'score_a' => [], 'score_b' => [],
        'sets_a' => 0, 'sets_b' => 0, 'status' => 'scheduled',
        'scheduled_time' => '2024-01-15 14:00:00', 'winner_id' => null
    ]
];

$tournament_formats = [
    'single_elimination' => 'Single Elimination',
    'double_elimination' => 'Double Elimination',
    'round_robin' => 'Round Robin',
    'swiss_system' => 'Swiss System'
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($category['category_name']); ?> - SC_IMS Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Header */
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            opacity: 0.9;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .status-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            text-transform: uppercase;
            font-weight: 500;
        }

        /* Tabs */
        .tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .tab-button {
            flex: 1;
            padding: 1rem 2rem;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .tab-button:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .tab-button.active {
            background: #3b82f6;
            color: white;
        }

        /* Tab Content */
        .tab-content {
            display: none;
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .tab-content.active {
            display: block;
        }

        /* Form Styles */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
        }

        .form-input, .form-select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .checkbox-group input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #3b82f6;
        }

        /* Button Styles */
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        /* Table Styles */
        .table-container {
            overflow-x: auto;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .table th,
        .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            margin-top: 2rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .tabs {
                flex-direction: column;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .page-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title"><?php echo htmlspecialchars($category['category_name']); ?></h1>
            <div class="page-subtitle">
                <span><i class="fas fa-trophy"></i> <?php echo htmlspecialchars($category['sport_name']); ?></span>
                <span><i class="fas fa-calendar"></i> <?php echo htmlspecialchars($category['event_name']); ?></span>
                <span><i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($category['venue']); ?></span>
                <span class="status-badge"><?php echo htmlspecialchars($category['category_status']); ?></span>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="tabs">
            <button class="tab-button active" onclick="switchTab('overview')" data-tab="overview">
                <i class="fas fa-cog"></i>
                Overview
            </button>
            <button class="tab-button" onclick="switchTab('fixtures')" data-tab="fixtures">
                <i class="fas fa-calendar-alt"></i>
                Fixtures
            </button>
            <button class="tab-button" onclick="switchTab('standing')" data-tab="standing">
                <i class="fas fa-trophy"></i>
                Standing
            </button>
        </div>

        <!-- Overview Tab -->
        <div id="overview-tab" class="tab-content active">
            <h2 style="margin-bottom: 2rem; color: #1f2937;">
                <i class="fas fa-cog"></i> Tournament Configuration
            </h2>

            <!-- Category Settings Form -->
            <form id="categoryForm" class="form-grid">
                <div>
                    <div class="form-group">
                        <label class="form-label" for="categoryName">Category Name</label>
                        <input type="text" id="categoryName" class="form-input"
                               value="<?php echo htmlspecialchars($category['category_name']); ?>" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="sportFormat">Sport Format</label>
                        <select id="sportFormat" class="form-select" required>
                            <?php foreach ($tournament_formats as $key => $name): ?>
                                <option value="<?php echo $key; ?>"
                                        <?php echo $category['current_format'] === $key ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="maxParticipants">Max Participants</label>
                        <input type="number" id="maxParticipants" class="form-input"
                               value="<?php echo $category['max_participants']; ?>" min="2" max="64">
                    </div>
                </div>

                <div>
                    <div class="form-group">
                        <label class="form-label">Tournament Settings</label>

                        <div class="checkbox-group" style="margin-bottom: 1rem;">
                            <input type="checkbox" id="thirdPlaceMatch"
                                   <?php echo $category['settings']['third_place_match'] ? 'checked' : ''; ?>>
                            <label for="thirdPlaceMatch">Enable Third Place Match</label>
                        </div>

                        <div class="checkbox-group" style="margin-bottom: 1rem;">
                            <input type="checkbox" id="autoSeeding"
                                   <?php echo $category['settings']['auto_seeding'] ? 'checked' : ''; ?>>
                            <label for="autoSeeding">Auto-Seeding</label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="setsToWin">Sets to Win</label>
                        <input type="number" id="setsToWin" class="form-input"
                               value="<?php echo $category['settings']['sets_to_win']; ?>" min="1" max="5">
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="pointsPerSet">Points per Set</label>
                        <input type="number" id="pointsPerSet" class="form-input"
                               value="<?php echo $category['settings']['points_per_set']; ?>" min="11" max="30">
                    </div>
                </div>
            </form>

            <div style="margin-bottom: 2rem;">
                <button type="button" class="btn btn-primary" onclick="saveSettings()">
                    <i class="fas fa-save"></i> Save Settings
                </button>
            </div>

            <!-- Participants Section -->
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                <h3 style="color: #1f2937;">
                    <i class="fas fa-users"></i> Participants (<?php echo count($participants); ?>)
                </h3>
                <button type="button" class="btn btn-primary" onclick="openAddParticipantModal()">
                    <i class="fas fa-plus"></i> Add Participant
                </button>
            </div>

            <div class="table-container">
                <table class="table" id="participantsTable">
                    <thead>
                        <tr>
                            <th>Seed</th>
                            <th>Name</th>
                            <th>Club/Team</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($participants as $participant): ?>
                            <tr data-participant-id="<?php echo $participant['id']; ?>">
                                <td>
                                    <?php if ($participant['seed']): ?>
                                        <span style="background: #3b82f6; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-weight: 600;">
                                            #<?php echo $participant['seed']; ?>
                                        </span>
                                    <?php else: ?>
                                        <span style="color: #6b7280;">-</span>
                                    <?php endif; ?>
                                </td>
                                <td style="font-weight: 500;"><?php echo htmlspecialchars($participant['name']); ?></td>
                                <td><?php echo htmlspecialchars($participant['club']); ?></td>
                                <td>
                                    <span style="background: #10b981; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                                        <?php echo ucfirst($participant['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-secondary"
                                            onclick="editParticipant(<?php echo $participant['id']; ?>)">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    <button type="button" class="btn btn-sm" style="background: #ef4444; color: white; margin-left: 0.5rem;"
                                            onclick="removeParticipant(<?php echo $participant['id']; ?>)">
                                        <i class="fas fa-trash"></i> Remove
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Fixtures Tab -->
        <div id="fixtures-tab" class="tab-content">
            <h2 style="margin-bottom: 2rem; color: #1f2937;">
                <i class="fas fa-calendar-alt"></i> Tournament Fixtures
            </h2>

            <!-- Tournament Bracket -->
            <div id="tournamentBracket">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                    <h3 style="color: #1f2937;">Single Elimination Bracket</h3>
                    <div>
                        <button type="button" class="btn btn-secondary" onclick="generateBracket()">
                            <i class="fas fa-sync"></i> Regenerate Bracket
                        </button>
                        <button type="button" class="btn btn-primary" onclick="scheduleMatches()">
                            <i class="fas fa-calendar-plus"></i> Schedule Matches
                        </button>
                    </div>
                </div>

                <!-- Bracket Display -->
                <div class="bracket-container" style="overflow-x: auto; padding: 1rem; background: #f9fafb; border-radius: 8px;">
                    <div class="bracket-rounds" style="display: flex; gap: 3rem; min-width: 800px;">

                        <!-- Round 1 -->
                        <div class="bracket-round">
                            <h4 style="text-align: center; margin-bottom: 1rem; color: #374151;">Round 1</h4>
                            <div class="matches" style="display: flex; flex-direction: column; gap: 2rem;">

                                <!-- Match 1 -->
                                <div class="match-card" style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; min-width: 250px;">
                                    <div class="match-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                                        <span style="font-weight: 600; color: #374151;">Match 1</span>
                                        <span style="font-size: 0.8rem; color: #6b7280;">Jan 15, 10:00 AM</span>
                                    </div>

                                    <div class="match-players">
                                        <div class="player" style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem; background: #f3f4f6; border-radius: 4px; margin-bottom: 0.5rem;">
                                            <span style="font-weight: 500;">John Smith / Mike Johnson</span>
                                            <div class="score" style="display: flex; gap: 0.25rem;">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="21" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="19" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="21" min="0" max="30">
                                            </div>
                                        </div>

                                        <div class="player" style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem; background: #f3f4f6; border-radius: 4px;">
                                            <span style="font-weight: 500;">Mark Davis / Kevin Park</span>
                                            <div class="score" style="display: flex; gap: 0.25rem;">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="18" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="21" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="15" min="0" max="30">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="match-actions" style="display: flex; justify-content: space-between; align-items: center; margin-top: 1rem;">
                                        <span style="font-size: 0.8rem; color: #10b981; font-weight: 500;">
                                            <i class="fas fa-check-circle"></i> Completed
                                        </span>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-secondary" onclick="rescheduleMatch(1)">
                                                <i class="fas fa-clock"></i> Reschedule
                                            </button>
                                            <button type="button" class="btn btn-sm btn-primary" onclick="saveMatchScore(1)">
                                                <i class="fas fa-save"></i> Save
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Match 2 -->
                                <div class="match-card" style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; min-width: 250px;">
                                    <div class="match-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                                        <span style="font-weight: 600; color: #374151;">Match 2</span>
                                        <span style="font-size: 0.8rem; color: #6b7280;">Jan 15, 11:00 AM</span>
                                    </div>

                                    <div class="match-players">
                                        <div class="player" style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem; background: #f3f4f6; border-radius: 4px; margin-bottom: 0.5rem;">
                                            <span style="font-weight: 500;">Alex Brown / Tom Wilson</span>
                                            <div class="score" style="display: flex; gap: 0.25rem;">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="21" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="21" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="" min="0" max="30">
                                            </div>
                                        </div>

                                        <div class="player" style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem; background: #f3f4f6; border-radius: 4px;">
                                            <span style="font-weight: 500;">David Lee / Chris Taylor</span>
                                            <div class="score" style="display: flex; gap: 0.25rem;">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="15" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="18" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="" min="0" max="30">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="match-actions" style="display: flex; justify-content: space-between; align-items: center; margin-top: 1rem;">
                                        <span style="font-size: 0.8rem; color: #10b981; font-weight: 500;">
                                            <i class="fas fa-check-circle"></i> Completed
                                        </span>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-secondary" onclick="rescheduleMatch(2)">
                                                <i class="fas fa-clock"></i> Reschedule
                                            </button>
                                            <button type="button" class="btn btn-sm btn-primary" onclick="saveMatchScore(2)">
                                                <i class="fas fa-save"></i> Save
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Round 2 (Finals) -->
                        <div class="bracket-round">
                            <h4 style="text-align: center; margin-bottom: 1rem; color: #374151;">Finals</h4>
                            <div class="matches" style="display: flex; flex-direction: column; gap: 2rem; justify-content: center; height: 100%;">

                                <!-- Final Match -->
                                <div class="match-card" style="background: white; border: 2px solid #f59e0b; border-radius: 8px; padding: 1rem; min-width: 250px;">
                                    <div class="match-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                                        <span style="font-weight: 600; color: #f59e0b;">
                                            <i class="fas fa-crown"></i> Final
                                        </span>
                                        <span style="font-size: 0.8rem; color: #6b7280;">Jan 15, 2:00 PM</span>
                                    </div>

                                    <div class="match-players">
                                        <div class="player" style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem; background: #fef3c7; border-radius: 4px; margin-bottom: 0.5rem;">
                                            <span style="font-weight: 500;">John Smith / Mike Johnson</span>
                                            <div class="score" style="display: flex; gap: 0.25rem;">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="" min="0" max="30">
                                            </div>
                                        </div>

                                        <div class="player" style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem; background: #fef3c7; border-radius: 4px;">
                                            <span style="font-weight: 500;">Alex Brown / Tom Wilson</span>
                                            <div class="score" style="display: flex; gap: 0.25rem;">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="" min="0" max="30">
                                                <input type="number" class="score-input" style="width: 40px; padding: 0.25rem; border: 1px solid #d1d5db; border-radius: 4px; text-align: center;" value="" min="0" max="30">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="match-actions" style="display: flex; justify-content: space-between; align-items: center; margin-top: 1rem;">
                                        <span style="font-size: 0.8rem; color: #f59e0b; font-weight: 500;">
                                            <i class="fas fa-clock"></i> Scheduled
                                        </span>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-secondary" onclick="rescheduleMatch(3)">
                                                <i class="fas fa-clock"></i> Reschedule
                                            </button>
                                            <button type="button" class="btn btn-sm btn-primary" onclick="saveMatchScore(3)">
                                                <i class="fas fa-save"></i> Save
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Standing Tab -->
        <div id="standing-tab" class="tab-content">
            <h2 style="margin-bottom: 2rem; color: #1f2937;">
                <i class="fas fa-trophy"></i> Tournament Standings
            </h2>

            <!-- Standings Controls -->
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                <div style="display: flex; gap: 1rem; align-items: center;">
                    <label style="font-weight: 500; color: #374151;">Filter:</label>
                    <select id="standingsFilter" class="form-select" style="width: auto;" onchange="filterStandings()">
                        <option value="all">All Participants</option>
                        <option value="top8">Top 8</option>
                        <option value="active">Active Only</option>
                    </select>
                </div>
                <div>
                    <button type="button" class="btn btn-secondary" onclick="refreshStandings()">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                    <button type="button" class="btn btn-primary" onclick="exportStandings()">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>

            <!-- Standings Table -->
            <div class="table-container">
                <table class="table" id="standingsTable">
                    <thead>
                        <tr>
                            <th style="cursor: pointer;" onclick="sortStandings('rank')">
                                Rank <i class="fas fa-sort"></i>
                            </th>
                            <th style="cursor: pointer;" onclick="sortStandings('name')">
                                Name <i class="fas fa-sort"></i>
                            </th>
                            <th style="cursor: pointer;" onclick="sortStandings('wins')">
                                Wins <i class="fas fa-sort"></i>
                            </th>
                            <th style="cursor: pointer;" onclick="sortStandings('losses')">
                                Losses <i class="fas fa-sort"></i>
                            </th>
                            <th style="cursor: pointer;" onclick="sortStandings('sets_won')">
                                Sets Won <i class="fas fa-sort"></i>
                            </th>
                            <th style="cursor: pointer;" onclick="sortStandings('sets_lost')">
                                Sets Lost <i class="fas fa-sort"></i>
                            </th>
                            <th style="cursor: pointer;" onclick="sortStandings('points')">
                                Points <i class="fas fa-sort"></i>
                            </th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="background: #fef3c7;">
                            <td>
                                <span style="background: #f59e0b; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-weight: 600;">
                                    <i class="fas fa-crown"></i> 1
                                </span>
                            </td>
                            <td style="font-weight: 600;">John Smith / Mike Johnson</td>
                            <td style="font-weight: 600; color: #10b981;">1</td>
                            <td>0</td>
                            <td style="color: #10b981;">2</td>
                            <td style="color: #ef4444;">1</td>
                            <td style="font-weight: 600; color: #3b82f6;">3</td>
                            <td>
                                <span style="background: #10b981; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                                    Active
                                </span>
                            </td>
                        </tr>
                        <tr style="background: #f3f4f6;">
                            <td>
                                <span style="background: #6b7280; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-weight: 600;">
                                    2
                                </span>
                            </td>
                            <td style="font-weight: 600;">Alex Brown / Tom Wilson</td>
                            <td style="font-weight: 600; color: #10b981;">1</td>
                            <td>0</td>
                            <td style="color: #10b981;">2</td>
                            <td style="color: #ef4444;">0</td>
                            <td style="font-weight: 600; color: #3b82f6;">3</td>
                            <td>
                                <span style="background: #10b981; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                                    Active
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <span style="background: #cd7f32; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-weight: 600;">
                                    3
                                </span>
                            </td>
                            <td>David Lee / Chris Taylor</td>
                            <td>0</td>
                            <td style="color: #ef4444;">1</td>
                            <td>0</td>
                            <td style="color: #ef4444;">2</td>
                            <td>0</td>
                            <td>
                                <span style="background: #ef4444; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                                    Eliminated
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <span style="background: #6b7280; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-weight: 600;">
                                    4
                                </span>
                            </td>
                            <td>Mark Davis / Kevin Park</td>
                            <td>0</td>
                            <td style="color: #ef4444;">1</td>
                            <td style="color: #10b981;">1</td>
                            <td style="color: #ef4444;">2</td>
                            <td>0</td>
                            <td>
                                <span style="background: #ef4444; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                                    Eliminated
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <span style="background: #6b7280; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-weight: 600;">
                                    5
                                </span>
                            </td>
                            <td>Robert Jones / James Miller</td>
                            <td>0</td>
                            <td>0</td>
                            <td>0</td>
                            <td>0</td>
                            <td>0</td>
                            <td>
                                <span style="background: #6b7280; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                                    Waiting
                                </span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Standings Summary -->
            <div style="margin-top: 2rem; padding: 1.5rem; background: #f9fafb; border-radius: 8px; border: 1px solid #e5e7eb;">
                <h4 style="margin-bottom: 1rem; color: #1f2937;">Tournament Progress</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <div style="text-align: center;">
                        <div style="font-size: 2rem; font-weight: 700; color: #3b82f6;">2/3</div>
                        <div style="color: #6b7280; font-size: 0.9rem;">Matches Completed</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 2rem; font-weight: 700; color: #10b981;">2</div>
                        <div style="color: #6b7280; font-size: 0.9rem;">Active Players</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 2rem; font-weight: 700; color: #f59e0b;">1</div>
                        <div style="color: #6b7280; font-size: 0.9rem;">Matches Remaining</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 2rem; font-weight: 700; color: #ef4444;">2</div>
                        <div style="color: #6b7280; font-size: 0.9rem;">Eliminated</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add Participant Modal -->
        <div id="addParticipantModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">Add Participant</h3>
                    <button class="modal-close" onclick="closeModal('addParticipantModal')">&times;</button>
                </div>
                <form id="addParticipantForm">
                    <div class="form-group">
                        <label class="form-label" for="participantName">Name</label>
                        <input type="text" id="participantName" class="form-input" required
                               placeholder="e.g., John Smith / Mike Johnson">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="participantClub">Club/Team</label>
                        <input type="text" id="participantClub" class="form-input" required
                               placeholder="e.g., Engineering Department">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="participantSeed">Seed (Optional)</label>
                        <input type="number" id="participantSeed" class="form-input" min="1" max="64"
                               placeholder="Leave empty for auto-assignment">
                    </div>
                </form>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addParticipantModal')">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="addParticipant()">
                        <i class="fas fa-plus"></i> Add Participant
                    </button>
                </div>
            </div>
        </div>

        <!-- Edit Participant Modal -->
        <div id="editParticipantModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">Edit Participant</h3>
                    <button class="modal-close" onclick="closeModal('editParticipantModal')">&times;</button>
                </div>
                <form id="editParticipantForm">
                    <input type="hidden" id="editParticipantId">
                    <div class="form-group">
                        <label class="form-label" for="editParticipantName">Name</label>
                        <input type="text" id="editParticipantName" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="editParticipantClub">Club/Team</label>
                        <input type="text" id="editParticipantClub" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="editParticipantSeed">Seed</label>
                        <input type="number" id="editParticipantSeed" class="form-input" min="1" max="64">
                    </div>
                </form>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('editParticipantModal')">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="updateParticipant()">
                        <i class="fas fa-save"></i> Update Participant
                    </button>
                </div>
            </div>
        </div>

        <!-- Reschedule Match Modal -->
        <div id="rescheduleModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">Reschedule Match</h3>
                    <button class="modal-close" onclick="closeModal('rescheduleModal')">&times;</button>
                </div>
                <form id="rescheduleForm">
                    <input type="hidden" id="rescheduleMatchId">
                    <div class="form-group">
                        <label class="form-label" for="rescheduleDate">Date</label>
                        <input type="date" id="rescheduleDate" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="rescheduleTime">Time</label>
                        <input type="time" id="rescheduleTime" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="rescheduleVenue">Venue (Optional)</label>
                        <input type="text" id="rescheduleVenue" class="form-input"
                               placeholder="Leave empty to use default venue">
                    </div>
                </form>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('rescheduleModal')">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="updateMatchSchedule()">
                        <i class="fas fa-calendar-check"></i> Reschedule
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        const categoryId = <?php echo $category_id; ?>;
        const eventId = <?php echo $event_id; ?>;

        // Tab switching functionality
        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName + '-tab').classList.add('active');

            // Add active class to selected tab button
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // Load tab-specific data
            loadTabData(tabName);
        }

        // Load data for specific tab
        function loadTabData(tabName) {
            switch(tabName) {
                case 'fixtures':
                    loadFixtures();
                    break;
                case 'standing':
                    loadStandings();
                    break;
            }
        }

        // Modal functions
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }

        // Overview tab functions
        function saveSettings() {
            const formData = {
                category_name: document.getElementById('categoryName').value,
                sport_format: document.getElementById('sportFormat').value,
                max_participants: document.getElementById('maxParticipants').value,
                third_place_match: document.getElementById('thirdPlaceMatch').checked,
                auto_seeding: document.getElementById('autoSeeding').checked,
                sets_to_win: document.getElementById('setsToWin').value,
                points_per_set: document.getElementById('pointsPerSet').value
            };

            // Simulate API call
            fetch(`/api/category/${categoryId}/settings`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Settings saved successfully!', 'success');
                } else {
                    showNotification('Failed to save settings', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Settings saved successfully! (Demo mode)', 'success');
            });
        }

        // Participant management functions
        function openAddParticipantModal() {
            document.getElementById('addParticipantForm').reset();
            openModal('addParticipantModal');
        }

        function addParticipant() {
            const formData = {
                name: document.getElementById('participantName').value,
                club: document.getElementById('participantClub').value,
                seed: document.getElementById('participantSeed').value || null
            };

            if (!formData.name || !formData.club) {
                showNotification('Please fill in all required fields', 'error');
                return;
            }

            // Simulate API call
            fetch(`/api/category/${categoryId}/participants`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Participant added successfully!', 'success');
                    closeModal('addParticipantModal');
                    refreshParticipants();
                } else {
                    showNotification('Failed to add participant', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Participant added successfully! (Demo mode)', 'success');
                closeModal('addParticipantModal');
                // In demo mode, add to table
                addParticipantToTable(formData);
            });
        }

        function editParticipant(participantId) {
            // Get participant data (in real app, fetch from API)
            const row = document.querySelector(`tr[data-participant-id="${participantId}"]`);
            const name = row.cells[1].textContent;
            const club = row.cells[2].textContent;
            const seedElement = row.cells[0].querySelector('span');
            const seed = seedElement && seedElement.textContent.includes('#') ?
                         seedElement.textContent.replace('#', '') : '';

            // Populate edit form
            document.getElementById('editParticipantId').value = participantId;
            document.getElementById('editParticipantName').value = name;
            document.getElementById('editParticipantClub').value = club;
            document.getElementById('editParticipantSeed').value = seed;

            openModal('editParticipantModal');
        }

        function updateParticipant() {
            const participantId = document.getElementById('editParticipantId').value;
            const formData = {
                name: document.getElementById('editParticipantName').value,
                club: document.getElementById('editParticipantClub').value,
                seed: document.getElementById('editParticipantSeed').value || null
            };

            // Simulate API call
            fetch(`/api/category/${categoryId}/participants/${participantId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Participant updated successfully!', 'success');
                    closeModal('editParticipantModal');
                    refreshParticipants();
                } else {
                    showNotification('Failed to update participant', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Participant updated successfully! (Demo mode)', 'success');
                closeModal('editParticipantModal');
            });
        }

        function removeParticipant(participantId) {
            if (!confirm('Are you sure you want to remove this participant?')) {
                return;
            }

            // Simulate API call
            fetch(`/api/category/${categoryId}/participants/${participantId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Participant removed successfully!', 'success');
                    refreshParticipants();
                } else {
                    showNotification('Failed to remove participant', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Participant removed successfully! (Demo mode)', 'success');
                // In demo mode, remove from table
                document.querySelector(`tr[data-participant-id="${participantId}"]`).remove();
            });
        }

        function refreshParticipants() {
            // In a real app, this would reload the participants table
            showNotification('Participants refreshed', 'info');
        }

        // Fixtures tab functions
        function loadFixtures() {
            // Load fixture data if needed
            console.log('Loading fixtures...');
        }

        function generateBracket() {
            if (!confirm('This will regenerate the entire bracket. Continue?')) {
                return;
            }

            showNotification('Bracket regenerated successfully!', 'success');
        }

        function scheduleMatches() {
            showNotification('Match scheduling interface would open here', 'info');
        }

        function saveMatchScore(matchId) {
            const matchCard = document.querySelector(`[onclick="saveMatchScore(${matchId})"]`).closest('.match-card');
            const scoreInputs = matchCard.querySelectorAll('.score-input');
            const scores = Array.from(scoreInputs).map(input => input.value);

            // Simulate API call
            fetch(`/api/category/${categoryId}/match/${matchId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ scores: scores })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Match score saved successfully!', 'success');
                    loadStandings(); // Refresh standings
                } else {
                    showNotification('Failed to save match score', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Match score saved successfully! (Demo mode)', 'success');
            });
        }

        function rescheduleMatch(matchId) {
            document.getElementById('rescheduleMatchId').value = matchId;

            // Set current date/time as default
            const now = new Date();
            document.getElementById('rescheduleDate').value = now.toISOString().split('T')[0];
            document.getElementById('rescheduleTime').value = now.toTimeString().slice(0, 5);

            openModal('rescheduleModal');
        }

        function updateMatchSchedule() {
            const matchId = document.getElementById('rescheduleMatchId').value;
            const formData = {
                date: document.getElementById('rescheduleDate').value,
                time: document.getElementById('rescheduleTime').value,
                venue: document.getElementById('rescheduleVenue').value
            };

            // Simulate API call
            fetch(`/api/category/${categoryId}/match/${matchId}/schedule`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Match rescheduled successfully!', 'success');
                    closeModal('rescheduleModal');
                    loadFixtures();
                } else {
                    showNotification('Failed to reschedule match', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Match rescheduled successfully! (Demo mode)', 'success');
                closeModal('rescheduleModal');
            });
        }

        // Standing tab functions
        function loadStandings() {
            // Load standings data if needed
            console.log('Loading standings...');
        }

        function filterStandings() {
            const filter = document.getElementById('standingsFilter').value;
            const rows = document.querySelectorAll('#standingsTable tbody tr');

            rows.forEach((row, index) => {
                switch(filter) {
                    case 'top8':
                        row.style.display = index < 8 ? '' : 'none';
                        break;
                    case 'active':
                        const status = row.cells[7].textContent.trim();
                        row.style.display = status === 'Active' ? '' : 'none';
                        break;
                    default:
                        row.style.display = '';
                }
            });

            showNotification(`Showing ${filter} participants`, 'info');
        }

        function sortStandings(column) {
            const table = document.getElementById('standingsTable');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Simple sorting logic (in real app, this would be more sophisticated)
            rows.sort((a, b) => {
                let aVal, bVal;

                switch(column) {
                    case 'rank':
                        aVal = parseInt(a.cells[0].textContent.replace(/\D/g, ''));
                        bVal = parseInt(b.cells[0].textContent.replace(/\D/g, ''));
                        break;
                    case 'name':
                        aVal = a.cells[1].textContent;
                        bVal = b.cells[1].textContent;
                        return aVal.localeCompare(bVal);
                    case 'wins':
                        aVal = parseInt(a.cells[2].textContent);
                        bVal = parseInt(b.cells[2].textContent);
                        break;
                    case 'losses':
                        aVal = parseInt(a.cells[3].textContent);
                        bVal = parseInt(b.cells[3].textContent);
                        break;
                    case 'sets_won':
                        aVal = parseInt(a.cells[4].textContent);
                        bVal = parseInt(b.cells[4].textContent);
                        break;
                    case 'sets_lost':
                        aVal = parseInt(a.cells[5].textContent);
                        bVal = parseInt(b.cells[5].textContent);
                        break;
                    case 'points':
                        aVal = parseInt(a.cells[6].textContent);
                        bVal = parseInt(b.cells[6].textContent);
                        break;
                    default:
                        return 0;
                }

                return bVal - aVal; // Descending order
            });

            // Clear and re-append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            showNotification(`Sorted by ${column}`, 'info');
        }

        function refreshStandings() {
            showNotification('Standings refreshed', 'info');
            loadStandings();
        }

        function exportStandings() {
            // In a real app, this would generate and download a file
            showNotification('Standings exported successfully!', 'success');
        }

        // Utility functions
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                transition: all 0.3s ease;
            `;

            // Set background color based on type
            switch(type) {
                case 'success':
                    notification.style.background = '#10b981';
                    break;
                case 'error':
                    notification.style.background = '#ef4444';
                    break;
                case 'warning':
                    notification.style.background = '#f59e0b';
                    break;
                default:
                    notification.style.background = '#3b82f6';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            // Remove notification after 3 seconds
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        function addParticipantToTable(participant) {
            const tbody = document.querySelector('#participantsTable tbody');
            const newRow = document.createElement('tr');
            const newId = Date.now(); // Simple ID generation for demo

            newRow.setAttribute('data-participant-id', newId);
            newRow.innerHTML = `
                <td>
                    ${participant.seed ?
                        `<span style="background: #3b82f6; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-weight: 600;">#${participant.seed}</span>` :
                        '<span style="color: #6b7280;">-</span>'
                    }
                </td>
                <td style="font-weight: 500;">${participant.name}</td>
                <td>${participant.club}</td>
                <td>
                    <span style="background: #10b981; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                        Active
                    </span>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-secondary" onclick="editParticipant(${newId})">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button type="button" class="btn btn-sm" style="background: #ef4444; color: white; margin-left: 0.5rem;" onclick="removeParticipant(${newId})">
                        <i class="fas fa-trash"></i> Remove
                    </button>
                </td>
            `;

            tbody.appendChild(newRow);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Tournament Category Management Page loaded');

            // Load initial data for active tab
            loadTabData('overview');

            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case '1':
                            e.preventDefault();
                            switchTab('overview');
                            break;
                        case '2':
                            e.preventDefault();
                            switchTab('fixtures');
                            break;
                        case '3':
                            e.preventDefault();
                            switchTab('standing');
                            break;
                        case 's':
                            e.preventDefault();
                            saveSettings();
                            break;
                    }
                }
            });
        });
    </script>
</body>
</html>
