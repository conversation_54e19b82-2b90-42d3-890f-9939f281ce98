<?php
/**
 * Export Matches for Sport Category
 * SC_IMS Admin Panel
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get parameters
$category_id = isset($_GET['category_id']) ? (int)$_GET['category_id'] : 0;
$format = isset($_GET['format']) ? $_GET['format'] : 'excel';

if (!$category_id) {
    header('Location: events.php');
    exit;
}

// Get category details
$sql = "SELECT 
            sc.*,
            es.id as event_sport_id,
            es.event_id,
            es.sport_id,
            e.name as event_name,
            s.name as sport_name
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE sc.id = ?";

$stmt = $conn->prepare($sql);
$stmt->execute([$category_id]);
$category = $stmt->fetch();

if (!$category) {
    header('Location: events.php');
    exit;
}

// Get matches data
$sql = "SELECT 
            m.*,
            r1.team_name as team1_name,
            r1.department_id as team1_dept_id,
            d1.name as team1_dept_name,
            r2.team_name as team2_name,
            r2.department_id as team2_dept_id,
            d2.name as team2_dept_name,
            rw.team_name as winner_name,
            rw.department_id as winner_dept_id,
            dw.name as winner_dept_name,
            s.team1_score,
            s.team2_score
        FROM matches m
        LEFT JOIN registrations r1 ON m.team1_id = r1.id
        LEFT JOIN departments d1 ON r1.department_id = d1.id
        LEFT JOIN registrations r2 ON m.team2_id = r2.id
        LEFT JOIN departments d2 ON r2.department_id = d2.id
        LEFT JOIN registrations rw ON m.winner_id = rw.id
        LEFT JOIN departments dw ON rw.department_id = dw.id
        LEFT JOIN scores s ON m.id = s.match_id AND s.is_final = 1
        WHERE m.event_sport_id = ?
        ORDER BY m.round_number ASC, m.match_number ASC";

$stmt = $conn->prepare($sql);
$stmt->execute([$category['event_sport_id']]);
$matches = $stmt->fetchAll();

// Set headers for download
$filename = sanitizeFilename($category['event_name'] . '_' . $category['sport_name'] . '_' . $category['category_name'] . '_matches');

if ($format === 'excel') {
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
    
    // Output Excel format
    echo "<table border='1'>";
    echo "<tr>";
    echo "<th>Match ID</th>";
    echo "<th>Round</th>";
    echo "<th>Match Number</th>";
    echo "<th>Team 1</th>";
    echo "<th>Team 2</th>";
    echo "<th>Status</th>";
    echo "<th>Scheduled Time</th>";
    echo "<th>Venue</th>";
    echo "<th>Team 1 Score</th>";
    echo "<th>Team 2 Score</th>";
    echo "<th>Winner</th>";
    echo "</tr>";
    
    foreach ($matches as $match) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($match['id']) . "</td>";
        echo "<td>" . htmlspecialchars($match['round_number']) . "</td>";
        echo "<td>" . htmlspecialchars($match['match_number']) . "</td>";
        echo "<td>" . htmlspecialchars($match['team1_name'] ?: $match['team1_dept_name'] ?: 'TBD') . "</td>";
        echo "<td>" . htmlspecialchars($match['team2_name'] ?: $match['team2_dept_name'] ?: 'TBD') . "</td>";
        echo "<td>" . htmlspecialchars(ucfirst($match['status'])) . "</td>";
        echo "<td>" . htmlspecialchars($match['scheduled_time'] ? date('Y-m-d H:i', strtotime($match['scheduled_time'])) : 'Not scheduled') . "</td>";
        echo "<td>" . htmlspecialchars($match['venue'] ?: 'Not specified') . "</td>";
        echo "<td>" . htmlspecialchars($match['team1_score'] ?: '-') . "</td>";
        echo "<td>" . htmlspecialchars($match['team2_score'] ?: '-') . "</td>";
        echo "<td>" . htmlspecialchars($match['winner_name'] ?: $match['winner_dept_name'] ?: '-') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} else {
    // CSV format
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
    
    $output = fopen('php://output', 'w');
    
    // CSV headers
    fputcsv($output, [
        'Match ID',
        'Round',
        'Match Number',
        'Team 1',
        'Team 2',
        'Status',
        'Scheduled Time',
        'Venue',
        'Team 1 Score',
        'Team 2 Score',
        'Winner'
    ]);
    
    // CSV data
    foreach ($matches as $match) {
        fputcsv($output, [
            $match['id'],
            $match['round_number'],
            $match['match_number'],
            $match['team1_name'] ?: $match['team1_dept_name'] ?: 'TBD',
            $match['team2_name'] ?: $match['team2_dept_name'] ?: 'TBD',
            ucfirst($match['status']),
            $match['scheduled_time'] ? date('Y-m-d H:i', strtotime($match['scheduled_time'])) : 'Not scheduled',
            $match['venue'] ?: 'Not specified',
            $match['team1_score'] ?: '-',
            $match['team2_score'] ?: '-',
            $match['winner_name'] ?: $match['winner_dept_name'] ?: '-'
        ]);
    }
    
    fclose($output);
}

function sanitizeFilename($filename) {
    // Remove or replace invalid characters
    $filename = preg_replace('/[^a-zA-Z0-9_\-]/', '_', $filename);
    $filename = preg_replace('/_{2,}/', '_', $filename);
    return trim($filename, '_');
}

// Log admin activity
logAdminActivity('EXPORT_MATCHES', 'sport_categories', $category_id);
?>
