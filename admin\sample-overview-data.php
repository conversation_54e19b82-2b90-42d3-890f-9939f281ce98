<?php
/**
 * Sample data for demonstrating the tournament overview functionality
 * This file provides sample data for testing the enhanced manage-category.php page
 */

// Sample tournament formats data
$sample_formats = [
    [
        'id' => 1,
        'name' => 'Single Elimination',
        'description' => 'Traditional knockout tournament where teams are eliminated after one loss',
        'sport_type_category' => 'team',
        'min_participants' => 4,
        'max_participants' => 64,
        'requires_seeding' => true,
        'advancement_type' => 'elimination'
    ],
    [
        'id' => 2,
        'name' => 'Double Elimination',
        'description' => 'Tournament with winner\'s and loser\'s brackets, teams eliminated after two losses',
        'sport_type_category' => 'team',
        'min_participants' => 4,
        'max_participants' => 32,
        'requires_seeding' => true,
        'advancement_type' => 'elimination'
    ],
    [
        'id' => 3,
        'name' => 'Round Robin',
        'description' => 'Every team plays every other team once, winner determined by points',
        'sport_type_category' => 'team',
        'min_participants' => 3,
        'max_participants' => 12,
        'requires_seeding' => false,
        'advancement_type' => 'points'
    ],
    [
        'id' => 4,
        'name' => 'Swiss System',
        'description' => 'Pairing system where teams with similar records play each other',
        'sport_type_category' => 'academic',
        'min_participants' => 6,
        'max_participants' => null,
        'requires_seeding' => false,
        'advancement_type' => 'points'
    ],
    [
        'id' => 5,
        'name' => 'Judged Rounds',
        'description' => 'Performance-based competition with judges scoring participants',
        'sport_type_category' => 'judged',
        'min_participants' => 2,
        'max_participants' => null,
        'requires_seeding' => false,
        'advancement_type' => 'judged'
    ]
];

// Sample registrations data
$sample_registrations = [
    [
        'registration_id' => 1,
        'department_id' => 1,
        'department_name' => 'College of Engineering',
        'department_abbr' => 'COE',
        'registration_status' => 'confirmed',
        'color_code' => '#3b82f6',
        'participants' => json_encode([
            ['name' => 'John Smith', 'position' => 'Captain'],
            ['name' => 'Jane Doe', 'position' => 'Player'],
            ['name' => 'Mike Johnson', 'position' => 'Player'],
            ['name' => 'Sarah Wilson', 'position' => 'Player']
        ])
    ],
    [
        'registration_id' => 2,
        'department_id' => 2,
        'department_name' => 'College of Business',
        'department_abbr' => 'COB',
        'registration_status' => 'confirmed',
        'color_code' => '#10b981',
        'participants' => json_encode([
            ['name' => 'Alex Brown', 'position' => 'Captain'],
            ['name' => 'Lisa Garcia', 'position' => 'Player'],
            ['name' => 'Tom Davis', 'position' => 'Player']
        ])
    ],
    [
        'registration_id' => 3,
        'department_id' => 3,
        'department_name' => 'College of Arts and Sciences',
        'department_abbr' => 'CAS',
        'registration_status' => 'confirmed',
        'color_code' => '#f59e0b',
        'participants' => json_encode([
            ['name' => 'Emma Martinez', 'position' => 'Captain'],
            ['name' => 'David Lee', 'position' => 'Player'],
            ['name' => 'Rachel Kim', 'position' => 'Player'],
            ['name' => 'Chris Taylor', 'position' => 'Player'],
            ['name' => 'Amy Chen', 'position' => 'Player']
        ])
    ],
    [
        'registration_id' => 4,
        'department_id' => 4,
        'department_name' => 'College of Education',
        'department_abbr' => 'COEd',
        'registration_status' => 'pending',
        'color_code' => '#ef4444',
        'participants' => json_encode([
            ['name' => 'Mark Anderson', 'position' => 'Captain'],
            ['name' => 'Jennifer White', 'position' => 'Player']
        ])
    ],
    [
        'registration_id' => 5,
        'department_id' => 5,
        'department_name' => 'College of Medicine',
        'department_abbr' => 'COM',
        'registration_status' => 'confirmed',
        'color_code' => '#8b5cf6',
        'participants' => json_encode([
            ['name' => 'Dr. Robert Jones', 'position' => 'Captain'],
            ['name' => 'Dr. Maria Rodriguez', 'position' => 'Player'],
            ['name' => 'Dr. Kevin Park', 'position' => 'Player'],
            ['name' => 'Dr. Linda Thompson', 'position' => 'Player']
        ])
    ],
    [
        'registration_id' => 6,
        'department_id' => 6,
        'department_name' => 'College of Law',
        'department_abbr' => 'COL',
        'registration_status' => 'confirmed',
        'color_code' => '#06b6d4',
        'participants' => json_encode([
            ['name' => 'Attorney Sarah Johnson', 'position' => 'Captain'],
            ['name' => 'Attorney Michael Brown', 'position' => 'Player'],
            ['name' => 'Attorney Jessica Davis', 'position' => 'Player']
        ])
    ]
];

// Sample category data
$sample_category = [
    'event_sport_id' => 1,
    'category_id' => 1,
    'event_name' => 'Annual Inter-College Sports Festival 2024',
    'sport_name' => 'Basketball',
    'category_name' => 'Men\'s Basketball',
    'sport_type_category' => 'team',
    'category_status' => 'active',
    'max_participants' => 8,
    'venue' => 'Main Gymnasium',
    'referee_name' => 'Coach Michael Thompson',
    'referee_email' => '<EMAIL>'
];

// Sample tournament statistics
$sample_tournament_stats = [
    'tournament_exists' => false,
    'total_registrations' => 6,
    'confirmed_registrations' => 5,
    'pending_registrations' => 1,
    'current_round' => 0,
    'total_rounds' => 0
];

// Sample existing tournament data (when tournament exists)
$sample_existing_tournament = [
    'id' => 1,
    'tournament_name' => 'Men\'s Basketball Tournament',
    'format_name' => 'Single Elimination',
    'format_id' => 1,
    'seeding_method' => 'random',
    'status' => 'active',
    'created_at' => '2024-01-15 10:00:00'
];

// Function to get sample data
function getSampleData($type) {
    global $sample_formats, $sample_registrations, $sample_category, 
           $sample_tournament_stats, $sample_existing_tournament;
    
    switch($type) {
        case 'formats':
            return $sample_formats;
        case 'registrations':
            return $sample_registrations;
        case 'category':
            return $sample_category;
        case 'tournament_stats':
            return $sample_tournament_stats;
        case 'existing_tournament':
            return $sample_existing_tournament;
        default:
            return [];
    }
}

// Usage instructions
/*
To use this sample data in manage-category.php for testing:

1. Include this file at the top of manage-category.php:
   require_once 'sample-overview-data.php';

2. Replace database queries with sample data calls:
   $available_formats = getSampleData('formats');
   $registrations = getSampleData('registrations');
   $category = getSampleData('category');
   $tournament_stats = getSampleData('tournament_stats');

3. Set JavaScript variables:
   const eventSportId = 1;
   const categoryId = 1;
   const tournamentExists = false;

This will allow you to test the overview functionality without needing
a complete database setup.
*/
?>
