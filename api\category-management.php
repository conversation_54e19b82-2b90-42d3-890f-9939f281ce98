<?php
/**
 * API endpoints for category management
 * Handles AJAX requests for tournament category operations
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once '../includes/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Require admin authentication
try {
    requireAdmin();
} catch (Exception $e) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Parse the request URI
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);
$path_parts = explode('/', trim($path, '/'));

// Extract category ID and action from URL
$category_id = null;
$action = null;
$resource = null;
$resource_id = null;

// Parse URL pattern: /api/category/{id}/{action} or /api/category/{id}/{resource}/{id}
if (count($path_parts) >= 3 && $path_parts[0] === 'api' && $path_parts[1] === 'category') {
    $category_id = $path_parts[2];
    if (isset($path_parts[3])) {
        $action = $path_parts[3];
        if (isset($path_parts[4])) {
            $resource_id = $path_parts[4];
        }
    }
}

if (!$category_id) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Category ID required']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($action) {
        case 'settings':
            handleCategorySettings($category_id, $method);
            break;
            
        case 'participants':
            handleParticipants($category_id, $method, $resource_id);
            break;
            
        case 'match':
            handleMatch($category_id, $method, $resource_id);
            break;
            
        default:
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Endpoint not found']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

/**
 * Handle category settings operations
 */
function handleCategorySettings($category_id, $method) {
    global $conn;
    
    switch ($method) {
        case 'GET':
            // Get category settings
            $sql = "SELECT * FROM sport_categories WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$category_id]);
            $category = $stmt->fetch();
            
            if (!$category) {
                throw new Exception('Category not found');
            }
            
            echo json_encode(['success' => true, 'data' => $category]);
            break;
            
        case 'POST':
            // Update category settings
            $input = json_decode(file_get_contents('php://input'), true);
            
            $sql = "UPDATE sport_categories SET 
                        category_name = ?, 
                        max_participants = ?,
                        updated_at = NOW()
                    WHERE id = ?";
            
            $stmt = $conn->prepare($sql);
            $result = $stmt->execute([
                $input['category_name'],
                $input['max_participants'],
                $category_id
            ]);
            
            if ($result) {
                // Log admin activity
                logAdminActivity("Updated category settings for category ID: $category_id");
                echo json_encode(['success' => true, 'message' => 'Settings updated successfully']);
            } else {
                throw new Exception('Failed to update settings');
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
}

/**
 * Handle participants operations
 */
function handleParticipants($category_id, $method, $participant_id = null) {
    global $conn;
    
    switch ($method) {
        case 'GET':
            // Get participants list
            $sql = "SELECT * FROM tournament_participants WHERE category_id = ? ORDER BY seed ASC, name ASC";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$category_id]);
            $participants = $stmt->fetchAll();
            
            echo json_encode(['success' => true, 'data' => $participants]);
            break;
            
        case 'POST':
            // Add new participant
            $input = json_decode(file_get_contents('php://input'), true);
            
            // Validate input
            if (empty($input['name']) || empty($input['club'])) {
                throw new Exception('Name and club are required');
            }
            
            // Check if seed is already taken
            if (!empty($input['seed'])) {
                $sql = "SELECT COUNT(*) FROM tournament_participants WHERE category_id = ? AND seed = ?";
                $stmt = $conn->prepare($sql);
                $stmt->execute([$category_id, $input['seed']]);
                if ($stmt->fetchColumn() > 0) {
                    throw new Exception('Seed number already taken');
                }
            }
            
            $sql = "INSERT INTO tournament_participants (category_id, name, club, seed, status, created_at) 
                    VALUES (?, ?, ?, ?, 'active', NOW())";
            
            $stmt = $conn->prepare($sql);
            $result = $stmt->execute([
                $category_id,
                $input['name'],
                $input['club'],
                $input['seed'] ?: null
            ]);
            
            if ($result) {
                $participant_id = $conn->lastInsertId();
                logAdminActivity("Added participant: {$input['name']} to category ID: $category_id");
                echo json_encode(['success' => true, 'message' => 'Participant added successfully', 'id' => $participant_id]);
            } else {
                throw new Exception('Failed to add participant');
            }
            break;
            
        case 'PUT':
            // Update participant
            if (!$participant_id) {
                throw new Exception('Participant ID required');
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            // Check if seed is already taken by another participant
            if (!empty($input['seed'])) {
                $sql = "SELECT COUNT(*) FROM tournament_participants WHERE category_id = ? AND seed = ? AND id != ?";
                $stmt = $conn->prepare($sql);
                $stmt->execute([$category_id, $input['seed'], $participant_id]);
                if ($stmt->fetchColumn() > 0) {
                    throw new Exception('Seed number already taken');
                }
            }
            
            $sql = "UPDATE tournament_participants SET 
                        name = ?, 
                        club = ?, 
                        seed = ?,
                        updated_at = NOW()
                    WHERE id = ? AND category_id = ?";
            
            $stmt = $conn->prepare($sql);
            $result = $stmt->execute([
                $input['name'],
                $input['club'],
                $input['seed'] ?: null,
                $participant_id,
                $category_id
            ]);
            
            if ($result) {
                logAdminActivity("Updated participant ID: $participant_id in category ID: $category_id");
                echo json_encode(['success' => true, 'message' => 'Participant updated successfully']);
            } else {
                throw new Exception('Failed to update participant');
            }
            break;
            
        case 'DELETE':
            // Remove participant
            if (!$participant_id) {
                throw new Exception('Participant ID required');
            }
            
            $sql = "DELETE FROM tournament_participants WHERE id = ? AND category_id = ?";
            $stmt = $conn->prepare($sql);
            $result = $stmt->execute([$participant_id, $category_id]);
            
            if ($result) {
                logAdminActivity("Removed participant ID: $participant_id from category ID: $category_id");
                echo json_encode(['success' => true, 'message' => 'Participant removed successfully']);
            } else {
                throw new Exception('Failed to remove participant');
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
}

/**
 * Handle match operations
 */
function handleMatch($category_id, $method, $match_id = null) {
    global $conn;
    
    switch ($method) {
        case 'GET':
            // Get matches for category
            $sql = "SELECT * FROM tournament_matches WHERE category_id = ? ORDER BY round ASC, match_number ASC";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$category_id]);
            $matches = $stmt->fetchAll();
            
            echo json_encode(['success' => true, 'data' => $matches]);
            break;
            
        case 'PUT':
            // Update match (score or schedule)
            if (!$match_id) {
                throw new Exception('Match ID required');
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (isset($input['scores'])) {
                // Update match scores
                $sql = "UPDATE tournament_matches SET 
                            score_data = ?,
                            status = 'completed',
                            updated_at = NOW()
                        WHERE id = ? AND category_id = ?";
                
                $stmt = $conn->prepare($sql);
                $result = $stmt->execute([
                    json_encode($input['scores']),
                    $match_id,
                    $category_id
                ]);
                
                if ($result) {
                    logAdminActivity("Updated match score for match ID: $match_id");
                    echo json_encode(['success' => true, 'message' => 'Match score updated successfully']);
                } else {
                    throw new Exception('Failed to update match score');
                }
            } elseif (isset($input['date']) && isset($input['time'])) {
                // Update match schedule
                $scheduled_time = $input['date'] . ' ' . $input['time'];
                
                $sql = "UPDATE tournament_matches SET 
                            scheduled_time = ?,
                            venue = ?,
                            updated_at = NOW()
                        WHERE id = ? AND category_id = ?";
                
                $stmt = $conn->prepare($sql);
                $result = $stmt->execute([
                    $scheduled_time,
                    $input['venue'] ?: null,
                    $match_id,
                    $category_id
                ]);
                
                if ($result) {
                    logAdminActivity("Rescheduled match ID: $match_id");
                    echo json_encode(['success' => true, 'message' => 'Match rescheduled successfully']);
                } else {
                    throw new Exception('Failed to reschedule match');
                }
            } else {
                throw new Exception('Invalid update data');
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
}
?>
