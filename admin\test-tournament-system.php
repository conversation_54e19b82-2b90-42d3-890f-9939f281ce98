<?php
/**
 * Test Tournament System Setup
 */

try {
    $conn = new PDO("mysql:host=localhost;dbname=SC_IMS_db", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Tournament System Test</h2>\n";
    
    // Check tables
    $tables = ['tournament_formats', 'tournament_structures', 'tournament_rounds', 'tournament_participants'];
    echo "<h3>Database Tables:</h3>\n";
    foreach ($tables as $table) {
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        $exists = $stmt->fetch() ? '✅ EXISTS' : '❌ MISSING';
        echo "<p>$table: $exists</p>\n";
    }
    
    // Check tournament formats
    echo "<h3>Tournament Formats:</h3>\n";
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM tournament_formats");
        $result = $stmt->fetch();
        echo "<p>Tournament formats available: " . $result['count'] . "</p>\n";
        
        if ($result['count'] > 0) {
            $stmt = $conn->query("SELECT name, sport_type_category FROM tournament_formats LIMIT 5");
            $formats = $stmt->fetchAll();
            echo "<ul>\n";
            foreach ($formats as $format) {
                echo "<li>" . htmlspecialchars($format['name']) . " (" . $format['sport_type_category'] . ")</li>\n";
            }
            echo "</ul>\n";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error checking tournament formats: " . $e->getMessage() . "</p>\n";
    }
    
    // Check sport types
    echo "<h3>Sport Types:</h3>\n";
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM sport_types");
        $result = $stmt->fetch();
        echo "<p>Sport types available: " . $result['count'] . "</p>\n";
    } catch (Exception $e) {
        echo "<p>❌ Error checking sport types: " . $e->getMessage() . "</p>\n";
    }
    
    // Test tournament manager
    echo "<h3>Tournament Manager:</h3>\n";
    if (file_exists('../includes/tournament_manager.php')) {
        echo "<p>✅ tournament_manager.php exists</p>\n";
        
        require_once '../includes/tournament_manager.php';
        if (class_exists('TournamentManager')) {
            echo "<p>✅ TournamentManager class loaded</p>\n";
            
            try {
                $manager = new TournamentManager($conn);
                $formats = $manager->getAvailableFormats('team');
                echo "<p>✅ Can get tournament formats for team sports: " . count($formats) . " formats</p>\n";
            } catch (Exception $e) {
                echo "<p>❌ Error testing TournamentManager: " . $e->getMessage() . "</p>\n";
            }
        } else {
            echo "<p>❌ TournamentManager class not found</p>\n";
        }
    } else {
        echo "<p>❌ tournament_manager.php not found</p>\n";
    }
    
    echo "<h3>Test Results:</h3>\n";
    echo "<p><a href='manage-category.php?event_id=1&sport_id=1&category_id=1'>Test Tournament Management Page</a></p>\n";
    echo "<p><a href='tournament-setup.php'>Run Tournament Setup</a></p>\n";
    
} catch (Exception $e) {
    echo "<p>❌ Database connection failed: " . $e->getMessage() . "</p>\n";
}
?>
