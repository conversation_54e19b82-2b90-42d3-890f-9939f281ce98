<?php
/**
 * Tournament Management AJAX Endpoint
 * SC_IMS Sports Competition and Event Management System
 *
 * Handles AJAX requests for tournament management operations
 */

// Clean output buffer to prevent any unwanted output
if (ob_get_level()) {
    ob_clean();
}

require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

// Ensure admin authentication
requireAdmin();

// Set headers
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Initialize database connection
$database = new Database();
$conn = $database->getConnection();

try {
    $action = $_POST['action'] ?? $_GET['action'] ?? '';

    if (empty($action)) {
        throw new Exception('No action specified');
    }

    switch ($action) {
        case 'get_bracket_types':
            handleGetBracketTypes();
            break;

        case 'create_tournament':
            handleCreateTournament();
            break;

        case 'get_tournament_standings':
            handleGetTournamentStandings();
            break;

        case 'get_bracket_visualization':
            handleGetBracketVisualization();
            break;

        case 'advance_tournament':
            handleAdvanceTournament();
            break;

        case 'get_sport_formats':
            handleGetSportFormats();
            break;

        case 'regenerate_bracket':
            handleRegenerateBracket();
            break;

        default:
            throw new Exception('Invalid action: ' . $action);
    }

} catch (Exception $e) {
    // Clear any output that might have been generated
    if (ob_get_level()) {
        ob_clean();
    }

    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'action' => $action ?? 'unknown'
    ]);
} catch (Error $e) {
    // Handle fatal errors
    if (ob_get_level()) {
        ob_clean();
    }

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Internal server error: ' . $e->getMessage(),
        'action' => $action ?? 'unknown'
    ]);
}

/**
 * Get available bracket types for a sport
 */
function handleGetBracketTypes() {
    global $conn;

    try {
        $sportId = $_GET['sport_id'] ?? '';
        if (empty($sportId)) {
            throw new Exception('Sport ID is required');
        }

        // Check if tournament system is initialized
        $stmt = $conn->query("SHOW TABLES LIKE 'tournament_formats'");
        if ($stmt->rowCount() == 0) {
            throw new Exception('Tournament system not initialized. Please run tournament setup first.');
        }

        $stmt = $conn->query("SHOW COLUMNS FROM sports LIKE 'sport_type_id'");
        if ($stmt->rowCount() == 0) {
            throw new Exception('Sports table not updated. Please run tournament setup first.');
        }

        // Check if TournamentManager class exists
        if (!class_exists('TournamentManager')) {
            if (file_exists('../../includes/tournament_manager.php')) {
                require_once '../../includes/tournament_manager.php';
            } else {
                throw new Exception('TournamentManager class not found. Please run tournament setup first.');
            }
        }

        $tournamentManager = new TournamentManager($conn);

        // Get sport type category
        $sportTypeCategory = $tournamentManager->getSportTypeCategory($sportId);

        // Get available formats
        $formats = $tournamentManager->getAvailableFormats($sportTypeCategory);

        // Format for select options
        $options = [];
        foreach ($formats as $format) {
            $options[] = [
                'value' => $format['id'],
                'text' => $format['name'],
                'description' => $format['description'] ?? '',
                'min_participants' => $format['min_participants'] ?? 2,
                'max_participants' => $format['max_participants'] ?? null
            ];
        }

        // Clear any output buffer before sending JSON
        if (ob_get_level()) {
            ob_clean();
        }

        echo json_encode([
            'success' => true,
            'sport_type_category' => $sportTypeCategory,
            'formats' => $options
        ]);

    } catch (Exception $e) {
        // Clear any output buffer before sending error JSON
        if (ob_get_level()) {
            ob_clean();
        }

        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

/**
 * Create a new tournament
 */
function handleCreateTournament() {
    global $conn;
    
    $eventSportId = $_POST['event_sport_id'] ?? '';
    $formatId = $_POST['format_id'] ?? '';
    $tournamentName = $_POST['tournament_name'] ?? '';
    $seedingMethod = $_POST['seeding_method'] ?? 'random';
    
    if (empty($eventSportId) || empty($formatId)) {
        throw new Exception('Event sport ID and format ID are required');
    }
    
    require_once '../../includes/tournament_manager.php';
    $tournamentManager = new TournamentManager($conn);
    
    $config = [
        'seeding_method' => $seedingMethod,
        'scoring_config' => [
            'points_win' => $_POST['points_win'] ?? 3,
            'points_draw' => $_POST['points_draw'] ?? 1,
            'points_loss' => $_POST['points_loss'] ?? 0
        ]
    ];
    
    $tournamentId = $tournamentManager->createTournament($eventSportId, $formatId, $tournamentName, $config);
    
    // Log admin activity
    logAdminActivity($conn, $_SESSION['admin_id'], 'create_tournament', 
        "Created tournament: {$tournamentName} (ID: {$tournamentId})");
    
    echo json_encode([
        'success' => true,
        'message' => 'Tournament created successfully',
        'tournament_id' => $tournamentId
    ]);
}

/**
 * Get tournament standings
 */
function handleGetTournamentStandings() {
    global $conn;
    
    $tournamentId = $_GET['tournament_id'] ?? '';
    if (empty($tournamentId)) {
        throw new Exception('Tournament ID is required');
    }
    
    require_once '../../includes/tournament_manager.php';
    $tournamentManager = new TournamentManager($conn);
    $standings = $tournamentManager->getTournamentStandings($tournamentId);
    
    echo json_encode([
        'success' => true,
        'standings' => $standings
    ]);
}

/**
 * Get bracket visualization data
 */
function handleGetBracketVisualization() {
    global $conn;
    
    $tournamentId = $_GET['tournament_id'] ?? '';
    if (empty($tournamentId)) {
        throw new Exception('Tournament ID is required');
    }
    
    require_once '../../includes/tournament_manager.php';
    $tournamentManager = new TournamentManager($conn);
    $bracketData = $tournamentManager->getBracketVisualization($tournamentId);
    
    echo json_encode([
        'success' => true,
        'bracket' => $bracketData
    ]);
}

/**
 * Advance tournament to next round
 */
function handleAdvanceTournament() {
    global $conn;
    
    $tournamentId = $_POST['tournament_id'] ?? '';
    if (empty($tournamentId)) {
        throw new Exception('Tournament ID is required');
    }
    
    require_once '../../includes/tournament_manager.php';
    $tournamentManager = new TournamentManager($conn);
    $result = $tournamentManager->advanceToNextRound($tournamentId);
    
    // Log admin activity
    logAdminActivity($conn, $_SESSION['admin_id'], 'advance_tournament', 
        "Advanced tournament ID: {$tournamentId} - {$result['message']}");
    
    echo json_encode([
        'success' => true,
        'message' => $result['message'],
        'status' => $result['status']
    ]);
}

/**
 * Get tournament formats for a specific sport
 */
function handleGetSportFormats() {
    global $conn;

    $sportId = $_POST['sport_id'] ?? $_GET['sport_id'] ?? '';

    if (empty($sportId)) {
        throw new Exception('Sport ID is required');
    }

    // Get sport type category
    $stmt = $conn->prepare("
        SELECT st.category
        FROM sports s
        JOIN sport_types st ON s.sport_type_id = st.id
        WHERE s.id = ?
    ");
    $stmt->execute([$sportId]);
    $result = $stmt->fetch();
    $sportTypeCategory = $result ? $result['category'] : 'team';

    // Get available tournament formats for this sport type
    $stmt = $conn->prepare("
        SELECT id, name, description, min_participants, max_participants,
               requires_seeding, advancement_type
        FROM tournament_formats
        WHERE sport_type_category = ? OR sport_type_category = 'all'
        ORDER BY name
    ");
    $stmt->execute([$sportTypeCategory]);
    $formats = $stmt->fetchAll();

    echo json_encode([
        'success' => true,
        'sport_type_category' => $sportTypeCategory,
        'formats' => $formats
    ]);
}

/**
 * Regenerate tournament bracket
 */
function handleRegenerateBracket() {
    global $conn;

    $eventSportId = $_POST['event_sport_id'] ?? '';

    if (empty($eventSportId)) {
        throw new Exception('Event sport ID is required');
    }

    // Get existing tournament
    $stmt = $conn->prepare("
        SELECT ts.*, tf.name as format_name
        FROM tournament_structures ts
        JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
        WHERE ts.event_sport_id = ?
        ORDER BY ts.created_at DESC LIMIT 1
    ");
    $stmt->execute([$eventSportId]);
    $tournament = $stmt->fetch();

    if (!$tournament) {
        throw new Exception('No tournament found for this category');
    }

    // Delete existing tournament and recreate it
    $conn->beginTransaction();

    try {
        // Store tournament configuration
        $config = [
            'seeding_method' => $tournament['seeding_method'],
            'scoring_config' => json_decode($tournament['scoring_config'], true) ?: [
                'points_win' => 3,
                'points_draw' => 1,
                'points_loss' => 0
            ]
        ];

        // Clear existing matches and tournament data
        $stmt = $conn->prepare("DELETE FROM matches WHERE event_sport_id = ?");
        $stmt->execute([$eventSportId]);

        $stmt = $conn->prepare("DELETE FROM tournament_participants WHERE tournament_id = ?");
        $stmt->execute([$tournament['id']]);

        $stmt = $conn->prepare("DELETE FROM tournament_rounds WHERE tournament_id = ?");
        $stmt->execute([$tournament['id']]);

        $stmt = $conn->prepare("DELETE FROM tournament_structures WHERE id = ?");
        $stmt->execute([$tournament['id']]);

        // Create new tournament using tournament manager
        require_once '../../includes/tournament_manager.php';
        $tournamentManager = new TournamentManager($conn);

        $newTournamentId = $tournamentManager->createTournament(
            $eventSportId,
            $tournament['tournament_format_id'],
            $tournament['name'],
            $config
        );

        $conn->commit();

        // Log admin activity
        logAdminActivity($conn, $_SESSION['admin_id'], 'regenerate_bracket',
            "Regenerated bracket for tournament: {$tournament['name']} (New ID: {$newTournamentId})");

        echo json_encode([
            'success' => true,
            'message' => 'Tournament bracket regenerated successfully',
            'tournament_id' => $newTournamentId
        ]);

    } catch (Exception $e) {
        $conn->rollBack();
        throw new Exception('Failed to regenerate bracket: ' . $e->getMessage());
    }
}
?>
