<?php
/**
 * Tournament Format Detection Helper
 * Determines the appropriate tournament format based on sport type and category
 */

function getDefaultTournamentFormat($conn, $sportId) {
    try {
        // Get sport type category
        $stmt = $conn->prepare("
            SELECT st.category 
            FROM sports s 
            JOIN sport_types st ON s.sport_type_id = st.id 
            WHERE s.id = ?
        ");
        $stmt->execute([$sportId]);
        $result = $stmt->fetch();
        $category = $result ? $result['category'] : 'team';
        
        // Format preferences by category
        $format_preferences = [
            'team' => ['single_elimination', 'double_elimination', 'round_robin'],
            'individual' => ['elimination_rounds', 'single_elimination', 'best_performance'],
            'academic' => ['swiss_system', 'knockout_rounds', 'round_robin'],
            'judged' => ['judged_rounds', 'talent_showcase', 'performance_competition']
        ];
        
        $preferred_codes = $format_preferences[$category] ?? $format_preferences['team'];
        
        // Find the first available format
        foreach ($preferred_codes as $code) {
            $stmt = $conn->prepare("SELECT id, name FROM tournament_formats WHERE code = ? LIMIT 1");
            $stmt->execute([$code]);
            $format = $stmt->fetch();
            if ($format) {
                return $format;
            }
        }
        
        // Fallback to any single elimination format
        $stmt = $conn->prepare("SELECT id, name FROM tournament_formats WHERE name LIKE '%elimination%' ORDER BY id LIMIT 1");
        $stmt->execute();
        $format = $stmt->fetch();
        
        return $format ?: ['id' => 1, 'name' => 'Single Elimination'];
        
    } catch (Exception $e) {
        // Return safe default
        return ['id' => 1, 'name' => 'Single Elimination'];
    }
}

function getTournamentFormatDisplay($conn, $eventSportId) {
    try {
        // Check if tournament already exists
        $stmt = $conn->prepare("
            SELECT ts.*, tf.name as format_name 
            FROM tournament_structures ts
            JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
            WHERE ts.event_sport_id = ?
            ORDER BY ts.created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$eventSportId]);
        $tournament = $stmt->fetch();
        
        if ($tournament) {
            return $tournament['format_name'];
        }
        
        // Get sport ID from event_sport
        $stmt = $conn->prepare("SELECT sport_id FROM event_sports WHERE id = ?");
        $stmt->execute([$eventSportId]);
        $sportId = $stmt->fetchColumn();
        
        if ($sportId) {
            $format = getDefaultTournamentFormat($conn, $sportId);
            return $format['name'];
        }
        
        return 'Single Elimination';
        
    } catch (Exception $e) {
        return 'Single Elimination';
    }
}
?>