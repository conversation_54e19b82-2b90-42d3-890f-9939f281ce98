<?php
/**
 * Export Registrations for Sport Category
 * SC_IMS Admin Panel
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get parameters
$category_id = isset($_GET['category_id']) ? (int)$_GET['category_id'] : 0;
$format = isset($_GET['format']) ? $_GET['format'] : 'excel';

if (!$category_id) {
    header('Location: events.php');
    exit;
}

// Get category details
$sql = "SELECT 
            sc.*,
            es.id as event_sport_id,
            es.event_id,
            es.sport_id,
            e.name as event_name,
            s.name as sport_name
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE sc.id = ?";

$stmt = $conn->prepare($sql);
$stmt->execute([$category_id]);
$category = $stmt->fetch();

if (!$category) {
    header('Location: events.php');
    exit;
}

// Get registrations data
$sql = "SELECT
            r.id,
            r.team_name,
            r.status,
            r.registration_date,
            d.name as department_name,
            d.contact_person,
            d.contact_email,
            d.contact_phone,
            r.participants
        FROM registrations r
        JOIN departments d ON r.department_id = d.id
        WHERE r.event_sport_id = ?
        ORDER BY r.registration_date ASC";

$stmt = $conn->prepare($sql);
$stmt->execute([$category['event_sport_id']]);
$registrations = $stmt->fetchAll();

// Set headers for download
$filename = sanitizeFilename($category['event_name'] . '_' . $category['sport_name'] . '_' . $category['category_name'] . '_registrations');

if ($format === 'excel') {
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
    
    // Output Excel format
    echo "<table border='1'>";
    echo "<tr>";
    echo "<th>Registration ID</th>";
    echo "<th>Department</th>";
    echo "<th>Team Name</th>";
    echo "<th>Status</th>";
    echo "<th>Registration Date</th>";
    echo "<th>Contact Person</th>";
    echo "<th>Contact Email</th>";
    echo "<th>Contact Phone</th>";
    echo "<th>Participants</th>";
    echo "</tr>";
    
    foreach ($registrations as $reg) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($reg['id']) . "</td>";
        echo "<td>" . htmlspecialchars($reg['department_name']) . "</td>";
        echo "<td>" . htmlspecialchars($reg['team_name'] ?: $reg['department_name']) . "</td>";
        echo "<td>" . htmlspecialchars(ucfirst($reg['status'])) . "</td>";
        echo "<td>" . htmlspecialchars(date('Y-m-d H:i', strtotime($reg['registration_date']))) . "</td>";
        echo "<td>" . htmlspecialchars($reg['contact_person']) . "</td>";
        echo "<td>" . htmlspecialchars($reg['contact_email']) . "</td>";
        echo "<td>" . htmlspecialchars($reg['contact_phone']) . "</td>";
        
        // Format participants
        $participants = json_decode($reg['participants'], true);
        if (is_array($participants)) {
            echo "<td>" . htmlspecialchars(implode(', ', $participants)) . "</td>";
        } else {
            echo "<td>" . htmlspecialchars($reg['participants']) . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
    
} else {
    // CSV format
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
    
    $output = fopen('php://output', 'w');
    
    // CSV headers
    fputcsv($output, [
        'Registration ID',
        'Department',
        'Team Name',
        'Status',
        'Registration Date',
        'Contact Person',
        'Contact Email',
        'Contact Phone',
        'Participants'
    ]);
    
    // CSV data
    foreach ($registrations as $reg) {
        $participants = json_decode($reg['participants'], true);
        if (is_array($participants)) {
            $participants_str = implode(', ', $participants);
        } else {
            $participants_str = $reg['participants'];
        }
        
        fputcsv($output, [
            $reg['id'],
            $reg['department_name'],
            $reg['team_name'] ?: $reg['department_name'],
            ucfirst($reg['status']),
            date('Y-m-d H:i', strtotime($reg['registration_date'])),
            $reg['contact_person'],
            $reg['contact_email'],
            $reg['contact_phone'],
            $participants_str
        ]);
    }
    
    fclose($output);
}

function sanitizeFilename($filename) {
    // Remove or replace invalid characters
    $filename = preg_replace('/[^a-zA-Z0-9_\-]/', '_', $filename);
    $filename = preg_replace('/_{2,}/', '_', $filename);
    return trim($filename, '_');
}

// Log admin activity
logAdminActivity('EXPORT_REGISTRATIONS', 'sport_categories', $category_id);
?>
