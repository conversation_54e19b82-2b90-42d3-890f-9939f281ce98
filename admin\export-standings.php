<?php
/**
 * Export Standings for Sport Category
 * SC_IMS Admin Panel
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get parameters
$category_id = isset($_GET['category_id']) ? (int)$_GET['category_id'] : 0;
$format = isset($_GET['format']) ? $_GET['format'] : 'pdf';

if (!$category_id) {
    header('Location: events.php');
    exit;
}

// Get category details
$sql = "SELECT 
            sc.*,
            es.id as event_sport_id,
            es.event_id,
            es.sport_id,
            e.name as event_name,
            s.name as sport_name
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE sc.id = ?";

$stmt = $conn->prepare($sql);
$stmt->execute([$category_id]);
$category = $stmt->fetch();

if (!$category) {
    header('Location: events.php');
    exit;
}

// Get standings data
$sql = "SELECT
            standings.*,
            (standings.total_points_scored - standings.total_points_conceded) as point_difference
        FROM (
            SELECT
                r.id,
                COALESCE(r.team_name, d.name) as team_name,
                r.department_id,
                d.name as department_name,
                COUNT(DISTINCT m1.id) as matches_played,
                COUNT(DISTINCT CASE WHEN m1.winner_id = r.id THEN m1.id END) as wins,
                COUNT(DISTINCT CASE WHEN m1.status = 'completed' AND m1.winner_id != r.id AND m1.winner_id IS NOT NULL THEN m1.id END) as losses,
                COUNT(DISTINCT CASE WHEN m1.status = 'completed' AND m1.winner_id IS NULL THEN m1.id END) as draws,
                COALESCE(SUM(CASE WHEN s.team1_score IS NOT NULL AND m1.team1_id = r.id THEN s.team1_score
                                 WHEN s.team2_score IS NOT NULL AND m1.team2_id = r.id THEN s.team2_score
                                 ELSE 0 END), 0) as total_points_scored,
                COALESCE(SUM(CASE WHEN s.team1_score IS NOT NULL AND m1.team2_id = r.id THEN s.team1_score
                                 WHEN s.team2_score IS NOT NULL AND m1.team1_id = r.id THEN s.team2_score
                                 ELSE 0 END), 0) as total_points_conceded
            FROM registrations r
            JOIN departments d ON r.department_id = d.id
            LEFT JOIN matches m1 ON (m1.team1_id = r.id OR m1.team2_id = r.id) AND m1.event_sport_id = r.event_sport_id
            LEFT JOIN scores s ON m1.id = s.match_id AND s.is_final = 1
            WHERE r.event_sport_id = ?
            GROUP BY r.id, r.team_name, r.department_id, d.name
        ) as standings
        ORDER BY standings.wins DESC, point_difference DESC, standings.total_points_scored DESC";

$stmt = $conn->prepare($sql);
$stmt->execute([$category['event_sport_id']]);
$standings = $stmt->fetchAll();

// Set headers for download
$filename = sanitizeFilename($category['event_name'] . '_' . $category['sport_name'] . '_' . $category['category_name'] . '_standings');

if ($format === 'pdf') {
    // For PDF, we'll output HTML that can be printed to PDF
    header('Content-Type: text/html');
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title><?php echo htmlspecialchars($filename); ?></title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { color: #333; text-align: center; }
            .header-info { text-align: center; margin-bottom: 30px; color: #666; }
            table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .rank { text-align: center; font-weight: bold; }
            .rank-1 { background-color: #ffd700; }
            .rank-2 { background-color: #c0c0c0; }
            .rank-3 { background-color: #cd7f32; }
            @media print {
                body { margin: 0; }
                .no-print { display: none; }
            }
        </style>
    </head>
    <body>
        <h1>Final Standings</h1>
        <div class="header-info">
            <p><strong>Event:</strong> <?php echo htmlspecialchars($category['event_name']); ?></p>
            <p><strong>Sport:</strong> <?php echo htmlspecialchars($category['sport_name']); ?></p>
            <p><strong>Category:</strong> <?php echo htmlspecialchars($category['category_name']); ?></p>
            <p><strong>Generated:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th class="rank">Rank</th>
                    <th>Team</th>
                    <th>Department</th>
                    <th>Played</th>
                    <th>Won</th>
                    <th>Lost</th>
                    <th>Draws</th>
                    <th>Points For</th>
                    <th>Points Against</th>
                    <th>Point Difference</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($standings as $index => $team): ?>
                    <tr class="<?php echo $index < 3 ? 'rank-' . ($index + 1) : ''; ?>">
                        <td class="rank"><?php echo $index + 1; ?></td>
                        <td><?php echo htmlspecialchars($team['team_name'] ?: $team['department_name']); ?></td>
                        <td><?php echo htmlspecialchars($team['department_name']); ?></td>
                        <td><?php echo $team['matches_played']; ?></td>
                        <td><?php echo $team['wins']; ?></td>
                        <td><?php echo $team['losses']; ?></td>
                        <td><?php echo $team['draws']; ?></td>
                        <td><?php echo $team['total_points_scored']; ?></td>
                        <td><?php echo $team['total_points_conceded']; ?></td>
                        <td><?php echo $team['point_difference']; ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <div class="no-print" style="margin-top: 30px; text-align: center;">
            <button onclick="window.print()">Print to PDF</button>
            <button onclick="window.close()">Close</button>
        </div>
    </body>
    </html>
    <?php
} else {
    // Excel/CSV format
    if ($format === 'excel') {
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
        
        echo "<table border='1'>";
        echo "<tr><th colspan='10' style='text-align: center; font-size: 16px;'>" . htmlspecialchars($category['event_name'] . ' - ' . $category['sport_name'] . ' - ' . $category['category_name']) . "</th></tr>";
        echo "<tr><th colspan='10' style='text-align: center;'>Final Standings - Generated: " . date('Y-m-d H:i:s') . "</th></tr>";
        echo "<tr><td colspan='10'></td></tr>";
        echo "<tr>";
        echo "<th>Rank</th><th>Team</th><th>Department</th><th>Played</th><th>Won</th><th>Lost</th><th>Draws</th><th>Points For</th><th>Points Against</th><th>Point Difference</th>";
        echo "</tr>";
        
        foreach ($standings as $index => $team) {
            echo "<tr>";
            echo "<td>" . ($index + 1) . "</td>";
            echo "<td>" . htmlspecialchars($team['team_name'] ?: $team['department_name']) . "</td>";
            echo "<td>" . htmlspecialchars($team['department_name']) . "</td>";
            echo "<td>" . $team['matches_played'] . "</td>";
            echo "<td>" . $team['wins'] . "</td>";
            echo "<td>" . $team['losses'] . "</td>";
            echo "<td>" . $team['draws'] . "</td>";
            echo "<td>" . $team['total_points_scored'] . "</td>";
            echo "<td>" . $team['total_points_conceded'] . "</td>";
            echo "<td>" . $team['point_difference'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        // CSV format
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, ['Rank', 'Team', 'Department', 'Played', 'Won', 'Lost', 'Draws', 'Points For', 'Points Against', 'Point Difference']);
        
        // CSV data
        foreach ($standings as $index => $team) {
            fputcsv($output, [
                $index + 1,
                $team['team_name'] ?: $team['department_name'],
                $team['department_name'],
                $team['matches_played'],
                $team['wins'],
                $team['losses'],
                $team['draws'],
                $team['total_points_scored'],
                $team['total_points_conceded'],
                $team['point_difference']
            ]);
        }
        
        fclose($output);
    }
}

function sanitizeFilename($filename) {
    // Remove or replace invalid characters
    $filename = preg_replace('/[^a-zA-Z0-9_\-]/', '_', $filename);
    $filename = preg_replace('/_{2,}/', '_', $filename);
    return trim($filename, '_');
}

// Log admin activity
logAdminActivity('EXPORT_STANDINGS', 'sport_categories', $category_id);
?>
