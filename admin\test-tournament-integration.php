<?php
/**
 * Tournament Integration Test
 * Tests the manage-category.php page with real data
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

$database = new Database();
$conn = $database->getConnection();

// Get or create test data
$test_event_id = null;
$test_sport_id = null;
$test_category_id = null;

try {
    // Get or create a test event
    $stmt = $conn->query("SELECT id FROM events WHERE status != 'cancelled' LIMIT 1");
    $event = $stmt->fetch();
    
    if (!$event) {
        // Create a test event
        $stmt = $conn->prepare("INSERT INTO events (name, description, start_date, end_date, location, status) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            'Test Tournament Event',
            'Test event for tournament system verification',
            date('Y-m-d'),
            date('Y-m-d', strtotime('+7 days')),
            'Test Venue',
            'ongoing'
        ]);
        $test_event_id = $conn->lastInsertId();
    } else {
        $test_event_id = $event['id'];
    }
    
    // Get or create a test sport
    $stmt = $conn->query("SELECT id FROM sports LIMIT 1");
    $sport = $stmt->fetch();
    
    if (!$sport) {
        // Create a test sport
        $stmt = $conn->prepare("INSERT INTO sports (name, type, scoring_method, bracket_format) VALUES (?, ?, ?, ?)");
        $stmt->execute([
            'Test Basketball',
            'traditional',
            'point_based',
            'single_elimination'
        ]);
        $test_sport_id = $conn->lastInsertId();
    } else {
        $test_sport_id = $sport['id'];
    }
    
    // Get or create event_sport
    $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ? AND sport_id = ?");
    $stmt->execute([$test_event_id, $test_sport_id]);
    $event_sport = $stmt->fetch();
    
    if (!$event_sport) {
        $stmt = $conn->prepare("INSERT INTO event_sports (event_id, sport_id, max_teams, status) VALUES (?, ?, ?, ?)");
        $stmt->execute([$test_event_id, $test_sport_id, 8, 'registration']);
        $event_sport_id = $conn->lastInsertId();
    } else {
        $event_sport_id = $event_sport['id'];
    }
    
    // Get or create sport category
    $stmt = $conn->prepare("SELECT id FROM sport_categories WHERE event_sport_id = ?");
    $stmt->execute([$event_sport_id]);
    $category = $stmt->fetch();
    
    if (!$category) {
        $stmt = $conn->prepare("INSERT INTO sport_categories (event_sport_id, category_name, category_type, status) VALUES (?, ?, ?, ?)");
        $stmt->execute([$event_sport_id, 'Men\'s Division', 'men', 'registration']);
        $test_category_id = $conn->lastInsertId();
    } else {
        $test_category_id = $category['id'];
    }
    
    // Create some test departments if none exist
    $stmt = $conn->query("SELECT COUNT(*) as count FROM departments");
    $dept_count = $stmt->fetch()['count'];
    
    if ($dept_count < 3) {
        $test_departments = [
            ['Computer Science', 'CS', '#3b82f6'],
            ['Engineering', 'ENG', '#ef4444'],
            ['Business', 'BUS', '#10b981'],
            ['Arts', 'ARTS', '#f59e0b']
        ];
        
        foreach ($test_departments as $dept) {
            $stmt = $conn->prepare("INSERT IGNORE INTO departments (name, abbreviation, color_code) VALUES (?, ?, ?)");
            $stmt->execute($dept);
        }
    }
    
    // Create some test registrations
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM registrations WHERE event_sport_id = ?");
    $stmt->execute([$event_sport_id]);
    $reg_count = $stmt->fetch()['count'];
    
    if ($reg_count < 2) {
        $stmt = $conn->query("SELECT id FROM departments LIMIT 4");
        $departments = $stmt->fetchAll();
        
        foreach ($departments as $dept) {
            $participants = json_encode([
                ['name' => 'Player 1', 'student_id' => '2021001'],
                ['name' => 'Player 2', 'student_id' => '2021002']
            ]);
            
            $stmt = $conn->prepare("INSERT IGNORE INTO registrations (event_sport_id, department_id, status, participants) VALUES (?, ?, ?, ?)");
            $stmt->execute([$event_sport_id, $dept['id'], 'confirmed', $participants]);
        }
    }
    
    $setup_success = true;
    $setup_message = "Test data created successfully";
    
} catch (Exception $e) {
    $setup_success = false;
    $setup_message = "Error setting up test data: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tournament Integration Test | SC_IMS Admin</title>
    <?php include 'includes/admin-styles.php'; ?>
    <style>
        .integration-test {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-status {
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            text-align: center;
        }
        .status-success {
            background: #f0fdf4;
            border: 2px solid #22c55e;
            color: #166534;
        }
        .status-error {
            background: #fef2f2;
            border: 2px solid #ef4444;
            color: #991b1b;
        }
        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }
        .test-link-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
        }
        .test-link-card:hover {
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .test-link-card h4 {
            margin: 0 0 1rem 0;
            color: #1f2937;
        }
        .test-link-card p {
            margin: 0 0 1.5rem 0;
            color: #6b7280;
            font-size: 0.9rem;
        }
        .iframe-container {
            margin: 2rem 0;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }
        .iframe-header {
            background: #f9fafb;
            padding: 1rem;
            border-bottom: 1px solid #e5e7eb;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="admin-content">
            <div class="integration-test">
                <div class="page-header">
                    <h1><i class="fas fa-puzzle-piece"></i> Tournament Integration Test</h1>
                    <p>Test the complete tournament management system with real data</p>
                </div>
                
                <!-- Setup Status -->
                <div class="test-status <?php echo $setup_success ? 'status-success' : 'status-error'; ?>">
                    <div style="font-size: 2rem; margin-bottom: 1rem;">
                        <?php echo $setup_success ? '✅' : '❌'; ?>
                    </div>
                    <h3><?php echo $setup_success ? 'Test Data Ready' : 'Setup Failed'; ?></h3>
                    <p><?php echo htmlspecialchars($setup_message); ?></p>
                    
                    <?php if ($setup_success): ?>
                        <div style="margin-top: 1rem; font-size: 0.9rem; opacity: 0.8;">
                            <p>Event ID: <?php echo $test_event_id; ?> | Sport ID: <?php echo $test_sport_id; ?> | Category ID: <?php echo $test_category_id; ?></p>
                        </div>
                    <?php endif; ?>
                </div>
                
                <?php if ($setup_success): ?>
                    <!-- Test Links -->
                    <div class="test-links">
                        <div class="test-link-card">
                            <h4><i class="fas fa-trophy"></i> Tournament Management</h4>
                            <p>Test the main tournament management interface with real data</p>
                            <a href="manage-category.php?event_id=<?php echo $test_event_id; ?>&sport_id=<?php echo $test_sport_id; ?>&category_id=<?php echo $test_category_id; ?>" 
                               class="btn btn-primary" target="_blank">
                                <i class="fas fa-external-link-alt"></i> Open Tournament Page
                            </a>
                        </div>
                        
                        <div class="test-link-card">
                            <h4><i class="fas fa-cogs"></i> Creation Flow Test</h4>
                            <p>Test tournament creation with automated data</p>
                            <a href="test-tournament-creation-flow.php" class="btn btn-secondary" target="_blank">
                                <i class="fas fa-play"></i> Run Creation Test
                            </a>
                        </div>
                        
                        <div class="test-link-card">
                            <h4><i class="fas fa-check-circle"></i> Full System Test</h4>
                            <p>Comprehensive end-to-end system verification</p>
                            <a href="test-tournament-end-to-end.php" class="btn btn-success" target="_blank">
                                <i class="fas fa-clipboard-check"></i> Run Full Test
                            </a>
                        </div>
                        
                        <div class="test-link-card">
                            <h4><i class="fas fa-calendar"></i> Event Management</h4>
                            <p>Manage the test event and its sports</p>
                            <a href="manage-event.php?event_id=<?php echo $test_event_id; ?>" class="btn btn-info" target="_blank">
                                <i class="fas fa-edit"></i> Manage Event
                            </a>
                        </div>
                    </div>
                    
                    <!-- Embedded Tournament Page Preview -->
                    <div class="iframe-container">
                        <div class="iframe-header">
                            <i class="fas fa-eye"></i> Tournament Management Page Preview
                        </div>
                        <iframe src="manage-category.php?event_id=<?php echo $test_event_id; ?>&sport_id=<?php echo $test_sport_id; ?>&category_id=<?php echo $test_category_id; ?>" 
                                width="100%" height="600" frameborder="0" style="display: block;">
                        </iframe>
                    </div>
                    
                    <!-- Test Instructions -->
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-list-check"></i> Testing Checklist</h3>
                        </div>
                        <div class="card-body">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                                <div>
                                    <h4>🏆 Tournament Management</h4>
                                    <ul>
                                        <li>✅ Page loads without errors</li>
                                        <li>✅ Category information displays correctly</li>
                                        <li>✅ Registration data shows properly</li>
                                        <li>✅ Tab navigation works</li>
                                        <li>✅ Tournament creation modal opens</li>
                                        <li>✅ Format selection is dynamic</li>
                                    </ul>
                                </div>
                                
                                <div>
                                    <h4>🎯 Tournament Creation</h4>
                                    <ul>
                                        <li>✅ Tournament formats load by sport type</li>
                                        <li>✅ Validation works correctly</li>
                                        <li>✅ Tournament creates successfully</li>
                                        <li>✅ Bracket generates automatically</li>
                                        <li>✅ Participants are seeded</li>
                                        <li>✅ Matches are scheduled</li>
                                    </ul>
                                </div>
                                
                                <div>
                                    <h4>📊 Data Management</h4>
                                    <ul>
                                        <li>✅ Standings calculate correctly</li>
                                        <li>✅ Match results update standings</li>
                                        <li>✅ Bracket visualization works</li>
                                        <li>✅ Real-time updates function</li>
                                        <li>✅ Export features work</li>
                                        <li>✅ Mobile responsive design</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Error Recovery -->
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-tools"></i> Recovery Actions</h3>
                        </div>
                        <div class="card-body">
                            <p>The test data setup failed. Try these recovery actions:</p>
                            <div style="display: flex; gap: 1rem; flex-wrap: wrap; margin-top: 1rem;">
                                <a href="tournament-setup.php" class="btn btn-warning">
                                    <i class="fas fa-cog"></i> Run Tournament Setup
                                </a>
                                <a href="events.php" class="btn btn-primary">
                                    <i class="fas fa-calendar"></i> Create Events
                                </a>
                                <a href="sports.php" class="btn btn-secondary">
                                    <i class="fas fa-futbol"></i> Create Sports
                                </a>
                                <a href="departments.php" class="btn btn-info">
                                    <i class="fas fa-building"></i> Create Departments
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Quick Navigation -->
                <div class="card" style="margin-top: 2rem;">
                    <div class="card-header">
                        <h3><i class="fas fa-compass"></i> Quick Navigation</h3>
                    </div>
                    <div class="card-body">
                        <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                            <a href="index.php" class="btn btn-outline-primary">
                                <i class="fas fa-home"></i> Dashboard
                            </a>
                            <a href="events.php" class="btn btn-outline-secondary">
                                <i class="fas fa-calendar"></i> Events
                            </a>
                            <a href="sports.php" class="btn btn-outline-info">
                                <i class="fas fa-futbol"></i> Sports
                            </a>
                            <a href="departments.php" class="btn btn-outline-success">
                                <i class="fas fa-building"></i> Departments
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
