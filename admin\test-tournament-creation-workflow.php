<?php
/**
 * Test Tournament Creation Workflow
 * SC_IMS Sports Competition and Event Management System
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/tournament_manager.php';
require_once __DIR__ . '/../includes/tournament_format_helper.php';

$database = new Database();
$conn = $database->getConnection();

echo "<!DOCTYPE html>";
echo "<html><head><title>Tournament Creation Workflow Test</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .warning { background: #fff3cd; color: #856404; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .info { background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 5px; margin: 10px 0; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    .test-step { border: 1px solid #ddd; margin: 15px 0; padding: 15px; border-radius: 8px; }
    .step-header { font-weight: bold; font-size: 1.1em; margin-bottom: 10px; }
</style></head><body>";

echo "<h1>🧪 Tournament Creation Workflow Test</h1>";

$test_results = [];

try {
    // Step 1: Database Schema Verification
    echo "<div class='test-step'>";
    echo "<div class='step-header'>📋 Step 1: Database Schema Verification</div>";
    
    $required_tables = ['tournament_structures', 'tournament_formats', 'tournament_rounds', 'tournament_participants'];
    $schema_ok = true;
    
    foreach ($required_tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->rowCount() > 0) {
            echo "<div class='success'>✅ Table '$table' exists</div>";
        } else {
            echo "<div class='error'>❌ Table '$table' missing</div>";
            $schema_ok = false;
        }
    }
    
    // Check matches table columns
    $result = $conn->query("DESCRIBE matches");
    $columns = array_column($result->fetchAll(PDO::FETCH_ASSOC), 'Field');
    $required_columns = ['tournament_structure_id', 'tournament_round_id', 'bracket_position', 'is_bye_match'];
    
    foreach ($required_columns as $column) {
        if (in_array($column, $columns)) {
            echo "<div class='success'>✅ Column 'matches.$column' exists</div>";
        } else {
            echo "<div class='error'>❌ Column 'matches.$column' missing</div>";
            $schema_ok = false;
        }
    }
    
    $test_results['schema'] = $schema_ok;
    echo "</div>";
    
    // Step 2: Tournament Format Detection Test
    echo "<div class='test-step'>";
    echo "<div class='step-header'>🎯 Step 2: Tournament Format Detection Test</div>";
    
    // Get a test sport
    $result = $conn->query("
        SELECT es.id as event_sport_id, s.id as sport_id, s.name as sport_name, st.category
        FROM event_sports es
        JOIN sports s ON es.sport_id = s.id
        LEFT JOIN sport_types st ON s.sport_type_id = st.id
        LIMIT 1
    ");
    $test_sport = $result->fetch(PDO::FETCH_ASSOC);
    
    if ($test_sport) {
        echo "<div class='info'>ℹ️ Testing with sport: {$test_sport['sport_name']} (Category: " . ($test_sport['category'] ?: 'Unassigned') . ")</div>";
        
        // Test format detection
        $default_format = getDefaultTournamentFormat($conn, $test_sport['sport_id']);
        $display_format = getTournamentFormatDisplay($conn, $test_sport['event_sport_id']);
        
        echo "<div class='success'>✅ Default format detected: {$default_format['name']} (ID: {$default_format['id']})</div>";
        echo "<div class='success'>✅ Display format: $display_format</div>";
        
        $test_results['format_detection'] = true;
    } else {
        echo "<div class='error'>❌ No test sport available</div>";
        $test_results['format_detection'] = false;
    }
    echo "</div>";
    
    // Step 3: Participant Registration Check
    echo "<div class='test-step'>";
    echo "<div class='step-header'>👥 Step 3: Participant Registration Check</div>";
    
    if ($test_sport) {
        // Check for participants using unified registration system
        $stmt = $conn->prepare("
            SELECT 
                dsp.id,
                COALESCE(dsp.team_name, d.name) as team_name,
                edr.department_id,
                d.name as department_name,
                dsp.status
            FROM event_department_registrations edr
            JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
            JOIN departments d ON edr.department_id = d.id
            WHERE dsp.event_sport_id = ? AND edr.status IN ('approved', 'pending') AND dsp.status = 'confirmed'
        ");
        $stmt->execute([$test_sport['event_sport_id']]);
        $participants = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($participants)) {
            // Try fallback registration system
            $stmt = $conn->prepare("
                SELECT 
                    r.id,
                    COALESCE(r.team_name, d.name) as team_name,
                    r.department_id,
                    d.name as department_name,
                    r.status
                FROM registrations r
                JOIN departments d ON r.department_id = d.id
                WHERE r.event_sport_id = ? AND r.status IN ('approved', 'confirmed')
            ");
            $stmt->execute([$test_sport['event_sport_id']]);
            $participants = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
        echo "<div class='info'>ℹ️ Found " . count($participants) . " participants</div>";
        
        if (count($participants) >= 2) {
            echo "<div class='success'>✅ Sufficient participants for tournament creation</div>";
            
            echo "<h4>Participants:</h4>";
            echo "<table>";
            echo "<tr><th>ID</th><th>Team Name</th><th>Department</th><th>Status</th></tr>";
            foreach (array_slice($participants, 0, 5) as $participant) {
                echo "<tr>";
                echo "<td>{$participant['id']}</td>";
                echo "<td>{$participant['team_name']}</td>";
                echo "<td>{$participant['department_name']}</td>";
                echo "<td>{$participant['status']}</td>";
                echo "</tr>";
            }
            if (count($participants) > 5) {
                echo "<tr><td colspan='4'>... and " . (count($participants) - 5) . " more</td></tr>";
            }
            echo "</table>";
            
            $test_results['participants'] = true;
        } else {
            echo "<div class='warning'>⚠ Not enough participants (minimum 2 required)</div>";
            $test_results['participants'] = false;
        }
    }
    echo "</div>";
    
    // Step 4: Tournament Creation Test
    echo "<div class='test-step'>";
    echo "<div class='step-header'>🏆 Step 4: Tournament Creation Test</div>";
    
    if ($test_sport && $test_results['schema'] && $test_results['format_detection'] && $test_results['participants']) {
        try {
            // Check if tournament already exists
            $stmt = $conn->prepare("SELECT id FROM tournament_structures WHERE event_sport_id = ?");
            $stmt->execute([$test_sport['event_sport_id']]);
            $existing_tournament = $stmt->fetch();
            
            if ($existing_tournament) {
                echo "<div class='info'>ℹ️ Tournament already exists (ID: {$existing_tournament['id']})</div>";
                echo "<div class='success'>✅ Tournament creation workflow previously successful</div>";
                $test_results['tournament_creation'] = true;
            } else {
                // Create tournament manager
                $tournamentManager = new TournamentManager($conn);
                
                // Configuration
                $config = [
                    'seeding_method' => 'random',
                    'scoring_config' => [
                        'points_win' => 3,
                        'points_draw' => 1,
                        'points_loss' => 0
                    ]
                ];
                
                $tournament_name = "Test Tournament - " . $test_sport['sport_name'] . " - " . date('Y-m-d H:i:s');
                
                echo "<div class='info'>ℹ️ Creating tournament: $tournament_name</div>";
                echo "<div class='info'>ℹ️ Format ID: {$default_format['id']}</div>";
                echo "<div class='info'>ℹ️ Event Sport ID: {$test_sport['event_sport_id']}</div>";
                
                // Create the tournament
                $tournamentId = $tournamentManager->createTournament(
                    $test_sport['event_sport_id'],
                    $default_format['id'],
                    $tournament_name,
                    $config
                );
                
                echo "<div class='success'>🎉 Tournament created successfully!</div>";
                echo "<div class='success'>✅ Tournament ID: $tournamentId</div>";
                echo "<div class='success'>✅ No database errors occurred</div>";
                
                $test_results['tournament_creation'] = true;
            }
            
        } catch (Exception $e) {
            echo "<div class='error'>❌ Tournament creation failed: " . $e->getMessage() . "</div>";
            echo "<div class='error'>Error details:</div>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
            $test_results['tournament_creation'] = false;
        }
    } else {
        echo "<div class='warning'>⚠ Skipping tournament creation test due to failed prerequisites</div>";
        $test_results['tournament_creation'] = false;
    }
    echo "</div>";
    
    // Step 5: AJAX Endpoint Test
    echo "<div class='test-step'>";
    echo "<div class='step-header'>🔗 Step 5: AJAX Endpoint Test</div>";
    
    if ($test_sport && $test_results['schema']) {
        // Test the AJAX endpoint that was failing
        echo "<div class='info'>ℹ️ Testing AJAX endpoint: ajax/create-tournament.php</div>";
        
        // Simulate the AJAX request data
        $ajax_data = [
            'event_sport_id' => $test_sport['event_sport_id'],
            'tournament_name' => 'AJAX Test Tournament - ' . date('H:i:s'),
            'format_id' => $default_format['id'],
            'seeding_method' => 'random'
        ];
        
        echo "<div class='info'>ℹ️ AJAX data prepared:</div>";
        echo "<pre>" . json_encode($ajax_data, JSON_PRETTY_PRINT) . "</pre>";
        
        // Check if the endpoint file exists
        if (file_exists(__DIR__ . '/ajax/create-tournament.php')) {
            echo "<div class='success'>✅ AJAX endpoint file exists</div>";
            echo "<div class='info'>ℹ️ Endpoint ready for testing via browser</div>";
            $test_results['ajax_endpoint'] = true;
        } else {
            echo "<div class='error'>❌ AJAX endpoint file missing</div>";
            $test_results['ajax_endpoint'] = false;
        }
    }
    echo "</div>";
    
    // Final Results Summary
    echo "<div class='test-step'>";
    echo "<div class='step-header'>📊 Final Test Results Summary</div>";
    
    $total_tests = count($test_results);
    $passed_tests = array_sum($test_results);
    
    echo "<table>";
    echo "<tr><th>Test</th><th>Result</th></tr>";
    foreach ($test_results as $test => $result) {
        $status = $result ? '✅ PASS' : '❌ FAIL';
        $class = $result ? 'success' : 'error';
        echo "<tr><td>" . ucfirst(str_replace('_', ' ', $test)) . "</td><td><span class='$class'>$status</span></td></tr>";
    }
    echo "</table>";
    
    echo "<div class='info'>";
    echo "<h3>Overall Result: $passed_tests/$total_tests tests passed</h3>";
    if ($passed_tests == $total_tests) {
        echo "<div class='success'>🎉 All tests passed! Tournament creation workflow is working correctly.</div>";
    } else {
        echo "<div class='warning'>⚠ Some tests failed. Please review the issues above.</div>";
    }
    echo "</div>";
    echo "</div>";
    
    echo "<p><a href='manage-category.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Live Tournament Creation</a></p>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Test execution error: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</body></html>";
?>
