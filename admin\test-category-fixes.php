<?php
/**
 * Test Category Management Fixes
 * Comprehensive testing of all sport category management issues
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Category Management Fixes</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { padding: 8px 16px; margin: 5px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; border: none; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Category Management Fixes Test Results</h1>

    <div class="section info">
        <h2>1. Database Schema Validation</h2>
        <?php
        try {
            // Check if tournament_format_id column exists
            $stmt = $conn->prepare("SHOW COLUMNS FROM event_sports LIKE 'tournament_format_id'");
            $stmt->execute();
            $column = $stmt->fetch();
            
            if ($column) {
                echo '<p class="success">✓ tournament_format_id column exists in event_sports table</p>';
                echo '<pre>' . print_r($column, true) . '</pre>';
            } else {
                echo '<p class="error">✗ tournament_format_id column is missing from event_sports table</p>';
                echo '<p><a href="debug-event-sports-schema.php" class="btn">Fix Database Schema</a></p>';
            }
        } catch (Exception $e) {
            echo '<p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>

    <div class="section info">
        <h2>2. Tournament Format Data Test</h2>
        <?php
        try {
            // Get a sample event_sport with tournament format
            $stmt = $conn->prepare("
                SELECT es.id, es.tournament_format_id, tf.name as format_name, e.name as event_name, s.name as sport_name
                FROM event_sports es
                LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
                LEFT JOIN events e ON es.event_id = e.id
                LEFT JOIN sports s ON es.sport_id = s.id
                LIMIT 5
            ");
            $stmt->execute();
            $event_sports = $stmt->fetchAll();
            
            if (!empty($event_sports)) {
                echo '<table>';
                echo '<tr><th>Event Sport ID</th><th>Event</th><th>Sport</th><th>Tournament Format ID</th><th>Format Name</th></tr>';
                foreach ($event_sports as $es) {
                    echo '<tr>';
                    echo '<td>' . htmlspecialchars($es['id']) . '</td>';
                    echo '<td>' . htmlspecialchars($es['event_name']) . '</td>';
                    echo '<td>' . htmlspecialchars($es['sport_name']) . '</td>';
                    echo '<td>' . htmlspecialchars($es['tournament_format_id'] ?: 'NULL') . '</td>';
                    echo '<td>' . htmlspecialchars($es['format_name'] ?: 'Not set') . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
                
                // Check if any have tournament formats
                $has_formats = false;
                foreach ($event_sports as $es) {
                    if ($es['tournament_format_id']) {
                        $has_formats = true;
                        break;
                    }
                }
                
                if ($has_formats) {
                    echo '<p class="success">✓ Some event sports have tournament formats assigned</p>';
                } else {
                    echo '<p class="warning">⚠ No event sports have tournament formats assigned yet</p>';
                }
            } else {
                echo '<p class="warning">No event sports found to test with</p>';
            }
        } catch (Exception $e) {
            echo '<p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>

    <div class="section info">
        <h2>3. Category Page Data Test</h2>
        <?php
        try {
            // Get a sample sport category
            $stmt = $conn->prepare("
                SELECT sc.id, sc.category_name, es.id as event_sport_id
                FROM sport_categories sc
                JOIN event_sports es ON sc.event_sport_id = es.id
                LIMIT 1
            ");
            $stmt->execute();
            $category = $stmt->fetch();
            
            if ($category) {
                echo '<p>Testing with Category ID: ' . $category['id'] . ' (' . htmlspecialchars($category['category_name']) . ')</p>';
                
                // Test the updated query from manage-category.php
                $sql = "SELECT 
                            sc.*,
                            es.id as event_sport_id,
                            es.event_id,
                            es.sport_id,
                            es.tournament_format_id,
                            es.max_teams,
                            es.venue as event_venue,
                            es.status as event_sport_status,
                            e.name as event_name,
                            s.name as sport_name,
                            tf.name as tournament_format_name,
                            tf.description as tournament_format_description
                        FROM sport_categories sc
                        JOIN event_sports es ON sc.event_sport_id = es.id
                        JOIN events e ON es.event_id = e.id
                        JOIN sports s ON es.sport_id = s.id
                        LEFT JOIN sport_types st ON s.sport_type_id = st.id
                        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
                        WHERE sc.id = ?";
                
                $stmt = $conn->prepare($sql);
                $stmt->execute([$category['id']]);
                $result = $stmt->fetch();
                
                if ($result) {
                    echo '<p class="success">✓ Category query successful</p>';
                    echo '<table>';
                    echo '<tr><th>Field</th><th>Value</th></tr>';
                    echo '<tr><td>Event Sport ID</td><td>' . htmlspecialchars($result['event_sport_id']) . '</td></tr>';
                    echo '<tr><td>Event Name</td><td>' . htmlspecialchars($result['event_name']) . '</td></tr>';
                    echo '<tr><td>Sport Name</td><td>' . htmlspecialchars($result['sport_name']) . '</td></tr>';
                    echo '<tr><td>Tournament Format ID</td><td>' . htmlspecialchars($result['tournament_format_id'] ?: 'NULL') . '</td></tr>';
                    echo '<tr><td>Tournament Format Name</td><td>' . htmlspecialchars($result['tournament_format_name'] ?: 'Not set') . '</td></tr>';
                    echo '</table>';
                    
                    if ($result['tournament_format_name']) {
                        echo '<p class="success">✓ Tournament format is properly retrieved and will display correctly</p>';
                    } else {
                        echo '<p class="warning">⚠ Tournament format not set for this category</p>';
                    }
                } else {
                    echo '<p class="error">✗ Category query failed</p>';
                }
            } else {
                echo '<p class="warning">No sport categories found to test with</p>';
            }
        } catch (Exception $e) {
            echo '<p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>

    <div class="section info">
        <h2>4. Registration Management Link Test</h2>
        <?php
        try {
            // Get a sample sport category for testing
            $stmt = $conn->prepare("
                SELECT sc.id, sc.category_name, es.id as event_sport_id, e.name as event_name, s.name as sport_name
                FROM sport_categories sc
                JOIN event_sports es ON sc.event_sport_id = es.id
                JOIN events e ON es.event_id = e.id
                JOIN sports s ON es.sport_id = s.id
                LIMIT 1
            ");
            $stmt->execute();
            $category = $stmt->fetch();
            
            if ($category) {
                $old_link = "registrations.php?event_id={$category['event_id']}&sport_id={$category['sport_id']}";
                $new_link = "registrations.php?event_sport_id={$category['event_sport_id']}";
                
                echo '<p><strong>Category:</strong> ' . htmlspecialchars($category['category_name']) . '</p>';
                echo '<p><strong>Old (incorrect) link:</strong> <code>' . htmlspecialchars($old_link) . '</code></p>';
                echo '<p><strong>New (correct) link:</strong> <code>' . htmlspecialchars($new_link) . '</code></p>';
                echo '<p class="success">✓ Registration management links have been updated to use event_sport_id</p>';
                
                // Test if the registrations page accepts the new parameter
                echo '<p><a href="' . htmlspecialchars($new_link) . '" class="btn" target="_blank">Test Registration Management Link</a></p>';
            } else {
                echo '<p class="warning">No categories found to test registration links</p>';
            }
        } catch (Exception $e) {
            echo '<p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>

    <div class="section info">
        <h2>5. Export Functionality Test</h2>
        <?php
        try {
            // Get a sample sport category for testing
            $stmt = $conn->prepare("
                SELECT sc.id, sc.category_name, e.name as event_name, s.name as sport_name
                FROM sport_categories sc
                JOIN event_sports es ON sc.event_sport_id = es.id
                JOIN events e ON es.event_id = e.id
                JOIN sports s ON es.sport_id = s.id
                LIMIT 1
            ");
            $stmt->execute();
            $category = $stmt->fetch();
            
            if ($category) {
                echo '<p><strong>Testing with Category:</strong> ' . htmlspecialchars($category['category_name']) . '</p>';
                
                $export_files = [
                    'export-registrations.php' => 'Export Registrations',
                    'export-matches.php' => 'Export Matches', 
                    'export-standings.php' => 'Export Standings'
                ];
                
                echo '<p><strong>Export Files Created:</strong></p>';
                echo '<ul>';
                foreach ($export_files as $file => $name) {
                    if (file_exists($file)) {
                        echo '<li class="success">✓ ' . $file . ' exists</li>';
                    } else {
                        echo '<li class="error">✗ ' . $file . ' missing</li>';
                    }
                }
                echo '</ul>';
                
                echo '<p><strong>Test Export Links:</strong></p>';
                echo '<p>';
                echo '<a href="export-registrations.php?category_id=' . $category['id'] . '&format=excel" class="btn btn-success" target="_blank">Test Export Registrations (Excel)</a>';
                echo '<a href="export-matches.php?category_id=' . $category['id'] . '&format=excel" class="btn btn-success" target="_blank">Test Export Matches (Excel)</a>';
                echo '<a href="export-standings.php?category_id=' . $category['id'] . '&format=pdf" class="btn btn-success" target="_blank">Test Export Standings (PDF)</a>';
                echo '</p>';
            } else {
                echo '<p class="warning">No categories found to test export functionality</p>';
            }
        } catch (Exception $e) {
            echo '<p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>

    <div class="section info">
        <h2>6. Live Category Page Test</h2>
        <?php
        try {
            // Get a sample sport category for testing
            $stmt = $conn->prepare("
                SELECT sc.id, sc.category_name, es.event_id, es.sport_id, e.name as event_name, s.name as sport_name
                FROM sport_categories sc
                JOIN event_sports es ON sc.event_sport_id = es.id
                JOIN events e ON es.event_id = e.id
                JOIN sports s ON es.sport_id = s.id
                LIMIT 1
            ");
            $stmt->execute();
            $category = $stmt->fetch();
            
            if ($category) {
                $category_url = "manage-category.php?event_id={$category['event_id']}&sport_id={$category['sport_id']}&category_id={$category['id']}";
                
                echo '<p><strong>Test Category:</strong> ' . htmlspecialchars($category['category_name']) . '</p>';
                echo '<p><strong>Event:</strong> ' . htmlspecialchars($category['event_name']) . '</p>';
                echo '<p><strong>Sport:</strong> ' . htmlspecialchars($category['sport_name']) . '</p>';
                echo '<p><a href="' . htmlspecialchars($category_url) . '" class="btn btn-warning" target="_blank">Open Category Management Page</a></p>';
                echo '<p class="info">This will open the actual category management page where you can verify:</p>';
                echo '<ul>';
                echo '<li>Tournament format displays correctly</li>';
                echo '<li>Manage Registration button works</li>';
                echo '<li>Export buttons function properly</li>';
                echo '</ul>';
            } else {
                echo '<p class="warning">No categories found to test with</p>';
            }
        } catch (Exception $e) {
            echo '<p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>

    <div class="section info">
        <h2>7. Quick Fix Actions</h2>
        <p>If you're seeing errors above, use these quick fix actions:</p>
        <p>
            <a href="debug-event-sports-schema.php" class="btn btn-warning" target="_blank">Fix Database Schema</a>
            <a href="debug-event-sports-schema.php" class="btn btn-success" target="_blank">Create Test Data</a>
        </p>
        <p class="info">
            <strong>Instructions:</strong><br>
            1. Click "Fix Database Schema" and run the schema fix<br>
            2. If you need test data, click "Create Test Data" and run the test data creation<br>
            3. Return to this page to verify all tests pass
        </p>
    </div>

    <div class="section success">
        <h2>Summary of Fixes Applied</h2>
        <ul>
            <li>✓ Added tournament_format_id column to event_sports table</li>
            <li>✓ Updated add sport AJAX handler to save tournament_format_id</li>
            <li>✓ Modified category page query to JOIN with tournament_formats table</li>
            <li>✓ Fixed tournament format display logic to use database data</li>
            <li>✓ Corrected Manage Registration button to use event_sport_id parameter</li>
            <li>✓ Created export-registrations.php for registration data export</li>
            <li>✓ Created export-matches.php for match data export</li>
            <li>✓ Created export-standings.php for standings data export</li>
        </ul>
    </div>

</body>
</html>
