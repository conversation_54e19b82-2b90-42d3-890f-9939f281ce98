<?php
/**
 * AJAX handler for category management actions
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require admin authentication
requireAdmin();

// Set JSON response header
header('Content-Type: application/json');

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get current admin user
$current_admin = getCurrentAdmin();

try {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'update_category':
            updateCategory($conn, $current_admin);
            break;
            
        default:
            throw new Exception('Invalid action specified');
    }
} catch (Exception $e) {
    error_log("Category action error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function updateCategory($conn, $current_admin) {
    // Validate required fields
    $category_id = $_POST['category_id'] ?? null;
    $category_name = $_POST['category_name'] ?? '';
    $category_type = $_POST['category_type'] ?? '';
    $max_participants = $_POST['max_participants'] ?? null;
    $category_status = $_POST['category_status'] ?? '';
    $referee_name = $_POST['referee_name'] ?? '';
    $referee_email = $_POST['referee_email'] ?? '';
    $venue = $_POST['venue'] ?? '';
    $registration_deadline = $_POST['registration_deadline'] ?? null;
    
    if (!$category_id || !$category_name || !$category_type || !$category_status) {
        throw new Exception('Missing required fields');
    }
    
    // Validate category exists and user has permission
    $sql = "SELECT sc.id, sc.category_name, es.event_id, e.name as event_name
            FROM sport_categories sc
            JOIN event_sports es ON sc.event_sport_id = es.id
            JOIN events e ON es.event_id = e.id
            WHERE sc.id = ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$category_id]);
    $category = $stmt->fetch();
    
    if (!$category) {
        throw new Exception('Category not found');
    }
    
    // Validate email format if provided
    if ($referee_email && !filter_var($referee_email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid referee email format');
    }
    
    // Validate max participants
    if ($max_participants !== null && ($max_participants < 2 || $max_participants > 64)) {
        throw new Exception('Maximum participants must be between 2 and 64');
    }
    
    // Validate category status
    $valid_statuses = ['registration', 'ongoing', 'completed', 'cancelled'];
    if (!in_array($category_status, $valid_statuses)) {
        throw new Exception('Invalid category status');
    }
    
    // Validate category type
    $valid_types = ['men', 'women', 'mixed', 'open', 'youth', 'senior', 'other'];
    if (!in_array($category_type, $valid_types)) {
        throw new Exception('Invalid category type');
    }
    
    // Convert empty registration deadline to null
    if (empty($registration_deadline)) {
        $registration_deadline = null;
    }
    
    // Update category
    $sql = "UPDATE sport_categories SET
                category_name = ?,
                category_type = ?,
                max_participants = ?,
                status = ?,
                referee_name = ?,
                referee_email = ?,
                venue = ?,
                registration_deadline = ?,
                updated_at = NOW()
            WHERE id = ?";
    
    $stmt = $conn->prepare($sql);
    $success = $stmt->execute([
        $category_name,
        $category_type,
        $max_participants,
        $category_status,
        $referee_name ?: null,
        $referee_email ?: null,
        $venue ?: null,
        $registration_deadline,
        $category_id
    ]);
    
    if (!$success) {
        throw new Exception('Failed to update category');
    }
    
    // Log admin activity
    logAdminActivity($conn, $current_admin['id'], 'update_category', 
        "Updated category: {$category_name} in event: {$category['event_name']}", 
        ['category_id' => $category_id, 'event_id' => $category['event_id']]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Category updated successfully',
        'data' => [
            'category_id' => $category_id,
            'category_name' => $category_name,
            'category_type' => $category_type,
            'status' => $category_status
        ]
    ]);
}
?>
