<?php
require_once '../config/database.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Check if tables exist
    $tables = ['matches', 'tournament_formats', 'tournament_structures', 'tournament_rounds'];
    echo "<h2>TABLE EXISTENCE CHECK</h2>\n";
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        $exists = $result->rowCount() > 0;
        echo "<p>$table: " . ($exists ? '<span style="color: green;">EXISTS</span>' : '<span style="color: red;">MISSING</span>') . "</p>\n";
    }
    
    // Check matches table structure
    echo "<h2>MATCHES TABLE STRUCTURE</h2>\n";
    $result = $conn->query('DESCRIBE matches');
    echo "<table border='1'><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr><td>{$row['Field']}</td><td>{$row['Type']}</td><td>{$row['Null']}</td><td>{$row['Key']}</td><td>{$row['Default']}</td></tr>";
    }
    echo "</table>";
    
    // Check tournament_formats table
    echo "<h2>TOURNAMENT_FORMATS TABLE</h2>\n";
    try {
        $result = $conn->query('SELECT COUNT(*) as count FROM tournament_formats');
        $count = $result->fetch(PDO::FETCH_ASSOC)['count'];
        echo "<p>Records: $count</p>\n";
        
        if ($count > 0) {
            echo "<table border='1'><tr><th>ID</th><th>Name</th><th>Code</th></tr>";
            $result = $conn->query('SELECT id, name, code FROM tournament_formats LIMIT 5');
            while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
                echo "<tr><td>{$row['id']}</td><td>{$row['name']}</td><td>{$row['code']}</td></tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error accessing tournament_formats: " . $e->getMessage() . "</p>";
    }
    
    // Check tournament_structures table
    echo "<h2>TOURNAMENT_STRUCTURES TABLE</h2>\n";
    try {
        $result = $conn->query('SELECT COUNT(*) as count FROM tournament_structures');
        $count = $result->fetch(PDO::FETCH_ASSOC)['count'];
        echo "<p>Records: $count</p>\n";
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error accessing tournament_structures: " . $e->getMessage() . "</p>";
    }
    
    // Test actual tournament creation workflow
    echo "<h2>TOURNAMENT CREATION TEST</h2>\n";
    try {
        // Get an event_sport to test with
        $result = $conn->query('SELECT id, sport_name FROM event_sports LIMIT 1');
        $eventSport = $result->fetch(PDO::FETCH_ASSOC);
        
        if ($eventSport) {
            echo "<p>Testing with Event Sport: {$eventSport['sport_name']} (ID: {$eventSport['id']})</p>";
            
            // Try to get tournament formats
            $result = $conn->query('SELECT id, name FROM tournament_formats WHERE id = 1');
            $format = $result->fetch(PDO::FETCH_ASSOC);
            
            if ($format) {
                echo "<p>Using Tournament Format: {$format['name']} (ID: {$format['id']})</p>";
                
                // Test the actual tournament creation process
                require_once '../includes/tournament_manager.php';
                $tournamentManager = new TournamentManager($conn);
                
                $config = [
                    'seeding_method' => 'random',
                    'scoring_config' => [
                        'points_win' => 3,
                        'points_draw' => 1,
                        'points_loss' => 0
                    ]
                ];
                
                echo "<p>Attempting to create tournament...</p>";
                $tournamentId = $tournamentManager->createTournament($eventSport['id'], $format['id'], 'Test Tournament', $config);
                echo "<p style='color: green;'>SUCCESS: Tournament created with ID: $tournamentId</p>";
                
            } else {
                echo "<p style='color: red;'>No tournament formats found</p>";
            }
        } else {
            echo "<p style='color: red;'>No event sports found</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Tournament creation failed: " . $e->getMessage() . "</p>";
        echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
    }
    
} catch (Exception $e) {
    echo '<p style="color: red;">Error: ' . $e->getMessage() . '</p>';
}
?>
