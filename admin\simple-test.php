<?php
echo "<h2>Simple Test</h2>";
echo "<p>Testing basic PHP functionality...</p>";

// Test database connection
try {
    require_once '../config/database.php';
    $database = new Database();
    $conn = $database->getConnection();
    echo "<p>✅ Database connection: SUCCESS</p>";
} catch (Exception $e) {
    echo "<p>❌ Database connection: " . $e->getMessage() . "</p>";
}

// Test if we can access the manage-category.php file
if (file_exists('manage-category.php')) {
    echo "<p>✅ manage-category.php file exists</p>";
    
    // Check file size
    $size = filesize('manage-category.php');
    echo "<p>📄 File size: " . number_format($size) . " bytes</p>";
    
    // Check if it's readable
    if (is_readable('manage-category.php')) {
        echo "<p>✅ File is readable</p>";
    } else {
        echo "<p>❌ File is not readable</p>";
    }
} else {
    echo "<p>❌ manage-category.php file not found</p>";
}

echo "<p>✅ Test completed successfully!</p>";
?>
