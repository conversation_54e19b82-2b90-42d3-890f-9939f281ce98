<?php
/**
 * Quick Tournament System Check
 */

// Simple database connection test
try {
    $conn = new PDO("mysql:host=localhost;dbname=SC_IMS_db", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "Database connection: SUCCESS\n";


    // Check tables
    $tables = ['tournament_formats', 'tournament_structures', 'tournament_rounds', 'tournament_participants'];
    echo "\nDatabase Tables:\n";
    foreach ($tables as $table) {
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        $exists = $stmt->fetch() ? 'EXISTS' : 'MISSING';
        echo "  $table: $exists\n";
    }

    // Check files
    echo "\nCore Files:\n";
    $files = [
        'tournament_manager.php' => '../includes/tournament_manager.php',
        'tournament_algorithms.php' => '../includes/tournament_algorithms.php',
        'manage-category.php' => 'manage-category.php'
    ];

    foreach ($files as $name => $path) {
        if (file_exists($path)) {
            $size = filesize($path);
            echo "  $name: EXISTS ($size bytes)\n";
        } else {
            echo "  $name: MISSING\n";
        }
    }

    echo "\n=== Tournament System Status ===\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
