<?php
/**
 * Validate Tournament Format Display Logic
 * SC_IMS Sports Competition and Event Management System
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/tournament_format_helper.php';

$database = new Database();
$conn = $database->getConnection();

echo "<!DOCTYPE html>";
echo "<html><head><title>Tournament Format Display Logic Validation</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .warning { background: #fff3cd; color: #856404; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .info { background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 5px; margin: 10px 0; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    .validation-section { border: 1px solid #ddd; margin: 15px 0; padding: 15px; border-radius: 8px; }
    .section-header { font-weight: bold; font-size: 1.1em; margin-bottom: 10px; color: #333; }
    .format-card { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 6px; padding: 10px; margin: 5px 0; }
    .format-name { font-weight: bold; color: #495057; }
    .format-category { color: #6c757d; font-size: 0.9em; }
</style></head><body>";

echo "<h1>🔍 Tournament Format Display Logic Validation</h1>";

try {
    // Section 1: Sport Type Categories Validation
    echo "<div class='validation-section'>";
    echo "<div class='section-header'>📊 Section 1: Sport Type Categories Validation</div>";
    
    $result = $conn->query("
        SELECT s.id, s.name as sport_name, st.name as type_name, st.category
        FROM sports s
        LEFT JOIN sport_types st ON s.sport_type_id = st.id
        ORDER BY st.category, s.name
    ");
    $sports = $result->fetchAll(PDO::FETCH_ASSOC);
    
    $categories = ['team', 'individual', 'academic', 'judged'];
    $category_counts = array_fill_keys($categories, 0);
    $unassigned_count = 0;
    
    echo "<h3>Sports by Category:</h3>";
    
    foreach ($categories as $category) {
        $category_sports = array_filter($sports, function($sport) use ($category) {
            return $sport['category'] === $category;
        });
        
        $category_counts[$category] = count($category_sports);
        
        echo "<h4>" . ucfirst($category) . " Sports (" . count($category_sports) . "):</h4>";
        if (!empty($category_sports)) {
            echo "<table>";
            echo "<tr><th>Sport Name</th><th>Type Name</th></tr>";
            foreach ($category_sports as $sport) {
                echo "<tr><td>{$sport['sport_name']}</td><td>{$sport['type_name']}</td></tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='info'>No sports in this category</div>";
        }
    }
    
    // Check unassigned sports
    $unassigned_sports = array_filter($sports, function($sport) {
        return empty($sport['category']);
    });
    $unassigned_count = count($unassigned_sports);
    
    if ($unassigned_count > 0) {
        echo "<h4>Unassigned Sports ($unassigned_count):</h4>";
        echo "<table>";
        echo "<tr><th>Sport Name</th><th>Status</th></tr>";
        foreach ($unassigned_sports as $sport) {
            echo "<tr><td>{$sport['sport_name']}</td><td><span style='color: red;'>Needs Assignment</span></td></tr>";
        }
        echo "</table>";
        echo "<div class='warning'>⚠ These sports need sport type assignment for proper format detection</div>";
    } else {
        echo "<div class='success'>✅ All sports have assigned categories</div>";
    }
    
    echo "</div>";
    
    // Section 2: Tournament Format Mappings Validation
    echo "<div class='validation-section'>";
    echo "<div class='section-header'>🎯 Section 2: Tournament Format Mappings Validation</div>";
    
    $result = $conn->query("SELECT * FROM tournament_formats ORDER BY sport_type_category, name");
    $formats = $result->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Available Tournament Formats by Category:</h3>";
    
    $format_categories = ['team', 'individual', 'academic', 'judged', 'all'];
    
    foreach ($format_categories as $category) {
        $category_formats = array_filter($formats, function($format) use ($category) {
            return ($format['sport_type_category'] ?? 'all') === $category;
        });
        
        echo "<h4>" . ucfirst($category) . " Formats (" . count($category_formats) . "):</h4>";
        
        if (!empty($category_formats)) {
            foreach ($category_formats as $format) {
                echo "<div class='format-card'>";
                echo "<div class='format-name'>{$format['name']}</div>";
                echo "<div class='format-category'>Code: {$format['code']} | Min Participants: {$format['min_participants']}</div>";
                if (!empty($format['description'])) {
                    echo "<div style='font-size: 0.9em; color: #6c757d; margin-top: 5px;'>{$format['description']}</div>";
                }
                echo "</div>";
            }
        } else {
            echo "<div class='warning'>⚠ No formats available for this category</div>";
        }
    }
    
    echo "</div>";
    
    // Section 3: Format Detection Logic Validation
    echo "<div class='validation-section'>";
    echo "<div class='section-header'>⚙️ Section 3: Format Detection Logic Validation</div>";
    
    echo "<h3>Format Detection Test for Each Sport:</h3>";
    
    $detection_results = [];
    
    foreach ($sports as $sport) {
        if (empty($sport['category'])) continue;
        
        $default_format = getDefaultTournamentFormat($conn, $sport['id']);
        
        $detection_results[] = [
            'sport_name' => $sport['sport_name'],
            'category' => $sport['category'],
            'detected_format' => $default_format['name'],
            'format_id' => $default_format['id']
        ];
    }
    
    // Group by category for better display
    foreach ($categories as $category) {
        $category_results = array_filter($detection_results, function($result) use ($category) {
            return $result['category'] === $category;
        });
        
        if (!empty($category_results)) {
            echo "<h4>" . ucfirst($category) . " Sports Format Detection:</h4>";
            echo "<table>";
            echo "<tr><th>Sport Name</th><th>Detected Format</th><th>Format ID</th></tr>";
            foreach ($category_results as $result) {
                echo "<tr>";
                echo "<td>{$result['sport_name']}</td>";
                echo "<td>{$result['detected_format']}</td>";
                echo "<td>{$result['format_id']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    echo "</div>";
    
    // Section 4: Event Sport Display Validation
    echo "<div class='validation-section'>";
    echo "<div class='section-header'>🏆 Section 4: Event Sport Display Validation</div>";
    
    $result = $conn->query("
        SELECT es.id as event_sport_id, s.name as sport_name, e.name as event_name, st.category
        FROM event_sports es
        JOIN sports s ON es.sport_id = s.id
        JOIN events e ON es.event_id = e.id
        LEFT JOIN sport_types st ON s.sport_type_id = st.id
        ORDER BY e.name, s.name
        LIMIT 10
    ");
    $event_sports = $result->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Event Sport Format Display Test:</h3>";
    
    if (!empty($event_sports)) {
        echo "<table>";
        echo "<tr><th>Event</th><th>Sport</th><th>Category</th><th>Display Format</th><th>Has Tournament</th></tr>";
        
        foreach ($event_sports as $event_sport) {
            $display_format = getTournamentFormatDisplay($conn, $event_sport['event_sport_id']);
            
            // Check if tournament exists
            $stmt = $conn->prepare("SELECT id FROM tournament_structures WHERE event_sport_id = ?");
            $stmt->execute([$event_sport['event_sport_id']]);
            $has_tournament = $stmt->fetch() ? 'Yes' : 'No';
            
            echo "<tr>";
            echo "<td>{$event_sport['event_name']}</td>";
            echo "<td>{$event_sport['sport_name']}</td>";
            echo "<td>" . ($event_sport['category'] ?: 'Unassigned') . "</td>";
            echo "<td><strong>$display_format</strong></td>";
            echo "<td>$has_tournament</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='info'>No event sports found for testing</div>";
    }
    
    echo "</div>";
    
    // Section 5: Consistency Validation
    echo "<div class='validation-section'>";
    echo "<div class='section-header'>✅ Section 5: Consistency Validation</div>";
    
    $validation_issues = [];
    
    // Check 1: All sports have categories
    if ($unassigned_count > 0) {
        $validation_issues[] = "$unassigned_count sports are missing category assignments";
    }
    
    // Check 2: All categories have formats
    foreach ($categories as $category) {
        $category_formats = array_filter($formats, function($format) use ($category) {
            return ($format['sport_type_category'] ?? 'all') === $category || ($format['sport_type_category'] ?? 'all') === 'all';
        });
        
        if (empty($category_formats)) {
            $validation_issues[] = "Category '$category' has no available tournament formats";
        }
    }
    
    // Check 3: Format detection consistency
    $format_detection_errors = 0;
    foreach ($detection_results as $result) {
        if (empty($result['detected_format']) || $result['format_id'] <= 0) {
            $format_detection_errors++;
        }
    }
    
    if ($format_detection_errors > 0) {
        $validation_issues[] = "$format_detection_errors sports have format detection errors";
    }
    
    // Display validation results
    if (empty($validation_issues)) {
        echo "<div class='success'>";
        echo "<h3>🎉 All Validations Passed!</h3>";
        echo "<ul>";
        echo "<li>✅ All sports have proper category assignments</li>";
        echo "<li>✅ All categories have available tournament formats</li>";
        echo "<li>✅ Format detection works correctly for all sports</li>";
        echo "<li>✅ Display logic is consistent across the system</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div class='warning'>";
        echo "<h3>⚠ Validation Issues Found:</h3>";
        echo "<ul>";
        foreach ($validation_issues as $issue) {
            echo "<li>$issue</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    echo "</div>";
    
    // Section 6: Recommendations
    echo "<div class='validation-section'>";
    echo "<div class='section-header'>💡 Section 6: Recommendations</div>";
    
    echo "<h3>System Optimization Recommendations:</h3>";
    echo "<ul>";
    echo "<li><strong>Sport Type Assignment:</strong> Ensure all sports have proper type assignments for accurate format detection</li>";
    echo "<li><strong>Format Diversity:</strong> Consider adding more format options for academic and judged categories</li>";
    echo "<li><strong>Default Handling:</strong> The system gracefully falls back to 'Single Elimination' when detection fails</li>";
    echo "<li><strong>Performance:</strong> Format detection is efficient with minimal database queries</li>";
    echo "<li><strong>Consistency:</strong> Display logic is unified across all interfaces</li>";
    echo "</ul>";
    
    echo "<div class='info'>";
    echo "<h4>Next Steps:</h4>";
    echo "<ol>";
    echo "<li>Test tournament creation with different sport types</li>";
    echo "<li>Verify format display in manage-category.php interface</li>";
    echo "<li>Confirm AJAX tournament creation works without errors</li>";
    echo "<li>Validate bracket generation for different formats</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "</div>";
    
    echo "<p><a href='manage-category.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Category Management Interface</a></p>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Validation error: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</body></html>";
?>
