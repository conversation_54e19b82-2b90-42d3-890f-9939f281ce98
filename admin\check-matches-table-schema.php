<?php
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>🔍 Matches Table Schema Investigation</h2>\n";

try {
    // Check if matches table exists
    $result = $conn->query("SHOW TABLES LIKE 'matches'");
    if ($result->rowCount() == 0) {
        echo "<p style='color: red;'>❌ matches table does not exist!</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ matches table exists</p>";
    
    // Get table structure
    echo "<h3>Current Matches Table Structure:</h3>";
    $result = $conn->query("DESCRIBE matches");
    $columns = $result->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $has_tournament_structure_id = false;
    $has_tournament_round_id = false;
    $has_bracket_position = false;
    $has_is_bye_match = false;
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'tournament_structure_id') $has_tournament_structure_id = true;
        if ($column['Field'] === 'tournament_round_id') $has_tournament_round_id = true;
        if ($column['Field'] === 'bracket_position') $has_bracket_position = true;
        if ($column['Field'] === 'is_bye_match') $has_is_bye_match = true;
    }
    echo "</table>";
    
    echo "<h3>Missing Columns Analysis:</h3>";
    echo "<p>tournament_structure_id: " . ($has_tournament_structure_id ? '<span style="color: green;">✅ EXISTS</span>' : '<span style="color: red;">❌ MISSING</span>') . "</p>";
    echo "<p>tournament_round_id: " . ($has_tournament_round_id ? '<span style="color: green;">✅ EXISTS</span>' : '<span style="color: red;">❌ MISSING</span>') . "</p>";
    echo "<p>bracket_position: " . ($has_bracket_position ? '<span style="color: green;">✅ EXISTS</span>' : '<span style="color: red;">❌ MISSING</span>') . "</p>";
    echo "<p>is_bye_match: " . ($has_is_bye_match ? '<span style="color: green;">✅ EXISTS</span>' : '<span style="color: red;">❌ MISSING</span>') . "</p>";
    
    // Check related tables
    echo "<h3>Related Tables Check:</h3>";
    $tables_to_check = ['tournament_structures', 'tournament_rounds', 'tournament_formats'];
    
    foreach ($tables_to_check as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        $exists = $result->rowCount() > 0;
        echo "<p>$table: " . ($exists ? '<span style="color: green;">✅ EXISTS</span>' : '<span style="color: red;">❌ MISSING</span>') . "</p>";
        
        if ($exists) {
            $result = $conn->query("SELECT COUNT(*) as count FROM $table");
            $count = $result->fetch(PDO::FETCH_ASSOC)['count'];
            echo "<p>&nbsp;&nbsp;Records: $count</p>";
        }
    }
    
    // If columns are missing, provide fix
    if (!$has_tournament_structure_id || !$has_tournament_round_id || !$has_bracket_position || !$has_is_bye_match) {
        echo "<h3>🔧 Auto-Fix Available</h3>";
        echo "<p><a href='fix-matches-table-schema.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Run Auto-Fix</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
