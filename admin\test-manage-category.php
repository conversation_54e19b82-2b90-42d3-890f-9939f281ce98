<?php
/**
 * Test script for the redesigned manage-category.php page
 */

// Simulate the required parameters
$_GET['event_id'] = 1;
$_GET['sport_id'] = 1; 
$_GET['category_id'] = 1;

// Capture output
ob_start();

try {
    // Include the manage-category.php file
    include 'manage-category.php';
    
    $output = ob_get_contents();
    ob_end_clean();
    
    echo "<h2>✅ Page Load Test Results</h2>\n";
    echo "<p><strong>Status:</strong> <span style='color: green;'>SUCCESS</span></p>\n";
    echo "<p><strong>Output Length:</strong> " . strlen($output) . " characters</p>\n";
    
    // Check for key elements
    $checks = [
        'category-hero' => 'Modern hero section',
        'nav-tabs' => 'Navigation tabs',
        'content-card' => 'Content cards',
        'showTab(' => 'JavaScript tab functionality',
        'createTournament(' => 'Tournament creation function',
        'alert alert-' => 'Alert components'
    ];
    
    echo "<h3>Component Checks:</h3>\n";
    foreach ($checks as $element => $description) {
        $found = strpos($output, $element) !== false;
        $status = $found ? '✅' : '❌';
        $color = $found ? 'green' : 'red';
        echo "<p>$status <strong>$description:</strong> <span style='color: $color;'>" . ($found ? 'Found' : 'Missing') . "</span></p>\n";
    }
    
    // Check for responsive design
    $responsive_found = strpos($output, '@media (max-width: 768px)') !== false;
    echo "<p>" . ($responsive_found ? '✅' : '❌') . " <strong>Responsive Design:</strong> <span style='color: " . ($responsive_found ? 'green' : 'red') . ";'>" . ($responsive_found ? 'Implemented' : 'Missing') . "</span></p>\n";
    
    echo "<h3>Modern Features:</h3>\n";
    $modern_features = [
        'gradient' => 'CSS gradients',
        'backdrop-filter' => 'Backdrop filters',
        'transform:' => 'CSS transforms',
        'animation:' => 'CSS animations',
        'box-shadow:' => 'Modern shadows'
    ];
    
    foreach ($modern_features as $feature => $description) {
        $found = strpos($output, $feature) !== false;
        $status = $found ? '✅' : '❌';
        $color = $found ? 'green' : 'red';
        echo "<p>$status <strong>$description:</strong> <span style='color: $color;'>" . ($found ? 'Implemented' : 'Missing') . "</span></p>\n";
    }
    
} catch (Exception $e) {
    ob_end_clean();
    echo "<h2>❌ Page Load Test Results</h2>\n";
    echo "<p><strong>Status:</strong> <span style='color: red;'>ERROR</span></p>\n";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>\n";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>\n";
}
?>
