<?php
require_once __DIR__ . '/../config/database.php';

$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    echo "Database connection failed\n";
    exit(1);
}

echo "=== Tournament System Status Check ===\n\n";

// Check if tournament tables exist
$tables = ['tournament_formats', 'tournament_structures', 'tournament_rounds', 'tournament_participants'];
echo "Database Tables:\n";
foreach ($tables as $table) {
    $stmt = $conn->prepare("SHOW TABLES LIKE ?");
    $stmt->execute([$table]);
    $exists = $stmt->fetch() ? 'EXISTS' : 'MISSING';
    echo "  $table: $exists\n";
}

echo "\nCore Files:\n";
// Check if tournament_manager.php exists
if (file_exists(__DIR__ . '/../includes/tournament_manager.php')) {
    echo "  tournament_manager.php: EXISTS\n";
} else {
    echo "  tournament_manager.php: MISSING\n";
}

// Check if tournament_algorithms.php exists
if (file_exists(__DIR__ . '/../includes/tournament_algorithms.php')) {
    echo "  tournament_algorithms.php: EXISTS\n";
} else {
    echo "  tournament_algorithms.php: MISSING\n";
}

// Check if manage-category.php exists and has content
$manage_category_path = __DIR__ . '/manage-category.php';
if (file_exists($manage_category_path)) {
    $content = file_get_contents($manage_category_path);
    $size = strlen(trim($content));
    echo "  manage-category.php: EXISTS (" . $size . " bytes)\n";
} else {
    echo "  manage-category.php: MISSING\n";
}

echo "\n=== End Status Check ===\n";

// Check if tournament_algorithms.php exists
if (file_exists('../includes/tournament_algorithms.php')) {
    echo "tournament_algorithms.php: EXISTS\n";
} else {
    echo "tournament_algorithms.php: MISSING\n";
}
?>
