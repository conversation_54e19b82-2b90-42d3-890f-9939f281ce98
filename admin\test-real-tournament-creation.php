<?php
/**
 * Test Real Tournament Creation
 * SC_IMS Sports Competition and Event Management System
 * 
 * This script tests the actual tournament creation workflow through the real AJAX endpoint
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Real Tournament Creation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .success { color: #28a745; background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .step { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; border: none; cursor: pointer; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        #result { margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Real Tournament Creation</h1>
        <p><strong>Test Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <div class="info">
            <h3>🎯 What This Test Does</h3>
            <p>This test uses the actual AJAX endpoint that the admin interface uses to create tournaments. It simulates exactly what happens when you click "Create Tournament" in the admin panel.</p>
        </div>
        
        <?php
        // Get available event sports for testing
        $stmt = $conn->prepare("
            SELECT es.id, es.sport_name, e.name as event_name, 
                   COUNT(dr.id) as participant_count
            FROM event_sports es
            JOIN events e ON es.event_id = e.id
            LEFT JOIN department_registrations dr ON es.id = dr.event_sport_id
            GROUP BY es.id, es.sport_name, e.name
            HAVING participant_count >= 2
            ORDER BY participant_count DESC
            LIMIT 5
        ");
        $stmt->execute();
        $event_sports = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get available tournament formats
        $stmt = $conn->prepare("SELECT id, name, code, description FROM tournament_formats ORDER BY id");
        $stmt->execute();
        $formats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        ?>
        
        <div class="step">
            <h2>📋 Available Test Data</h2>
            
            <h3>Event Sports with Participants:</h3>
            <table>
                <tr><th>ID</th><th>Sport</th><th>Event</th><th>Participants</th></tr>
                <?php foreach ($event_sports as $es): ?>
                <tr>
                    <td><?php echo $es['id']; ?></td>
                    <td><?php echo htmlspecialchars($es['sport_name']); ?></td>
                    <td><?php echo htmlspecialchars($es['event_name']); ?></td>
                    <td><?php echo $es['participant_count']; ?></td>
                </tr>
                <?php endforeach; ?>
            </table>
            
            <h3>Available Tournament Formats:</h3>
            <table>
                <tr><th>ID</th><th>Name</th><th>Code</th><th>Description</th></tr>
                <?php foreach ($formats as $format): ?>
                <tr>
                    <td><?php echo $format['id']; ?></td>
                    <td><?php echo htmlspecialchars($format['name']); ?></td>
                    <td><?php echo htmlspecialchars($format['code']); ?></td>
                    <td><?php echo htmlspecialchars($format['description']); ?></td>
                </tr>
                <?php endforeach; ?>
            </table>
        </div>
        
        <div class="step">
            <h2>🚀 Test Tournament Creation</h2>
            
            <?php if (!empty($event_sports) && !empty($formats)): ?>
                <form id="tournamentForm">
                    <p>
                        <label for="event_sport_id">Event Sport:</label>
                        <select id="event_sport_id" name="event_sport_id" required>
                            <option value="">Select Event Sport</option>
                            <?php foreach ($event_sports as $es): ?>
                                <option value="<?php echo $es['id']; ?>">
                                    <?php echo htmlspecialchars($es['sport_name'] . ' - ' . $es['event_name'] . ' (' . $es['participant_count'] . ' participants)'); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </p>
                    
                    <p>
                        <label for="format_id">Tournament Format:</label>
                        <select id="format_id" name="format_id" required>
                            <option value="">Select Format</option>
                            <?php foreach ($formats as $format): ?>
                                <option value="<?php echo $format['id']; ?>">
                                    <?php echo htmlspecialchars($format['name'] . ' - ' . $format['description']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </p>
                    
                    <p>
                        <label for="tournament_name">Tournament Name:</label>
                        <input type="text" id="tournament_name" name="tournament_name" value="Test Tournament <?php echo date('H:i:s'); ?>" required>
                    </p>
                    
                    <p>
                        <label for="seeding_method">Seeding Method:</label>
                        <select id="seeding_method" name="seeding_method">
                            <option value="random">Random</option>
                            <option value="ranking">Ranking</option>
                            <option value="manual">Manual</option>
                        </select>
                    </p>
                    
                    <p>
                        <button type="submit" class="btn btn-success">🧪 Create Test Tournament</button>
                    </p>
                </form>
            <?php else: ?>
                <div class="error">
                    <h3>❌ No Test Data Available</h3>
                    <p>No event sports with participants or tournament formats found. Please ensure:</p>
                    <ul>
                        <li>Events and sports are created</li>
                        <li>Departments are registered for events</li>
                        <li>Tournament formats are available</li>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
        
        <div id="result"></div>
        
        <div class="step">
            <h2>🔧 Quick Actions</h2>
            <p>
                <a href="definitive-database-fix.php" class="btn">🔧 Run Database Fix</a>
                <a href="check-database-state.php" class="btn">🔍 Check Database State</a>
                <a href="manage-event.php?event_id=1" class="btn">📋 Manage Events</a>
                <a href="index.php" class="btn">🏠 Admin Dashboard</a>
            </p>
        </div>
    </div>
    
    <script>
        document.getElementById('tournamentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><h3>🔄 Creating Tournament...</h3><p>Please wait while we test the tournament creation process.</p></div>';
            
            const formData = new FormData(this);
            
            fetch('ajax/create-tournament.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>🎉 TOURNAMENT CREATION SUCCESSFUL!</h3>
                            <p><strong>✅ The database fix worked!</strong></p>
                            <p><strong>Tournament ID:</strong> ${data.tournament_id}</p>
                            <p><strong>Tournament Name:</strong> ${data.tournament_name}</p>
                            <p><strong>Participants:</strong> ${data.participants_count}</p>
                            <p><strong>Format:</strong> ${data.format_name}</p>
                            <p><strong>Message:</strong> ${data.message}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Tournament Creation Failed</h3>
                            <p><strong>Error:</strong> ${data.message}</p>
                            <p>This indicates that the database fix may not have been applied correctly or there are other issues.</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Request Failed</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p>There was a problem communicating with the server.</p>
                    </div>
                `;
            });
        });
    </script>
</body>
</html>
