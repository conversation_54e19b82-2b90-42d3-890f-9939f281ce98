<?php
/**
 * Test Add Sport with Tournament Format
 * Verify that tournament format is properly saved when adding sport to event
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Add Sport Tournament Format</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .btn { padding: 8px 16px; margin: 5px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; border: none; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .form-group { margin: 10px 0; }
        .form-control { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Test Add Sport with Tournament Format</h1>

    <div class="section info">
        <h2>1. Available Test Data</h2>
        <?php
        // Get available events
        $stmt = $conn->prepare("SELECT id, name FROM events ORDER BY id LIMIT 5");
        $stmt->execute();
        $events = $stmt->fetchAll();
        
        // Get available sports
        $stmt = $conn->prepare("SELECT id, name FROM sports ORDER BY id LIMIT 5");
        $stmt->execute();
        $sports = $stmt->fetchAll();
        
        // Get available tournament formats
        $stmt = $conn->prepare("SELECT id, name, code FROM tournament_formats ORDER BY id");
        $stmt->execute();
        $formats = $stmt->fetchAll();
        
        echo '<h4>Events:</h4>';
        if (!empty($events)) {
            echo '<table><tr><th>ID</th><th>Name</th></tr>';
            foreach ($events as $event) {
                echo '<tr><td>' . $event['id'] . '</td><td>' . htmlspecialchars($event['name']) . '</td></tr>';
            }
            echo '</table>';
        } else {
            echo '<p class="error">No events found. Please create an event first.</p>';
        }
        
        echo '<h4>Sports:</h4>';
        if (!empty($sports)) {
            echo '<table><tr><th>ID</th><th>Name</th></tr>';
            foreach ($sports as $sport) {
                echo '<tr><td>' . $sport['id'] . '</td><td>' . htmlspecialchars($sport['name']) . '</td></tr>';
            }
            echo '</table>';
        } else {
            echo '<p class="error">No sports found. Please create a sport first.</p>';
        }
        
        echo '<h4>Tournament Formats:</h4>';
        if (!empty($formats)) {
            echo '<table><tr><th>ID</th><th>Name</th><th>Code</th></tr>';
            foreach ($formats as $format) {
                echo '<tr><td>' . $format['id'] . '</td><td>' . htmlspecialchars($format['name']) . '</td><td>' . htmlspecialchars($format['code']) . '</td></tr>';
            }
            echo '</table>';
        } else {
            echo '<p class="error">No tournament formats found.</p>';
        }
        ?>
    </div>

    <div class="section info">
        <h2>2. Test Add Sport to Event</h2>
        <form id="testForm">
            <div class="form-group">
                <label>Event:</label>
                <select name="event_id" class="form-control" required>
                    <option value="">Select Event...</option>
                    <?php foreach ($events as $event): ?>
                        <option value="<?php echo $event['id']; ?>"><?php echo htmlspecialchars($event['name']); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label>Sport:</label>
                <select name="sport_id" class="form-control" required>
                    <option value="">Select Sport...</option>
                    <?php foreach ($sports as $sport): ?>
                        <option value="<?php echo $sport['id']; ?>"><?php echo htmlspecialchars($sport['name']); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label>Tournament Format:</label>
                <select name="tournament_format_id" class="form-control" required>
                    <option value="">Select Tournament Format...</option>
                    <?php foreach ($formats as $format): ?>
                        <option value="<?php echo $format['id']; ?>"><?php echo htmlspecialchars($format['name']); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label>Max Teams:</label>
                <input type="number" name="max_teams" class="form-control" value="8" min="2" max="64">
            </div>
            
            <div class="form-group">
                <label>Seeding Method:</label>
                <select name="seeding_method" class="form-control">
                    <option value="random">Random</option>
                    <option value="manual">Manual</option>
                    <option value="ranking">By Ranking</option>
                </select>
            </div>
            
            <button type="submit" class="btn">Test Add Sport</button>
        </form>
        
        <div id="testResult" style="margin-top: 20px;"></div>
    </div>

    <div class="section info">
        <h2>3. Current Event Sports</h2>
        <div id="currentEventSports">
            <?php
            $stmt = $conn->prepare("
                SELECT es.id, es.event_id, es.sport_id, es.tournament_format_id, 
                       e.name as event_name, s.name as sport_name, tf.name as format_name
                FROM event_sports es
                JOIN events e ON es.event_id = e.id
                JOIN sports s ON es.sport_id = s.id
                LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
                ORDER BY es.id DESC
                LIMIT 10
            ");
            $stmt->execute();
            $event_sports = $stmt->fetchAll();
            
            if (!empty($event_sports)) {
                echo '<table>';
                echo '<tr><th>ID</th><th>Event</th><th>Sport</th><th>Format ID</th><th>Format Name</th></tr>';
                foreach ($event_sports as $es) {
                    echo '<tr>';
                    echo '<td>' . $es['id'] . '</td>';
                    echo '<td>' . htmlspecialchars($es['event_name']) . '</td>';
                    echo '<td>' . htmlspecialchars($es['sport_name']) . '</td>';
                    echo '<td>' . ($es['tournament_format_id'] ?: 'NULL') . '</td>';
                    echo '<td>' . htmlspecialchars($es['format_name'] ?: 'Not set') . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
            } else {
                echo '<p>No event sports found.</p>';
            }
            ?>
        </div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            formData.append('action', 'add_sport');
            formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');
            
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = '<p>Testing...</p>';
            
            fetch('ajax/event-management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success"><h4>✓ Success!</h4><p>' + data.message + '</p></div>';
                    // Refresh the current event sports list
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    resultDiv.innerHTML = '<div class="error"><h4>✗ Error</h4><p>' + data.message + '</p></div>';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="error"><h4>✗ Network Error</h4><p>' + error.message + '</p></div>';
            });
        });
    </script>

</body>
</html>
