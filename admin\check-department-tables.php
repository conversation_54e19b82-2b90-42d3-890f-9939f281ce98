<?php
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>Department-Related Tables Check</h2>\n";

// Check for department-related tables
$tables_to_check = [
    'departments',
    'registrations', 
    'department_registrations',
    'event_department_registrations',
    'department_sport_participations',
    'department_overall_scores',
    'event_overall_standings'
];

foreach ($tables_to_check as $table) {
    try {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        $exists = $result->rowCount() > 0;
        echo "<p>$table: " . ($exists ? '<span style="color: green;">EXISTS</span>' : '<span style="color: red;">MISSING</span>') . "</p>\n";
        
        if ($exists) {
            // Show record count
            $result = $conn->query("SELECT COUNT(*) as count FROM $table");
            $count = $result->fetch(PDO::FETCH_ASSOC)['count'];
            echo "<p>&nbsp;&nbsp;Records: $count</p>\n";
        }
    } catch (Exception $e) {
        echo "<p>$table: <span style='color: red;'>ERROR - " . $e->getMessage() . "</span></p>\n";
    }
}

// If department_registrations doesn't exist but event_department_registrations does, 
// create a view for backward compatibility
try {
    $result = $conn->query("SHOW TABLES LIKE 'department_registrations'");
    $dept_reg_exists = $result->rowCount() > 0;
    
    $result = $conn->query("SHOW TABLES LIKE 'event_department_registrations'");
    $event_dept_reg_exists = $result->rowCount() > 0;
    
    if (!$dept_reg_exists && $event_dept_reg_exists) {
        echo "<h3>Creating backward compatibility view...</h3>\n";
        
        // Create a view that maps the old table name to the new structure
        $sql = "CREATE OR REPLACE VIEW department_registrations AS 
                SELECT 
                    dsp.id,
                    dsp.event_sport_id,
                    edr.department_id,
                    dsp.team_name,
                    dsp.participants,
                    dsp.participation_date as registration_date,
                    dsp.status,
                    dsp.created_at,
                    dsp.updated_at
                FROM department_sport_participations dsp
                JOIN event_department_registrations edr ON dsp.event_department_registration_id = edr.id";
        
        $conn->exec($sql);
        echo "<p style='color: green;'>✅ Created department_registrations view for backward compatibility</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error creating compatibility view: " . $e->getMessage() . "</p>\n";
}

echo "<h3>Testing department_registrations access...</h3>\n";
try {
    $result = $conn->query("SELECT COUNT(*) as count FROM department_registrations");
    $count = $result->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<p style='color: green;'>✅ department_registrations accessible with $count records</p>\n";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error accessing department_registrations: " . $e->getMessage() . "</p>\n";
}
?>
