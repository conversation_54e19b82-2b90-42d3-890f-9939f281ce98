<?php
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/tournament_manager.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>🔍 Tournament Creation Debug</h2>\n";

try {
    // Test the exact SQL that's failing
    echo "<h3>Testing Tournament Structure Creation</h3>";
    
    // First, let's check what tables exist
    $result = $conn->query("SHOW TABLES");
    $tables = $result->fetchAll(PDO::FETCH_COLUMN);
    echo "<p>Available tables: " . implode(', ', $tables) . "</p>";
    
    // Check matches table structure again
    echo "<h3>Matches Table Structure</h3>";
    $result = $conn->query("DESCRIBE matches");
    $columns = $result->fetchAll(PDO::FETCH_ASSOC);
    
    $column_names = array_column($columns, 'Field');
    echo "<p>Matches table columns: " . implode(', ', $column_names) . "</p>";
    
    // Check if tournament_structure_id exists
    if (in_array('tournament_structure_id', $column_names)) {
        echo "<p style='color: green;'>✅ tournament_structure_id column exists in matches table</p>";
    } else {
        echo "<p style='color: red;'>❌ tournament_structure_id column missing from matches table</p>";
    }
    
    // Test the exact INSERT statement that's failing
    echo "<h3>Testing INSERT Statement</h3>";
    
    $test_sql = "INSERT INTO matches
                (event_sport_id, tournament_structure_id, tournament_round_id, team1_id, team2_id,
                 round_number, bracket_position, is_bye_match, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    echo "<p>SQL Statement:</p>";
    echo "<pre>$test_sql</pre>";
    
    // Try to prepare the statement
    try {
        $stmt = $conn->prepare($test_sql);
        echo "<p style='color: green;'>✅ SQL statement prepared successfully</p>";
        
        // Test with dummy data
        $test_params = [1, 1, 1, 1, 2, 1, 'TEST', 0, 'scheduled'];
        echo "<p>Test parameters: " . implode(', ', $test_params) . "</p>";
        
        // Don't actually execute, just test preparation
        echo "<p style='color: blue;'>ℹ Statement preparation successful - SQL syntax is correct</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ SQL preparation failed: " . $e->getMessage() . "</p>";
    }
    
    // Check tournament_structures table
    echo "<h3>Tournament Structures Table</h3>";
    $result = $conn->query("DESCRIBE tournament_structures");
    $ts_columns = $result->fetchAll(PDO::FETCH_ASSOC);
    $ts_column_names = array_column($ts_columns, 'Field');
    echo "<p>Tournament structures columns: " . implode(', ', $ts_column_names) . "</p>";
    
    // Check tournament_formats table
    echo "<h3>Tournament Formats Table</h3>";
    $result = $conn->query("SELECT COUNT(*) as count FROM tournament_formats");
    $format_count = $result->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<p>Tournament formats available: $format_count</p>";
    
    if ($format_count > 0) {
        $result = $conn->query("SELECT id, name, code FROM tournament_formats LIMIT 5");
        $formats = $result->fetchAll(PDO::FETCH_ASSOC);
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Name</th><th>Code</th></tr>";
        foreach ($formats as $format) {
            echo "<tr><td>{$format['id']}</td><td>{$format['name']}</td><td>{$format['code']}</td></tr>";
        }
        echo "</table>";
    }
    
    // Test a simple tournament creation with minimal data
    echo "<h3>Testing Tournament Manager</h3>";
    
    // Check if we have any event_sports to test with
    $result = $conn->query("SELECT es.id, s.name as sport_name, e.name as event_name 
                           FROM event_sports es 
                           JOIN sports s ON es.sport_id = s.id 
                           JOIN events e ON es.event_id = e.id 
                           LIMIT 1");
    $test_event_sport = $result->fetch(PDO::FETCH_ASSOC);
    
    if ($test_event_sport) {
        echo "<p>Test event sport found: {$test_event_sport['sport_name']} in {$test_event_sport['event_name']}</p>";
        
        // Check for participants
        $stmt = $conn->prepare("
            SELECT COUNT(*) as count
            FROM event_department_registrations edr
            JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
            WHERE dsp.event_sport_id = ? AND edr.status IN ('approved', 'pending') AND dsp.status = 'confirmed'
        ");
        $stmt->execute([$test_event_sport['id']]);
        $participant_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        echo "<p>Participants available: $participant_count</p>";
        
        if ($participant_count >= 2) {
            echo "<p style='color: green;'>✅ Sufficient participants for tournament creation test</p>";
        } else {
            echo "<p style='color: orange;'>⚠ Not enough participants for full test</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠ No event sports found for testing</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p><pre>" . $e->getTraceAsString() . "</pre>";
}
?>
