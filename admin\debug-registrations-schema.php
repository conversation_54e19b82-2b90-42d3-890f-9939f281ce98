<?php
/**
 * Debug Registrations Schema and Data
 * Check for issues with registrations table structure and data
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Registrations Schema</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Debug Registrations Schema and Data</h1>

    <div class="section info">
        <h2>1. Registrations Table Structure</h2>
        <?php
        try {
            $stmt = $conn->prepare("DESCRIBE registrations");
            $stmt->execute();
            $columns = $stmt->fetchAll();
            
            echo '<table>';
            echo '<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>';
            foreach ($columns as $column) {
                echo '<tr>';
                echo '<td>' . htmlspecialchars($column['Field']) . '</td>';
                echo '<td>' . htmlspecialchars($column['Type']) . '</td>';
                echo '<td>' . htmlspecialchars($column['Null']) . '</td>';
                echo '<td>' . htmlspecialchars($column['Key']) . '</td>';
                echo '<td>' . htmlspecialchars($column['Default'] ?? 'NULL') . '</td>';
                echo '<td>' . htmlspecialchars($column['Extra']) . '</td>';
                echo '</tr>';
            }
            echo '</table>';
            
            // Check for missing columns
            $column_names = array_column($columns, 'Field');
            $required_columns = ['id', 'event_sport_id', 'department_id', 'participants', 'status'];
            $optional_columns = ['created_at', 'registration_date', 'team_name', 'updated_at'];
            
            echo '<h4>Column Analysis:</h4>';
            echo '<ul>';
            foreach ($required_columns as $col) {
                if (in_array($col, $column_names)) {
                    echo '<li class="success">✓ Required column: ' . $col . '</li>';
                } else {
                    echo '<li class="error">✗ Missing required column: ' . $col . '</li>';
                }
            }
            foreach ($optional_columns as $col) {
                if (in_array($col, $column_names)) {
                    echo '<li class="info">ℹ Optional column present: ' . $col . '</li>';
                } else {
                    echo '<li class="warning">⚠ Optional column missing: ' . $col . '</li>';
                }
            }
            echo '</ul>';
            
        } catch (Exception $e) {
            echo '<p class="error">Error checking table structure: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>

    <div class="section info">
        <h2>2. Sample Registration Data</h2>
        <?php
        try {
            $stmt = $conn->prepare("SELECT * FROM registrations LIMIT 5");
            $stmt->execute();
            $registrations = $stmt->fetchAll();
            
            if (!empty($registrations)) {
                echo '<table>';
                $first_row = $registrations[0];
                echo '<tr>';
                foreach (array_keys($first_row) as $key) {
                    if (!is_numeric($key)) {
                        echo '<th>' . htmlspecialchars($key) . '</th>';
                    }
                }
                echo '</tr>';
                
                foreach ($registrations as $reg) {
                    echo '<tr>';
                    foreach ($reg as $key => $value) {
                        if (!is_numeric($key)) {
                            if ($key === 'participants') {
                                $decoded = json_decode($value, true);
                                if (json_last_error() === JSON_ERROR_NONE) {
                                    echo '<td>JSON (' . count($decoded) . ' items)</td>';
                                } else {
                                    echo '<td>Invalid JSON</td>';
                                }
                            } else {
                                echo '<td>' . htmlspecialchars($value ?? 'NULL') . '</td>';
                            }
                        }
                    }
                    echo '</tr>';
                }
                echo '</table>';
            } else {
                echo '<p class="warning">No registration data found.</p>';
            }
            
        } catch (Exception $e) {
            echo '<p class="error">Error retrieving registration data: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>

    <div class="section info">
        <h2>3. Test getRegistrations Function</h2>
        <?php
        try {
            // Get an event_sport_id to test with
            $stmt = $conn->prepare("SELECT id FROM event_sports LIMIT 1");
            $stmt->execute();
            $event_sport_id = $stmt->fetchColumn();
            
            if ($event_sport_id) {
                echo '<p>Testing with event_sport_id: ' . $event_sport_id . '</p>';
                
                require_once '../includes/functions.php';
                $test_registrations = getRegistrations($conn, $event_sport_id);
                
                echo '<h4>Function Result:</h4>';
                if (!empty($test_registrations)) {
                    echo '<pre>' . print_r($test_registrations, true) . '</pre>';
                } else {
                    echo '<p class="warning">No registrations found for this event sport.</p>';
                }
            } else {
                echo '<p class="warning">No event sports found to test with.</p>';
            }
            
        } catch (Exception $e) {
            echo '<p class="error">Error testing getRegistrations function: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>

    <div class="section info">
        <h2>4. Fix Missing Columns (if needed)</h2>
        <?php
        if (isset($_POST['fix_columns'])) {
            try {
                echo '<h4>Fixing missing columns:</h4>';
                
                // Check and add created_at column if missing
                $stmt = $conn->prepare("SHOW COLUMNS FROM registrations LIKE 'created_at'");
                $stmt->execute();
                if (!$stmt->fetch()) {
                    $conn->exec("ALTER TABLE registrations ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
                    echo '<p class="success">✓ Added created_at column</p>';
                } else {
                    echo '<p class="info">ℹ created_at column already exists</p>';
                }
                
                // Check and add registration_date column if missing
                $stmt = $conn->prepare("SHOW COLUMNS FROM registrations LIKE 'registration_date'");
                $stmt->execute();
                if (!$stmt->fetch()) {
                    $conn->exec("ALTER TABLE registrations ADD COLUMN registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
                    echo '<p class="success">✓ Added registration_date column</p>';
                } else {
                    echo '<p class="info">ℹ registration_date column already exists</p>';
                }
                
                // Update existing records with NULL dates
                $stmt = $conn->prepare("UPDATE registrations SET created_at = NOW() WHERE created_at IS NULL");
                $stmt->execute();
                $updated = $stmt->rowCount();
                if ($updated > 0) {
                    echo "<p class=\"success\">✓ Updated {$updated} records with current timestamp</p>";
                }
                
                $stmt = $conn->prepare("UPDATE registrations SET registration_date = NOW() WHERE registration_date IS NULL");
                $stmt->execute();
                $updated = $stmt->rowCount();
                if ($updated > 0) {
                    echo "<p class=\"success\">✓ Updated {$updated} records with current registration date</p>";
                }
                
                echo '<p class="success"><strong>✓ Column fixes completed!</strong></p>';
                
            } catch (Exception $e) {
                echo '<p class="error">Error fixing columns: ' . htmlspecialchars($e->getMessage()) . '</p>';
            }
        }
        ?>
        <form method="POST">
            <button type="submit" name="fix_columns" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
                Fix Missing Columns
            </button>
        </form>
    </div>

    <div class="section info">
        <h2>5. Create Test Registration (if needed)</h2>
        <?php
        if (isset($_POST['create_test_data'])) {
            try {
                // Get first available event_sport and department
                $stmt = $conn->prepare("SELECT id FROM event_sports LIMIT 1");
                $stmt->execute();
                $event_sport_id = $stmt->fetchColumn();
                
                $stmt = $conn->prepare("SELECT id FROM departments LIMIT 1");
                $stmt->execute();
                $department_id = $stmt->fetchColumn();
                
                if ($event_sport_id && $department_id) {
                    // Check if registration already exists
                    $stmt = $conn->prepare("SELECT id FROM registrations WHERE event_sport_id = ? AND department_id = ?");
                    $stmt->execute([$event_sport_id, $department_id]);
                    
                    if (!$stmt->fetch()) {
                        $participants = json_encode([
                            ['name' => 'Test Player 1', 'position' => 'Captain'],
                            ['name' => 'Test Player 2', 'position' => 'Member'],
                            ['name' => 'Test Player 3', 'position' => 'Member']
                        ]);
                        
                        $stmt = $conn->prepare("INSERT INTO registrations (event_sport_id, department_id, participants, status, created_at, registration_date) VALUES (?, ?, ?, 'confirmed', NOW(), NOW())");
                        $stmt->execute([$event_sport_id, $department_id, $participants]);
                        
                        echo '<p class="success">✓ Created test registration</p>';
                    } else {
                        echo '<p class="info">ℹ Test registration already exists</p>';
                    }
                } else {
                    echo '<p class="error">✗ Missing event_sports or departments data</p>';
                }
                
            } catch (Exception $e) {
                echo '<p class="error">Error creating test data: ' . htmlspecialchars($e->getMessage()) . '</p>';
            }
        }
        ?>
        <form method="POST">
            <button type="submit" name="create_test_data" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
                Create Test Registration
            </button>
        </form>
    </div>

</body>
</html>
