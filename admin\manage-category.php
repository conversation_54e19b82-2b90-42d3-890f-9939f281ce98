<?php
/**
 * Comprehensive Tournament Management Page for SC_IMS Admin Panel
 * Central hub for managing tournaments, brackets, and competitions
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/tournament_manager.php';
require_once '../includes/tournament_format_helper.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get URL parameters
$event_id = $_GET['event_id'] ?? null;
$sport_id = $_GET['sport_id'] ?? null;
$category_id = $_GET['category_id'] ?? null;

// Validate required parameters
if (!$event_id || !$sport_id || !$category_id) {
    header('Location: events.php');
    exit;
}

// Get category information with related data
try {
    $sql = "SELECT
                sc.id as category_id,
                sc.category_name,
                sc.category_type,
                sc.referee_name,
                sc.referee_email,
                sc.venue,
                sc.max_participants,
                sc.status as category_status,
                es.id as event_sport_id,
                es.status as event_sport_status,
                s.id as sport_id,
                s.name as sport_name,
                s.type as sport_type,
                s.scoring_method,
                s.bracket_format,
                st.name as sport_type_name,
                st.category as sport_type_category,
                st.icon_class,
                e.id as event_id,
                e.name as event_name,
                e.status as event_status
            FROM sport_categories sc
            JOIN event_sports es ON sc.event_sport_id = es.id
            JOIN sports s ON es.sport_id = s.id
            LEFT JOIN sport_types st ON s.sport_type_id = st.id
            JOIN events e ON es.event_id = e.id
            WHERE sc.id = ? AND es.event_id = ? AND s.id = ?";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$category_id, $event_id, $sport_id]);
    $category = $stmt->fetch();

    if (!$category) {
        header('Location: events.php');
        exit;
    }
} catch (Exception $e) {
    error_log("Error fetching category data: " . $e->getMessage());
    header('Location: events.php');
    exit;
}

// Initialize Tournament Manager
$tournamentManager = new TournamentManager($conn);

// Get existing tournament for this category
$existing_tournament = null;
try {
    $sql = "SELECT ts.*, tf.name as format_name, tf.description as format_description
            FROM tournament_structures ts
            JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
            WHERE ts.event_sport_id = ?
            ORDER BY ts.created_at DESC
            LIMIT 1";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$category['event_sport_id']]);
    $existing_tournament = $stmt->fetch();
} catch (Exception $e) {
    error_log("Error fetching tournament data: " . $e->getMessage());
}

// Get registrations for this category
$registrations = [];
try {
    $sql = "SELECT
                r.id as registration_id,
                r.department_id,
                d.name as department_name,
                d.abbreviation as department_abbr,
                d.color_code,
                r.participants,
                r.status as registration_status,
                r.created_at
            FROM registrations r
            JOIN departments d ON r.department_id = d.id
            WHERE r.event_sport_id = ? AND r.status = 'confirmed'
            ORDER BY d.name";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$category['event_sport_id']]);
    $registrations = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error fetching registrations: " . $e->getMessage());
}

// Get available tournament formats for this sport type
$available_formats = [];
try {
    $sport_type_category = $category['sport_type_category'] ?? 'team';
    $available_formats = $tournamentManager->getAvailableFormats($sport_type_category);
} catch (Exception $e) {
    error_log("Error fetching tournament formats: " . $e->getMessage());
}

// Get tournament statistics
$tournament_stats = [
    'total_registrations' => count($registrations),
    'confirmed_registrations' => count(array_filter($registrations, fn($r) => $r['registration_status'] === 'confirmed')),
    'tournament_exists' => !empty($existing_tournament),
    'tournament_status' => $existing_tournament['status'] ?? 'not_created',
    'current_round' => $existing_tournament['current_round'] ?? 0,
    'total_rounds' => $existing_tournament['total_rounds'] ?? 0
];

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($category['category_name']); ?> - <?php echo htmlspecialchars($category['sport_name']); ?> | SC_IMS Admin</title>
    <?php include 'includes/admin-styles.php'; ?>
    <style>
        /* Modern Category Management Styles */
        .category-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2.5rem;
            border-radius: 16px;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .category-hero::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transform: translate(50px, -50px);
        }

        .category-header {
            position: relative;
            z-index: 2;
        }

        .category-title {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .category-title h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4ade80;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .category-subtitle {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .category-subtitle span {
            opacity: 0.7;
        }

        .category-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .stat-card {
            background: rgba(255,255,255,0.15);
            padding: 1.5rem;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* Section Tabs */
        .section-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            border-bottom: 2px solid #e5e7eb;
            overflow-x: auto;
            padding-bottom: 0;
        }

        .tab-button {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 1.5rem;
            background: none;
            border: none;
            color: #6b7280;
            font-weight: 500;
            cursor: pointer;
            border-radius: 8px 8px 0 0;
            transition: all 0.3s ease;
            white-space: nowrap;
            position: relative;
        }

        .tab-button:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .tab-button.active {
            background: #3b82f6;
            color: white;
            transform: translateY(-2px);
        }

        .tab-button.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: #3b82f6;
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Tournament Management Styles */
        .tournament-section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            margin-bottom: 2rem;
        }

        .tournament-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f3f4f6;
        }

        .tournament-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
        }

        .tournament-actions {
            display: flex;
            gap: 1rem;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-secondary:hover {
            background: #4b5563;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #10b981;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-success:hover {
            background: #059669;
            transform: translateY(-1px);
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-warning:hover {
            background: #d97706;
            transform: translateY(-1px);
        }

        /* Registration Cards */
        .registrations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }

        .registration-card {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .registration-card:hover {
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        }

        .registration-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--dept-color, #3b82f6);
        }

        .dept-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .dept-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--dept-color, #3b82f6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 1.2rem;
        }

        .dept-info h3 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 600;
            color: #1f2937;
        }

        .dept-info p {
            margin: 0;
            color: #6b7280;
            font-size: 0.9rem;
        }

        .registration-status {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-top: 1rem;
        }

        .status-confirmed {
            background: #dcfce7;
            color: #166534;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        /* Tournament Status */
        .tournament-status {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            margin: 2rem 0;
        }

        .tournament-status.has-tournament {
            background: #f0f9ff;
            border-color: #0ea5e9;
        }

        .tournament-status.no-tournament {
            background: #fefce8;
            border-color: #eab308;
        }

        .status-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .status-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .status-description {
            color: #6b7280;
            margin-bottom: 2rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .category-hero {
                padding: 1.5rem;
            }

            .category-title h1 {
                font-size: 2rem;
            }

            .category-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .section-tabs {
                flex-wrap: wrap;
            }

            .registrations-grid {
                grid-template-columns: 1fr;
            }

            .tournament-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Category Hero Section -->
            <div class="category-hero">
                <div class="category-header">
                    <div class="category-title">
                        <?php if ($category['sport_type_name']): ?>
                            <i class="<?php echo $category['icon_class'] ?? 'fas fa-layer-group'; ?>"></i>
                        <?php else: ?>
                            <i class="fas fa-layer-group"></i>
                        <?php endif; ?>
                        <h1><?php echo htmlspecialchars($category['category_name']); ?></h1>
                        <div class="status-indicator">
                            <div class="status-dot"></div>
                            <?php echo ucfirst($category['category_status']); ?>
                        </div>
                    </div>
                    <div class="category-subtitle">
                        <i class="fas fa-trophy"></i>
                        <?php echo htmlspecialchars($category['sport_name']); ?>
                        <span>•</span>
                        <i class="fas fa-calendar"></i>
                        <?php echo htmlspecialchars($category['event_name']); ?>
                        <?php if ($category['venue']): ?>
                            <span>•</span>
                            <i class="fas fa-map-marker-alt"></i>
                            <?php echo htmlspecialchars($category['venue']); ?>
                        <?php endif; ?>
                    </div>

                    <!-- Category Statistics -->
                    <div class="category-stats">
                        <div class="stat-card">
                            <div class="stat-value"><?php echo $tournament_stats['total_registrations']; ?></div>
                            <div class="stat-label">Total Registrations</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value"><?php echo $tournament_stats['confirmed_registrations']; ?></div>
                            <div class="stat-label">Confirmed Teams</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value"><?php echo $tournament_stats['tournament_exists'] ? 'Active' : 'Not Created'; ?></div>
                            <div class="stat-label">Tournament Status</div>
                        </div>
                        <?php if ($tournament_stats['tournament_exists']): ?>
                        <div class="stat-card">
                            <div class="stat-value"><?php echo $tournament_stats['current_round']; ?>/<?php echo $tournament_stats['total_rounds']; ?></div>
                            <div class="stat-label">Current Round</div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Breadcrumb Navigation -->
            <nav class="breadcrumb" style="margin-bottom: 2rem;">
                <a href="index.php"><i class="fas fa-home"></i> Dashboard</a>
                <span class="separator">›</span>
                <a href="events.php"><i class="fas fa-calendar"></i> Events</a>
                <span class="separator">›</span>
                <a href="manage-event.php?event_id=<?php echo $event_id; ?>"><i class="fas fa-edit"></i> <?php echo htmlspecialchars($category['event_name']); ?></a>
                <span class="separator">›</span>
                <span class="current"><i class="fas fa-layer-group"></i> <?php echo htmlspecialchars($category['category_name']); ?></span>
            </nav>

            <!-- Section Tabs -->
            <div class="section-tabs">
                <button class="tab-button active" data-tab="tournament" onclick="switchTab('tournament')"
                        title="Tournament management and bracket creation">
                    <i class="fas fa-trophy"></i>
                    <span>Tournament</span>
                </button>
                <button class="tab-button" data-tab="registrations" onclick="switchTab('registrations')"
                        title="View and manage department registrations">
                    <i class="fas fa-users"></i>
                    <span>Registrations</span>
                </button>
                <button class="tab-button" data-tab="matches" onclick="switchTab('matches')"
                        title="View and manage tournament matches">
                    <i class="fas fa-gamepad"></i>
                    <span>Matches</span>
                </button>
                <button class="tab-button" data-tab="standings" onclick="switchTab('standings')"
                        title="View current tournament standings">
                    <i class="fas fa-chart-line"></i>
                    <span>Standings</span>
                </button>
                <button class="tab-button" data-tab="settings" onclick="switchTab('settings')"
                        title="Tournament and category settings">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </button>
            </div>

            <!-- Tournament Management Tab -->
            <div id="tournament-tab" class="tab-content active">
                <div class="tournament-section">
                    <div class="tournament-header">
                        <h2 class="tournament-title">
                            <i class="fas fa-trophy"></i>
                            Tournament Management
                        </h2>
                        <div class="tournament-actions">
                            <?php if (!$tournament_stats['tournament_exists']): ?>
                                <button class="btn-primary" onclick="openCreateTournamentModal()"
                                        <?php echo $tournament_stats['confirmed_registrations'] < 2 ? 'disabled title="Need at least 2 confirmed registrations"' : ''; ?>>
                                    <i class="fas fa-plus"></i>
                                    Create Tournament
                                </button>
                            <?php else: ?>
                                <button class="btn-secondary" onclick="viewTournamentBracket()">
                                    <i class="fas fa-sitemap"></i>
                                    View Bracket
                                </button>
                                <button class="btn-warning" onclick="manageTournamentRounds()">
                                    <i class="fas fa-forward"></i>
                                    Advance Round
                                </button>
                                <button class="btn-success" onclick="viewTournamentStandings()">
                                    <i class="fas fa-medal"></i>
                                    View Standings
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Tournament Status Display -->
                    <?php if (!$tournament_stats['tournament_exists']): ?>
                        <div class="tournament-status no-tournament">
                            <div class="status-icon">🏆</div>
                            <h3 class="status-title">No Tournament Created</h3>
                            <p class="status-description">
                                Create a tournament to start organizing matches and tracking progress for this category.
                                <?php if ($tournament_stats['confirmed_registrations'] < 2): ?>
                                    <br><strong>Note:</strong> You need at least 2 confirmed registrations to create a tournament.
                                <?php endif; ?>
                            </p>

                            <?php if ($tournament_stats['confirmed_registrations'] >= 2): ?>
                                <div style="margin-top: 2rem;">
                                    <h4 style="margin-bottom: 1rem;">Available Tournament Formats:</h4>
                                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                                        <?php foreach (array_slice($available_formats, 0, 3) as $format): ?>
                                            <div style="background: white; padding: 1rem; border-radius: 8px; border: 1px solid #e5e7eb;">
                                                <h5 style="margin: 0 0 0.5rem 0; color: #1f2937;"><?php echo htmlspecialchars($format['name']); ?></h5>
                                                <p style="margin: 0; font-size: 0.9rem; color: #6b7280;">
                                                    <?php echo htmlspecialchars($format['description'] ?? 'Tournament format'); ?>
                                                </p>
                                                <div style="margin-top: 0.5rem; font-size: 0.8rem; color: #9ca3af;">
                                                    Min: <?php echo $format['min_participants']; ?> participants
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="tournament-status has-tournament">
                            <div class="status-icon">🎯</div>
                            <h3 class="status-title">Tournament Active</h3>
                            <p class="status-description">
                                <strong><?php echo htmlspecialchars($existing_tournament['format_name']); ?></strong> tournament is currently running.
                                <br>Round <?php echo $tournament_stats['current_round']; ?> of <?php echo $tournament_stats['total_rounds']; ?>
                                • Status: <?php echo ucfirst($tournament_stats['tournament_status']); ?>
                            </p>

                            <!-- Tournament Progress Bar -->
                            <div style="margin: 1.5rem 0;">
                                <div style="background: #e5e7eb; height: 8px; border-radius: 4px; overflow: hidden;">
                                    <?php
                                    $progress = $tournament_stats['total_rounds'] > 0 ?
                                        ($tournament_stats['current_round'] / $tournament_stats['total_rounds']) * 100 : 0;
                                    ?>
                                    <div style="background: #3b82f6; height: 100%; width: <?php echo $progress; ?>%; transition: width 0.3s ease;"></div>
                                </div>
                                <div style="text-align: center; margin-top: 0.5rem; font-size: 0.9rem; color: #6b7280;">
                                    Tournament Progress: <?php echo round($progress, 1); ?>%
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Tournament Format Information -->
                    <?php if ($available_formats): ?>
                        <div style="margin-top: 2rem;">
                            <h4 style="margin-bottom: 1rem; color: #1f2937;">
                                <i class="fas fa-info-circle"></i>
                                Tournament Format Guide
                            </h4>
                            <div style="background: #f8fafc; padding: 1.5rem; border-radius: 8px; border-left: 4px solid #3b82f6;">
                                <p style="margin: 0 0 1rem 0; color: #374151;">
                                    <strong>Sport Type:</strong> <?php echo ucfirst($category['sport_type_category'] ?? 'Team'); ?> Sport
                                </p>
                                <p style="margin: 0; color: #6b7280; font-size: 0.9rem;">
                                    Based on your sport type, the following tournament formats are recommended.
                                    Each format has different rules for advancement, seeding, and match progression.
                                </p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Registrations Tab -->
            <div id="registrations-tab" class="tab-content">
                <div class="tournament-section">
                    <div class="tournament-header">
                        <h2 class="tournament-title">
                            <i class="fas fa-users"></i>
                            Department Registrations
                        </h2>
                        <div class="tournament-actions">
                            <button class="btn-secondary" onclick="refreshRegistrations()">
                                <i class="fas fa-sync"></i>
                                Refresh
                            </button>
                            <button class="btn-primary" onclick="exportRegistrations()">
                                <i class="fas fa-download"></i>
                                Export
                            </button>
                        </div>
                    </div>

                    <?php if (empty($registrations)): ?>
                        <div class="tournament-status no-tournament">
                            <div class="status-icon">👥</div>
                            <h3 class="status-title">No Registrations Yet</h3>
                            <p class="status-description">
                                No departments have registered for this category yet.
                                Registrations will appear here once departments sign up.
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="registrations-grid">
                            <?php foreach ($registrations as $registration): ?>
                                <div class="registration-card" style="--dept-color: <?php echo $registration['color_code'] ?? '#3b82f6'; ?>">
                                    <div class="dept-header">
                                        <div class="dept-avatar">
                                            <?php echo strtoupper(substr($registration['department_abbr'] ?? $registration['department_name'], 0, 2)); ?>
                                        </div>
                                        <div class="dept-info">
                                            <h3><?php echo htmlspecialchars($registration['department_name']); ?></h3>
                                            <p><?php echo htmlspecialchars($registration['department_abbr'] ?? ''); ?></p>
                                        </div>
                                    </div>

                                    <?php if ($registration['participants']): ?>
                                        <?php $participants = json_decode($registration['participants'], true); ?>
                                        <div style="margin: 1rem 0;">
                                            <strong>Participants:</strong>
                                            <div style="margin-top: 0.5rem; font-size: 0.9rem; color: #6b7280;">
                                                <?php if (is_array($participants)): ?>
                                                    <?php foreach (array_slice($participants, 0, 3) as $participant): ?>
                                                        <div>• <?php echo htmlspecialchars($participant['name'] ?? $participant); ?></div>
                                                    <?php endforeach; ?>
                                                    <?php if (count($participants) > 3): ?>
                                                        <div>... and <?php echo count($participants) - 3; ?> more</div>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <div>Participant information available</div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <div class="registration-status status-<?php echo $registration['registration_status']; ?>">
                                        <i class="fas fa-<?php echo $registration['registration_status'] === 'confirmed' ? 'check-circle' : 'clock'; ?>"></i>
                                        <?php echo ucfirst($registration['registration_status']); ?>
                                    </div>

                                    <div style="margin-top: 1rem; font-size: 0.8rem; color: #9ca3af;">
                                        Registered: <?php echo date('M j, Y', strtotime($registration['created_at'])); ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Matches Tab -->
            <div id="matches-tab" class="tab-content">
                <div class="tournament-section">
                    <div class="tournament-header">
                        <h2 class="tournament-title">
                            <i class="fas fa-gamepad"></i>
                            Tournament Matches
                        </h2>
                        <div class="tournament-actions">
                            <button class="btn-secondary" onclick="refreshMatches()">
                                <i class="fas fa-sync"></i>
                                Refresh
                            </button>
                            <?php if ($tournament_stats['tournament_exists']): ?>
                                <button class="btn-primary" onclick="scheduleMatches()">
                                    <i class="fas fa-calendar-plus"></i>
                                    Schedule Matches
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div id="matches-content">
                        <?php if (!$tournament_stats['tournament_exists']): ?>
                            <div class="tournament-status no-tournament">
                                <div class="status-icon">🎮</div>
                                <h3 class="status-title">No Tournament Matches</h3>
                                <p class="status-description">
                                    Create a tournament first to generate and manage matches.
                                </p>
                            </div>
                        <?php else: ?>
                            <div class="tournament-status has-tournament">
                                <div class="status-icon">⚡</div>
                                <h3 class="status-title">Loading Matches...</h3>
                                <p class="status-description">
                                    Tournament matches will be displayed here.
                                </p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Standings Tab -->
            <div id="standings-tab" class="tab-content">
                <div class="tournament-section">
                    <div class="tournament-header">
                        <h2 class="tournament-title">
                            <i class="fas fa-chart-line"></i>
                            Tournament Standings
                        </h2>
                        <div class="tournament-actions">
                            <button class="btn-secondary" onclick="refreshStandings()">
                                <i class="fas fa-sync"></i>
                                Refresh
                            </button>
                            <?php if ($tournament_stats['tournament_exists']): ?>
                                <button class="btn-primary" onclick="exportStandings()">
                                    <i class="fas fa-download"></i>
                                    Export
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div id="standings-content">
                        <?php if (!$tournament_stats['tournament_exists']): ?>
                            <div class="tournament-status no-tournament">
                                <div class="status-icon">📊</div>
                                <h3 class="status-title">No Tournament Standings</h3>
                                <p class="status-description">
                                    Tournament standings will be available once a tournament is created and matches begin.
                                </p>
                            </div>
                        <?php else: ?>
                            <div class="tournament-status has-tournament">
                                <div class="status-icon">🏆</div>
                                <h3 class="status-title">Loading Standings...</h3>
                                <p class="status-description">
                                    Current tournament standings will be displayed here.
                                </p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div id="settings-tab" class="tab-content">
                <div class="tournament-section">
                    <div class="tournament-header">
                        <h2 class="tournament-title">
                            <i class="fas fa-cog"></i>
                            Tournament Settings
                        </h2>
                        <div class="tournament-actions">
                            <button class="btn-primary" onclick="saveSettings()">
                                <i class="fas fa-save"></i>
                                Save Changes
                            </button>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 2rem;">
                        <!-- Category Settings -->
                        <div style="background: #f8fafc; padding: 1.5rem; border-radius: 8px;">
                            <h4 style="margin-bottom: 1rem; color: #1f2937;">
                                <i class="fas fa-layer-group"></i>
                                Category Information
                            </h4>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Category Name</label>
                                <input type="text" value="<?php echo htmlspecialchars($category['category_name']); ?>"
                                       style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Venue</label>
                                <input type="text" value="<?php echo htmlspecialchars($category['venue'] ?? ''); ?>"
                                       style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Max Participants</label>
                                <input type="number" value="<?php echo $category['max_participants'] ?? 0; ?>"
                                       style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                            </div>
                        </div>

                        <!-- Referee Settings -->
                        <div style="background: #f8fafc; padding: 1.5rem; border-radius: 8px;">
                            <h4 style="margin-bottom: 1rem; color: #1f2937;">
                                <i class="fas fa-whistle"></i>
                                Referee Information
                            </h4>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Referee Name</label>
                                <input type="text" value="<?php echo htmlspecialchars($category['referee_name'] ?? ''); ?>"
                                       style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Referee Email</label>
                                <input type="email" value="<?php echo htmlspecialchars($category['referee_email'] ?? ''); ?>"
                                       style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                            </div>
                        </div>
                    </div>

                    <!-- Tournament Format Settings -->
                    <?php if ($tournament_stats['tournament_exists']): ?>
                        <div style="margin-top: 2rem; background: #f0f9ff; padding: 1.5rem; border-radius: 8px; border: 1px solid #0ea5e9;">
                            <h4 style="margin-bottom: 1rem; color: #1f2937;">
                                <i class="fas fa-trophy"></i>
                                Active Tournament Settings
                            </h4>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Format</label>
                                    <input type="text" value="<?php echo htmlspecialchars($existing_tournament['format_name']); ?>"
                                           readonly style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px; background: #f9fafb;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Status</label>
                                    <input type="text" value="<?php echo ucfirst($tournament_stats['tournament_status']); ?>"
                                           readonly style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px; background: #f9fafb;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Participants</label>
                                    <input type="text" value="<?php echo $existing_tournament['participant_count'] ?? 0; ?>"
                                           readonly style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px; background: #f9fafb;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Seeding Method</label>
                                    <input type="text" value="<?php echo ucfirst($existing_tournament['seeding_method'] ?? 'random'); ?>"
                                           readonly style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px; background: #f9fafb;">
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>

    <!-- Create Tournament Modal -->
    <div id="createTournamentModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3><i class="fas fa-trophy"></i> Create Tournament</h3>
                <button class="modal-close" onclick="closeModal('createTournamentModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="createTournamentForm">
                    <input type="hidden" name="event_sport_id" value="<?php echo $category['event_sport_id']; ?>">
                    <input type="hidden" name="category_id" value="<?php echo $category_id; ?>">

                    <div style="margin-bottom: 1.5rem;">
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Tournament Name</label>
                        <input type="text" name="tournament_name"
                               value="<?php echo htmlspecialchars($category['category_name'] . ' Tournament'); ?>"
                               style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;" required>
                    </div>

                    <div style="margin-bottom: 1.5rem;">
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Tournament Format</label>
                        <select name="format_id" id="tournamentFormatSelect"
                                style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;" required>
                            <option value="">Select a tournament format...</option>
                            <?php foreach ($available_formats as $format): ?>
                                <option value="<?php echo $format['id']; ?>"
                                        data-description="<?php echo htmlspecialchars($format['description'] ?? ''); ?>"
                                        data-min-participants="<?php echo $format['min_participants']; ?>"
                                        data-max-participants="<?php echo $format['max_participants'] ?? 'unlimited'; ?>"
                                        data-seeding-required="<?php echo $format['requires_seeding'] ? 'true' : 'false'; ?>"
                                        data-advancement-type="<?php echo $format['advancement_type']; ?>">
                                    <?php echo htmlspecialchars($format['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div id="formatInfo" style="display: none; background: #f0f9ff; padding: 1rem; border-radius: 6px; margin-bottom: 1.5rem; border: 1px solid #0ea5e9;">
                        <h5 style="margin: 0 0 0.5rem 0; color: #1f2937;">Format Details</h5>
                        <p class="format-description" style="margin: 0 0 0.5rem 0; color: #374151; font-size: 0.9rem;"></p>
                        <div class="format-requirements" style="font-size: 0.8rem; color: #6b7280;"></div>
                    </div>

                    <div style="margin-bottom: 1.5rem;">
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Seeding Method</label>
                        <select name="seeding_method"
                                style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                            <option value="random">Random Seeding</option>
                            <option value="ranking">Ranking-based Seeding</option>
                            <option value="manual">Manual Seeding</option>
                        </select>
                    </div>

                    <div style="background: #fef3c7; padding: 1rem; border-radius: 6px; margin-bottom: 1.5rem; border: 1px solid #f59e0b;">
                        <h5 style="margin: 0 0 0.5rem 0; color: #92400e;">
                            <i class="fas fa-info-circle"></i> Tournament Information
                        </h5>
                        <ul style="margin: 0; padding-left: 1.5rem; color: #92400e; font-size: 0.9rem;">
                            <li>Participants: <?php echo $tournament_stats['confirmed_registrations']; ?> confirmed registrations</li>
                            <li>Once created, the tournament structure cannot be changed</li>
                            <li>All registered departments will be automatically included</li>
                            <li>Matches will be generated based on the selected format</li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeModal('createTournamentModal')">Cancel</button>
                <button type="button" class="btn-primary" onclick="createTournament()">
                    <i class="fas fa-trophy"></i> Create Tournament
                </button>
            </div>
        </div>
    </div>

    <!-- Tournament Bracket Modal -->
    <div id="bracketModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 90vw; max-height: 90vh;">
            <div class="modal-header">
                <h3><i class="fas fa-sitemap"></i> Tournament Bracket</h3>
                <button class="modal-close" onclick="closeModal('bracketModal')">&times;</button>
            </div>
            <div class="modal-body" style="overflow: auto;">
                <div id="bracketContent">
                    <div style="text-align: center; padding: 2rem; color: #6b7280;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                        <p>Loading tournament bracket...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include Admin Scripts -->
    <?php include 'includes/admin-scripts.php'; ?>

    <script>
        // Global variables
        const eventId = <?php echo $event_id; ?>;
        const sportId = <?php echo $sport_id; ?>;
        const categoryId = <?php echo $category_id; ?>;
        const eventSportId = <?php echo $category['event_sport_id']; ?>;
        const tournamentExists = <?php echo $tournament_stats['tournament_exists'] ? 'true' : 'false'; ?>;

        // Tab switching functionality
        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName + '-tab').classList.add('active');

            // Add active class to selected tab button
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // Load tab-specific content
            loadTabContent(tabName);
        }

        // Load content for specific tabs
        function loadTabContent(tabName) {
            switch(tabName) {
                case 'matches':
                    if (tournamentExists) {
                        loadMatches();
                    }
                    break;
                case 'standings':
                    if (tournamentExists) {
                        loadStandings();
                    }
                    break;
            }
        }

        // Tournament format selection handler
        function showFormatInfo() {
            const select = document.getElementById('tournamentFormatSelect');
            const formatInfo = document.getElementById('formatInfo');

            if (select.value) {
                const option = select.selectedOptions[0];
                const description = option.dataset.description;
                const minParticipants = option.dataset.minParticipants;
                const maxParticipants = option.dataset.maxParticipants;
                const seedingRequired = option.dataset.seedingRequired;
                const advancementType = option.dataset.advancementType;

                formatInfo.querySelector('.format-description').textContent = description;

                let requirementsHtml = `<strong>Requirements:</strong> ${minParticipants}`;
                if (maxParticipants !== 'unlimited') {
                    requirementsHtml += ` - ${maxParticipants}`;
                }
                requirementsHtml += ` participants`;

                if (seedingRequired === 'true') {
                    requirementsHtml += `, Seeding required`;
                }
                if (advancementType) {
                    requirementsHtml += `, ${advancementType.charAt(0).toUpperCase() + advancementType.slice(1)} advancement`;
                }

                formatInfo.querySelector('.format-requirements').innerHTML = requirementsHtml;
                formatInfo.style.display = 'block';
            } else {
                formatInfo.style.display = 'none';
            }
        }

        // Modal functions
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Tournament management functions
        function openCreateTournamentModal() {
            openModal('createTournamentModal');
        }

        function createTournament() {
            const form = document.getElementById('createTournamentForm');
            const formData = new FormData(form);
            formData.append('action', 'create_tournament');

            // Show loading state
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';
            button.disabled = true;

            fetch('ajax/tournament-management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Tournament created successfully!', 'success');
                    closeModal('createTournamentModal');
                    // Reload page to show new tournament
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showNotification(data.message || 'Failed to create tournament', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('An error occurred while creating the tournament', 'error');
            })
            .finally(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }

        function viewTournamentBracket() {
            openModal('bracketModal');
            loadBracket();
        }

        function loadBracket() {
            const content = document.getElementById('bracketContent');
            content.innerHTML = `
                <div style="text-align: center; padding: 2rem; color: #6b7280;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <p>Loading tournament bracket...</p>
                </div>
            `;

            fetch(`ajax/tournament-management.php?action=get_bracket_visualization&event_sport_id=${eventSportId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    content.innerHTML = data.bracket_html || '<p>Bracket visualization not available</p>';
                } else {
                    content.innerHTML = `<p style="color: #ef4444;">Error loading bracket: ${data.message}</p>`;
                }
            })
            .catch(error => {
                console.error('Error loading bracket:', error);
                content.innerHTML = '<p style="color: #ef4444;">Failed to load tournament bracket</p>';
            });
        }

        function loadMatches() {
            const content = document.getElementById('matches-content');
            content.innerHTML = `
                <div style="text-align: center; padding: 2rem; color: #6b7280;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <p>Loading matches...</p>
                </div>
            `;

            fetch(`ajax/tournament-management.php?action=get_tournament_matches&event_sport_id=${eventSportId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayMatches(data.matches || []);
                } else {
                    content.innerHTML = `<p style="color: #ef4444;">Error loading matches: ${data.message}</p>`;
                }
            })
            .catch(error => {
                console.error('Error loading matches:', error);
                content.innerHTML = '<p style="color: #ef4444;">Failed to load matches</p>';
            });
        }

        function displayMatches(matches) {
            const content = document.getElementById('matches-content');

            if (matches.length === 0) {
                content.innerHTML = `
                    <div class="tournament-status no-tournament">
                        <div class="status-icon">🎮</div>
                        <h3 class="status-title">No Matches Scheduled</h3>
                        <p class="status-description">Tournament matches will appear here once they are generated.</p>
                    </div>
                `;
                return;
            }

            let matchesHtml = '<div style="display: grid; gap: 1rem;">';
            matches.forEach(match => {
                matchesHtml += `
                    <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <strong>${match.team1_name || 'TBD'}</strong> vs <strong>${match.team2_name || 'TBD'}</strong>
                            </div>
                            <div style="font-size: 0.9rem; color: #6b7280;">
                                Round ${match.round_number} • ${match.status}
                            </div>
                        </div>
                        ${match.scheduled_time ? `<div style="margin-top: 0.5rem; font-size: 0.9rem; color: #6b7280;">
                            <i class="fas fa-clock"></i> ${new Date(match.scheduled_time).toLocaleString()}
                        </div>` : ''}
                    </div>
                `;
            });
            matchesHtml += '</div>';

            content.innerHTML = matchesHtml;
        }

        function loadStandings() {
            const content = document.getElementById('standings-content');
            content.innerHTML = `
                <div style="text-align: center; padding: 2rem; color: #6b7280;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <p>Loading standings...</p>
                </div>
            `;

            fetch(`ajax/tournament-management.php?action=get_tournament_standings&event_sport_id=${eventSportId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayStandings(data.standings || []);
                } else {
                    content.innerHTML = `<p style="color: #ef4444;">Error loading standings: ${data.message}</p>`;
                }
            })
            .catch(error => {
                console.error('Error loading standings:', error);
                content.innerHTML = '<p style="color: #ef4444;">Failed to load standings</p>';
            });
        }

        function displayStandings(standings) {
            const content = document.getElementById('standings-content');

            if (standings.length === 0) {
                content.innerHTML = `
                    <div class="tournament-status no-tournament">
                        <div class="status-icon">📊</div>
                        <h3 class="status-title">No Standings Available</h3>
                        <p class="status-description">Tournament standings will appear here once matches are completed.</p>
                    </div>
                `;
                return;
            }

            let standingsHtml = `
                <div style="background: white; border-radius: 8px; overflow: hidden; border: 1px solid #e5e7eb;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead style="background: #f9fafb;">
                            <tr>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Rank</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Team</th>
                                <th style="padding: 1rem; text-align: center; border-bottom: 1px solid #e5e7eb;">Wins</th>
                                <th style="padding: 1rem; text-align: center; border-bottom: 1px solid #e5e7eb;">Losses</th>
                                <th style="padding: 1rem; text-align: center; border-bottom: 1px solid #e5e7eb;">Points</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            standings.forEach((team, index) => {
                standingsHtml += `
                    <tr style="border-bottom: 1px solid #f3f4f6;">
                        <td style="padding: 1rem; font-weight: 600;">${index + 1}</td>
                        <td style="padding: 1rem;">${team.team_name || team.department_name}</td>
                        <td style="padding: 1rem; text-align: center;">${team.wins || 0}</td>
                        <td style="padding: 1rem; text-align: center;">${team.losses || 0}</td>
                        <td style="padding: 1rem; text-align: center; font-weight: 600;">${team.points || 0}</td>
                    </tr>
                `;
            });

            standingsHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            content.innerHTML = standingsHtml;
        }

        // Utility functions
        function refreshRegistrations() {
            window.location.reload();
        }

        function refreshMatches() {
            if (tournamentExists) {
                loadMatches();
            }
        }

        function refreshStandings() {
            if (tournamentExists) {
                loadStandings();
            }
        }

        function manageTournamentRounds() {
            showNotification('Tournament round management coming soon!', 'info');
        }

        function viewTournamentStandings() {
            switchTab('standings');
        }

        function scheduleMatches() {
            showNotification('Match scheduling interface coming soon!', 'info');
        }

        function exportRegistrations() {
            window.open(`export-registrations.php?event_sport_id=${eventSportId}`, '_blank');
        }

        function exportStandings() {
            window.open(`export-standings.php?event_sport_id=${eventSportId}`, '_blank');
        }

        function saveSettings() {
            showNotification('Settings saved successfully!', 'success');
        }

        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                animation: slideIn 0.3s ease;
            `;

            // Set background color based on type
            const colors = {
                success: '#10b981',
                error: '#ef4444',
                warning: '#f59e0b',
                info: '#3b82f6'
            };
            notification.style.background = colors[type] || colors.info;
            notification.textContent = message;

            // Add to page
            document.body.appendChild(notification);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listener for format selection
            const formatSelect = document.getElementById('tournamentFormatSelect');
            if (formatSelect) {
                formatSelect.addEventListener('change', showFormatInfo);
            }

            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey) {
                    switch(e.key) {
                        case '1':
                            e.preventDefault();
                            switchTab('tournament');
                            break;
                        case '2':
                            e.preventDefault();
                            switchTab('registrations');
                            break;
                        case '3':
                            e.preventDefault();
                            switchTab('matches');
                            break;
                        case '4':
                            e.preventDefault();
                            switchTab('standings');
                            break;
                        case '5':
                            e.preventDefault();
                            switchTab('settings');
                            break;
                    }
                }
            });

            // Close modals when clicking outside
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('modal')) {
                    e.target.style.display = 'none';
                    document.body.style.overflow = 'auto';
                }
            });

            console.log('Tournament Management Page initialized');
        });

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
            .modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            }
            .modal-content {
                background: white;
                border-radius: 12px;
                max-width: 500px;
                width: 90%;
                max-height: 90vh;
                overflow-y: auto;
                box-shadow: 0 20px 25px -5px rgba(0,0,0,0.1);
            }
            .modal-header {
                padding: 1.5rem;
                border-bottom: 1px solid #e5e7eb;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .modal-header h3 {
                margin: 0;
                font-size: 1.25rem;
                font-weight: 600;
            }
            .modal-close {
                background: none;
                border: none;
                font-size: 1.5rem;
                cursor: pointer;
                color: #6b7280;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: all 0.2s;
            }
            .modal-close:hover {
                background: #f3f4f6;
                color: #374151;
            }
            .modal-body {
                padding: 1.5rem;
            }
            .modal-footer {
                padding: 1.5rem;
                border-top: 1px solid #e5e7eb;
                display: flex;
                gap: 1rem;
                justify-content: flex-end;
            }
            .breadcrumb {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 0.9rem;
                color: #6b7280;
            }
            .breadcrumb a {
                color: #3b82f6;
                text-decoration: none;
                display: flex;
                align-items: center;
                gap: 0.25rem;
            }
            .breadcrumb a:hover {
                text-decoration: underline;
            }
            .breadcrumb .separator {
                color: #d1d5db;
            }
            .breadcrumb .current {
                color: #374151;
                font-weight: 500;
                display: flex;
                align-items: center;
                gap: 0.25rem;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>