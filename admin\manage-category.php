<?php
/**
 * Sport Category Management Page for SC_IMS Admin Panel
 * Dedicated interface for managing sport category settings and administration
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get URL parameters
$event_id = $_GET['event_id'] ?? null;
$sport_id = $_GET['sport_id'] ?? null;
$category_id = $_GET['category_id'] ?? null;

// Validate required parameters
if (!$event_id || !$sport_id || !$category_id) {
    header('Location: events.php');
    exit;
}

// Get category information with related data
try {
    $sql = "SELECT
                sc.id as category_id,
                sc.category_name,
                sc.category_type,
                sc.referee_name,
                sc.referee_email,
                sc.venue,
                sc.max_participants,
                sc.status as category_status,
                es.id as event_sport_id,
                es.status as event_sport_status,
                s.id as sport_id,
                s.name as sport_name,
                s.type as sport_type,
                s.scoring_method,
                st.name as sport_type_name,
                st.category as sport_type_category,
                st.icon_class,
                e.id as event_id,
                e.name as event_name,
                e.status as event_status,
                e.start_date,
                e.end_date
            FROM sport_categories sc
            JOIN event_sports es ON sc.event_sport_id = es.id
            JOIN sports s ON es.sport_id = s.id
            LEFT JOIN sport_types st ON s.sport_type_id = st.id
            JOIN events e ON es.event_id = e.id
            WHERE sc.id = ? AND es.event_id = ? AND s.id = ?";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$category_id, $event_id, $sport_id]);
    $category = $stmt->fetch();

    if (!$category) {
        header('Location: events.php');
        exit;
    }
} catch (Exception $e) {
    error_log("Error fetching category data: " . $e->getMessage());
    header('Location: events.php');
    exit;
}

// Get registrations for this category
$registrations = [];
try {
    $sql = "SELECT
                r.id as registration_id,
                r.department_id,
                d.name as department_name,
                d.abbreviation as department_abbr,
                d.color_code,
                r.team_name,
                r.participants,
                r.status as registration_status,
                r.registration_date
            FROM registrations r
            JOIN departments d ON r.department_id = d.id
            WHERE r.event_sport_id = ?
            ORDER BY d.name";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$category['event_sport_id']]);
    $registrations = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error fetching registrations: " . $e->getMessage());
}

// Get matches for this category
$matches = [];
$tournament_info = null;
try {
    // First check if there's a tournament structure
    $stmt = $conn->prepare("
        SELECT ts.*, tf.name as format_name, tf.description as format_description
        FROM tournament_structures ts
        JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
        WHERE ts.event_sport_id = ?
        ORDER BY ts.created_at DESC LIMIT 1
    ");
    $stmt->execute([$category['event_sport_id']]);
    $tournament_info = $stmt->fetch();

    // Get matches for this category
    $sql = "SELECT
                m.*,
                r1.team_name as team1_name,
                r1.department_id as team1_dept_id,
                d1.name as team1_dept_name,
                d1.abbreviation as team1_abbr,
                d1.color_code as team1_color,
                r2.team_name as team2_name,
                r2.department_id as team2_dept_id,
                d2.name as team2_dept_name,
                d2.abbreviation as team2_abbr,
                d2.color_code as team2_color
            FROM matches m
            LEFT JOIN registrations r1 ON m.team1_id = r1.id
            LEFT JOIN departments d1 ON r1.department_id = d1.id
            LEFT JOIN registrations r2 ON m.team2_id = r2.id
            LEFT JOIN departments d2 ON r2.department_id = d2.id
            WHERE m.event_sport_id = ?
            ORDER BY m.scheduled_time ASC, m.round_number ASC, m.match_number ASC";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$category['event_sport_id']]);
    $matches = $stmt->fetchAll();

    // Debug: Log what we found
    error_log("Tournament info found: " . ($tournament_info ? "Yes" : "No"));
    error_log("Matches found: " . count($matches));

} catch (Exception $e) {
    error_log("Error fetching matches: " . $e->getMessage());
}

// Calculate category statistics
$category_stats = [
    'total_registrations' => count($registrations),
    'confirmed_registrations' => count(array_filter($registrations, fn($r) => $r['registration_status'] === 'confirmed')),
    'total_matches' => count($matches),
    'completed_matches' => count(array_filter($matches, fn($m) => $m['status'] === 'completed')),
    'scheduled_matches' => count(array_filter($matches, fn($m) => $m['status'] === 'scheduled')),
    'ongoing_matches' => count(array_filter($matches, fn($m) => $m['status'] === 'ongoing'))
];

// Calculate standings from match results
$standings = [];
$team_stats = [];

// Initialize team stats from registrations
foreach ($registrations as $registration) {
    if ($registration['registration_status'] === 'confirmed') {
        $team_stats[$registration['registration_id']] = [
            'registration_id' => $registration['registration_id'],
            'team_name' => $registration['team_name'] ?: $registration['department_name'],
            'department_name' => $registration['department_name'],
            'department_abbr' => $registration['department_abbr'],
            'color_code' => $registration['color_code'],
            'wins' => 0,
            'losses' => 0,
            'draws' => 0,
            'points' => 0,
            'matches_played' => 0
        ];
    }
}

// Calculate stats from completed matches
foreach ($matches as $match) {
    if ($match['status'] === 'completed' && $match['winner_id']) {
        // Update winner stats
        if (isset($team_stats[$match['team1_id']])) {
            $team_stats[$match['team1_id']]['matches_played']++;
            if ($match['winner_id'] == $match['team1_id']) {
                $team_stats[$match['team1_id']]['wins']++;
                $team_stats[$match['team1_id']]['points'] += 3;
            } else {
                $team_stats[$match['team1_id']]['losses']++;
            }
        }
        
        if (isset($team_stats[$match['team2_id']])) {
            $team_stats[$match['team2_id']]['matches_played']++;
            if ($match['winner_id'] == $match['team2_id']) {
                $team_stats[$match['team2_id']]['wins']++;
                $team_stats[$match['team2_id']]['points'] += 3;
            } else {
                $team_stats[$match['team2_id']]['losses']++;
            }
        }
    }
}

// Sort standings by points, then wins
$standings = array_values($team_stats);
usort($standings, function($a, $b) {
    if ($a['points'] != $b['points']) {
        return $b['points'] <=> $a['points'];
    }
    if ($a['wins'] != $b['wins']) {
        return $b['wins'] <=> $a['wins'];
    }
    return $a['losses'] <=> $b['losses'];
});

// Add rank to standings
foreach ($standings as $index => &$standing) {
    $standing['rank'] = $index + 1;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($category['category_name']); ?> - Category Management | SC_IMS Admin</title>

    <?php include 'includes/admin-styles.php'; ?>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* Category-specific styles */
        .category-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Header */
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            opacity: 0.9;
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .breadcrumb {
            background: rgba(255, 255, 255, 0.1);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .status-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            text-transform: uppercase;
            font-weight: 500;
        }

        /* Tabs */
        .tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .tab-button {
            flex: 1;
            padding: 1rem 2rem;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .tab-button:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .tab-button.active {
            background: #3b82f6;
            color: white;
        }

        /* Tab Content */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Cards */
        .card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        /* Form Styles */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        /* Button Styles */
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        .btn-info {
            background: #0ea5e9;
            color: white;
        }

        .btn-info:hover {
            background: #0284c7;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #6b7280;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: #f3f4f6;
            color: #374151;
        }

        /* Statistics Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            text-align: center;
            padding: 1.5rem;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #6b7280;
            font-size: 0.9rem;
        }

        /* Table Styles */
        .table-container {
            overflow-x: auto;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .table th,
        .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }

        .table tr:hover {
            background: #f9fafb;
        }



        /* Responsive */
        @media (max-width: 768px) {
            .category-container {
                padding: 1rem;
            }

            .tabs {
                flex-direction: column;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .page-title {
                font-size: 1.5rem;
            }

            .page-subtitle {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content Area -->
    <div class="admin-main">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="breadcrumb">
                    <div class="breadcrumb-item">
                        <a href="events.php" style="color: inherit; text-decoration: none;">
                            <i class="fas fa-calendar-alt"></i>
                            <span>Events</span>
                        </a>
                    </div>
                    <div class="breadcrumb-separator">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                    <div class="breadcrumb-item">
                        <a href="manage-event.php?event_id=<?php echo $event_id; ?>" style="color: inherit; text-decoration: none;">
                            <i class="fas fa-edit"></i>
                            <span><?php echo htmlspecialchars($category['event_name']); ?></span>
                        </a>
                    </div>
                    <div class="breadcrumb-separator">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                    <div class="breadcrumb-item">
                        <i class="fas fa-layer-group"></i>
                        <span><?php echo htmlspecialchars($category['category_name']); ?></span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span class="user-name">
                        <i class="fas fa-user-shield"></i>
                        <?php echo htmlspecialchars($current_admin['username']); ?>
                    </span>
                    <a href="logout.php" class="btn btn-sm btn-secondary">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </div>
            </div>
        </header>

        <!-- Content -->
        <div class="admin-content">
            <div class="category-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title"><?php echo htmlspecialchars($category['category_name']); ?></h1>
            <div class="page-subtitle">
                <div class="breadcrumb">
                    <i class="fas fa-home"></i>
                    <a href="events.php" style="color: inherit; text-decoration: none;">Events</a>
                    <i class="fas fa-chevron-right" style="margin: 0 0.5rem;"></i>
                    <a href="manage-event.php?event_id=<?php echo $event_id; ?>" style="color: inherit; text-decoration: none;">
                        <?php echo htmlspecialchars($category['event_name']); ?>
                    </a>
                    <i class="fas fa-chevron-right" style="margin: 0 0.5rem;"></i>
                    <?php echo htmlspecialchars($category['sport_name']); ?>
                </div>
                <span><i class="fas fa-trophy"></i> <?php echo htmlspecialchars($category['sport_name']); ?></span>
                <span><i class="fas fa-calendar"></i> <?php echo htmlspecialchars($category['event_name']); ?></span>
                <span class="status-badge"><?php echo htmlspecialchars($category['category_status']); ?></span>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="tabs">
            <button class="tab-button active" onclick="switchTab('overview')" data-tab="overview">
                <i class="fas fa-cog"></i>
                Overview
            </button>
            <button class="tab-button" onclick="switchTab('fixtures')" data-tab="fixtures">
                <i class="fas fa-calendar-alt"></i>
                Fixtures
            </button>
            <button class="tab-button" onclick="switchTab('standings')" data-tab="standings">
                <i class="fas fa-trophy"></i>
                Standings
            </button>
        </div>

        <!-- Overview Tab -->
        <div id="overview-tab" class="tab-content active">
            <!-- Category Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" style="color: #3b82f6;"><?php echo $category_stats['total_registrations']; ?></div>
                    <div class="stat-label">Total Registrations</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #10b981;"><?php echo $category_stats['confirmed_registrations']; ?></div>
                    <div class="stat-label">Confirmed Teams</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #f59e0b;"><?php echo $category_stats['total_matches']; ?></div>
                    <div class="stat-label">Total Matches</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #8b5cf6;"><?php echo $category_stats['completed_matches']; ?></div>
                    <div class="stat-label">Completed</div>
                </div>
            </div>

            <!-- Sport & Category Details -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-info-circle"></i> Sport & Category Details
                    </h2>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                    <!-- Sport Information -->
                    <div>
                        <h4 style="margin-bottom: 1rem; color: #374151; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-trophy"></i> Sport Information
                        </h4>
                        <div style="background: #f8fafc; padding: 1.5rem; border-radius: 8px;">
                            <div style="margin-bottom: 1rem;">
                                <div style="font-weight: 500; color: #374151;">Sport Name</div>
                                <div style="color: #6b7280;"><?php echo htmlspecialchars($category['sport_name']); ?></div>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <div style="font-weight: 500; color: #374151;">Sport Type</div>
                                <div style="color: #6b7280;"><?php echo htmlspecialchars($category['sport_type_name'] ?? 'General'); ?></div>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <div style="font-weight: 500; color: #374151;">Category</div>
                                <div style="color: #6b7280;"><?php echo ucfirst($category['sport_type_category'] ?? 'team'); ?></div>
                            </div>
                            <div>
                                <div style="font-weight: 500; color: #374151;">Scoring Method</div>
                                <div style="color: #6b7280;"><?php echo ucfirst($category['scoring_method'] ?? 'standard'); ?></div>
                            </div>
                        </div>
                    </div>

                    <!-- Category Information -->
                    <div>
                        <h4 style="margin-bottom: 1rem; color: #374151; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-layer-group"></i> Category Information
                        </h4>
                        <div style="background: #f8fafc; padding: 1.5rem; border-radius: 8px;">
                            <div style="margin-bottom: 1rem;">
                                <div style="font-weight: 500; color: #374151;">Category Name</div>
                                <div style="color: #6b7280;"><?php echo htmlspecialchars($category['category_name']); ?></div>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <div style="font-weight: 500; color: #374151;">Category Type</div>
                                <div style="color: #6b7280;"><?php echo ucfirst($category['category_type']); ?></div>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <div style="font-weight: 500; color: #374151;">Max Participants</div>
                                <div style="color: #6b7280;"><?php echo $category['max_participants'] ?? 'Unlimited'; ?></div>
                            </div>
                            <div>
                                <div style="font-weight: 500; color: #374151;">Status</div>
                                <div style="color: #6b7280;">
                                    <span style="background: <?php echo $category['category_status'] === 'ongoing' ? '#10b981' : ($category['category_status'] === 'completed' ? '#3b82f6' : '#f59e0b'); ?>; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                                        <?php echo ucfirst($category['category_status']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tournament Format Information -->
                <?php
                // Get tournament format information from event_sports table
                $stmt = $conn->prepare("
                    SELECT es.tournament_format_id, es.bracket_type, tf.name as format_name,
                           tf.description as format_description, tf.min_participants, tf.max_participants,
                           tf.advancement_type, tf.code as format_code
                    FROM event_sports es
                    LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
                    WHERE es.id = ?
                ");
                $stmt->execute([$category['event_sport_id']]);
                $tournament_format_info = $stmt->fetch();

                // Also check if there's an active tournament structure
                $stmt = $conn->prepare("
                    SELECT ts.*, tf.name as format_name
                    FROM tournament_structures ts
                    JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
                    WHERE ts.event_sport_id = ?
                    ORDER BY ts.created_at DESC LIMIT 1
                ");
                $stmt->execute([$category['event_sport_id']]);
                $tournament_structure = $stmt->fetch();
                ?>

                <div style="margin-top: 2rem;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                        <h4 style="margin: 0; color: #374151; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-cogs"></i> Tournament Configuration
                        </h4>
                        <button type="button" class="btn btn-primary btn-sm" onclick="openTournamentFormatModal()">
                            <i class="fas fa-edit"></i> Edit Format
                        </button>
                    </div>

                    <?php if ($tournament_format_info && $tournament_format_info['tournament_format_id']): ?>
                        <div style="background: #f0f9ff; padding: 1.5rem; border-radius: 8px; border-left: 4px solid #3b82f6;">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                                <div>
                                    <div style="font-weight: 500; color: #374151;">Tournament Format</div>
                                    <div style="color: #6b7280;"><?php echo htmlspecialchars($tournament_format_info['format_name'] ?? 'Unknown Format'); ?></div>
                                </div>
                                <div>
                                    <div style="font-weight: 500; color: #374151;">Format Code</div>
                                    <div style="color: #6b7280;"><?php echo htmlspecialchars($tournament_format_info['format_code'] ?? $tournament_format_info['bracket_type'] ?? 'N/A'); ?></div>
                                </div>
                                <div>
                                    <div style="font-weight: 500; color: #374151;">Participants Range</div>
                                    <div style="color: #6b7280;">
                                        <?php echo $tournament_format_info['min_participants'] ?? 2; ?> -
                                        <?php echo $tournament_format_info['max_participants'] ? $tournament_format_info['max_participants'] : '∞'; ?>
                                    </div>
                                </div>
                                <div>
                                    <div style="font-weight: 500; color: #374151;">Advancement Type</div>
                                    <div style="color: #6b7280;"><?php echo ucfirst($tournament_format_info['advancement_type'] ?? 'elimination'); ?></div>
                                </div>
                            </div>

                            <?php if ($tournament_format_info['format_description']): ?>
                                <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid #e0f2fe;">
                                    <div style="font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Description</div>
                                    <div style="color: #6b7280; font-size: 0.9rem;"><?php echo htmlspecialchars($tournament_format_info['format_description']); ?></div>
                                </div>
                            <?php endif; ?>

                            <?php if ($tournament_structure): ?>
                                <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid #e0f2fe;">
                                    <div style="font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Tournament Status</div>
                                    <div style="display: flex; gap: 1rem; align-items: center;">
                                        <span style="background:
                                            <?php
                                                switch($tournament_structure['status']) {
                                                    case 'completed': echo '#10b981'; break;
                                                    case 'in_progress': echo '#f59e0b'; break;
                                                    case 'setup': echo '#6b7280'; break;
                                                    default: echo '#3b82f6';
                                                }
                                            ?>; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                                            <?php echo ucfirst($tournament_structure['status']); ?>
                                        </span>
                                        <span style="color: #6b7280; font-size: 0.9rem;">
                                            Round <?php echo $tournament_structure['current_round']; ?> of <?php echo $tournament_structure['total_rounds']; ?>
                                            (<?php echo $tournament_structure['participant_count']; ?> participants)
                                        </span>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div style="background: #fef3c7; padding: 1.5rem; border-radius: 8px; border-left: 4px solid #f59e0b;">
                            <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                                <i class="fas fa-exclamation-triangle" style="color: #d97706;"></i>
                                <div style="font-weight: 500; color: #92400e;">No Tournament Format Configured</div>
                            </div>
                            <div style="color: #92400e; font-size: 0.9rem;">
                                A tournament format needs to be selected for this sport category. Click "Edit Format" to configure the tournament settings.
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Venue & Officials -->
                <div style="margin-top: 2rem;">
                    <h4 style="margin-bottom: 1rem; color: #374151; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-map-marker-alt"></i> Venue & Officials
                    </h4>
                    <div style="background: #f8fafc; padding: 1.5rem; border-radius: 8px;">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                            <div>
                                <div style="font-weight: 500; color: #374151;">Venue</div>
                                <div style="color: #6b7280;"><?php echo htmlspecialchars($category['venue'] ?? 'TBD'); ?></div>
                            </div>
                            <div>
                                <div style="font-weight: 500; color: #374151;">Referee</div>
                                <div style="color: #6b7280;"><?php echo htmlspecialchars($category['referee_name'] ?? 'TBD'); ?></div>
                            </div>
                            <?php if ($category['referee_email']): ?>
                                <div>
                                    <div style="font-weight: 500; color: #374151;">Contact</div>
                                    <div style="color: #6b7280;">
                                        <a href="mailto:<?php echo htmlspecialchars($category['referee_email']); ?>" style="color: #3b82f6; text-decoration: none;">
                                            <?php echo htmlspecialchars($category['referee_email']); ?>
                                        </a>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>



            <!-- Registered Teams -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-users"></i> Registered Teams
                    </h2>
                    <span class="btn btn-secondary btn-sm">
                        <?php echo count($registrations); ?> Teams
                    </span>
                </div>

                <?php if (empty($registrations)): ?>
                    <div style="text-align: center; padding: 3rem; color: #6b7280;">
                        <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                        <p>No teams registered yet</p>
                    </div>
                <?php else: ?>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Department</th>
                                    <th>Team Name</th>
                                    <th>Participants</th>
                                    <th>Status</th>
                                    <th>Registration Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($registrations as $registration): ?>
                                    <tr>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                                <div style="width: 20px; height: 20px; background: <?php echo $registration['color_code'] ?? '#6b7280'; ?>; border-radius: 4px;"></div>
                                                <div>
                                                    <div style="font-weight: 500;"><?php echo htmlspecialchars($registration['department_name']); ?></div>
                                                    <div style="font-size: 0.8rem; color: #6b7280;"><?php echo htmlspecialchars($registration['department_abbr']); ?></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($registration['team_name'] ?: $registration['department_name']); ?></td>
                                        <td><?php echo $registration['participants'] ?: 'Not specified'; ?></td>
                                        <td>
                                            <span class="status-badge" style="background: <?php echo $registration['registration_status'] === 'confirmed' ? '#10b981' : '#f59e0b'; ?>; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                                                <?php echo ucfirst($registration['registration_status']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('M j, Y', strtotime($registration['registration_date'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Fixtures Tab -->
        <div id="fixtures-tab" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-sitemap"></i> Tournament Bracket & Fixtures
                    </h2>
                    <div style="display: flex; gap: 1rem;">
                        <span class="btn btn-secondary btn-sm">
                            <?php echo $category_stats['total_matches']; ?> Total Matches
                        </span>
                        <span class="btn btn-success btn-sm">
                            <?php echo $category_stats['completed_matches']; ?> Completed
                        </span>
                        <span class="btn btn-info btn-sm">
                            <?php echo $category_stats['scheduled_matches']; ?> Scheduled
                        </span>
                        <span class="btn btn-warning btn-sm">
                            <?php echo $category_stats['ongoing_matches']; ?> Ongoing
                        </span>
                    </div>
                </div>

                <?php if (empty($matches) && !$tournament_info): ?>
                    <div style="text-align: center; padding: 3rem; color: #6b7280;">
                        <i class="fas fa-sitemap" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                        <p>No tournament structure found</p>
                        <p style="font-size: 0.9rem;">A tournament needs to be created for this sport category before matches can be generated.</p>
                        <div style="margin-top: 1rem; padding: 1rem; background: #fef3c7; border-radius: 8px; border-left: 4px solid #f59e0b;">
                            <div style="font-weight: 500; color: #92400e; margin-bottom: 0.5rem;">
                                <i class="fas fa-info-circle"></i> Next Steps:
                            </div>
                            <div style="color: #92400e; font-size: 0.9rem;">
                                1. Ensure you have at least 2 confirmed team registrations<br>
                                2. Create a tournament using the tournament management system<br>
                                3. Generate the tournament bracket and matches
                            </div>
                        </div>
                    </div>
                <?php elseif (empty($matches) && $tournament_info): ?>
                    <div style="text-align: center; padding: 3rem; color: #6b7280;">
                        <i class="fas fa-calendar-alt" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                        <p>Tournament structure exists but no matches generated</p>
                        <p style="font-size: 0.9rem;">Tournament: <?php echo htmlspecialchars($tournament_info['name']); ?> (<?php echo htmlspecialchars($tournament_info['format_name']); ?>)</p>
                        <div style="margin-top: 1rem; padding: 1rem; background: #fef3c7; border-radius: 8px; border-left: 4px solid #f59e0b;">
                            <div style="font-weight: 500; color: #92400e; margin-bottom: 0.5rem;">
                                <i class="fas fa-exclamation-triangle"></i> Issue Detected:
                            </div>
                            <div style="color: #92400e; font-size: 0.9rem;">
                                Tournament structure exists but matches haven't been generated yet.<br>
                                Status: <?php echo ucfirst($tournament_info['status']); ?> |
                                Participants: <?php echo $tournament_info['participant_count']; ?> |
                                Rounds: <?php echo $tournament_info['current_round']; ?>/<?php echo $tournament_info['total_rounds']; ?>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Tournament Progress -->
                    <?php if (isset($tournament_info)): ?>
                        <div style="background: #f0f9ff; padding: 1rem; border-radius: 8px; margin-bottom: 2rem; border-left: 4px solid #3b82f6;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <h4 style="margin: 0; color: #1e40af;">Tournament Progress</h4>
                                    <p style="margin: 0.5rem 0 0 0; color: #6b7280;">
                                        Round <?php echo $tournament_info['current_round']; ?> of <?php echo $tournament_info['total_rounds']; ?>
                                        (<?php echo round(($tournament_info['current_round'] / $tournament_info['total_rounds']) * 100, 1); ?>% complete)
                                    </p>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-size: 0.9rem; color: #6b7280;">Format</div>
                                    <div style="font-weight: 500; color: #1e40af;"><?php echo htmlspecialchars($tournament_info['format_name']); ?></div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Matches by Round -->
                    <?php
                    // Group matches by round
                    $matches_by_round = [];
                    foreach ($matches as $match) {
                        $round = $match['round_number'];
                        if (!isset($matches_by_round[$round])) {
                            $matches_by_round[$round] = [];
                        }
                        $matches_by_round[$round][] = $match;
                    }
                    ksort($matches_by_round);
                    ?>

                    <?php foreach ($matches_by_round as $round_number => $round_matches): ?>
                        <div style="margin-bottom: 2rem;">
                            <h4 style="margin-bottom: 1rem; color: #374151; display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-layer-group"></i>
                                Round <?php echo $round_number; ?>
                                <span style="background: #e5e7eb; color: #6b7280; padding: 0.25rem 0.5rem; border-radius: 12px; font-size: 0.8rem; font-weight: normal;">
                                    <?php echo count($round_matches); ?> matches
                                </span>
                            </h4>

                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 1rem;">
                                <?php foreach ($round_matches as $match): ?>
                                    <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; transition: all 0.3s ease; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                        <!-- Match Header -->
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                                            <div style="font-weight: 500; color: #374151;">
                                                Match <?php echo $match['match_number']; ?>
                                            </div>
                                            <div>
                                                <span style="background:
                                                    <?php
                                                        switch($match['status']) {
                                                            case 'completed': echo '#10b981'; break;
                                                            case 'ongoing': echo '#f59e0b'; break;
                                                            case 'scheduled': echo '#3b82f6'; break;
                                                            default: echo '#6b7280';
                                                        }
                                                    ?>; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                                                    <?php echo ucfirst($match['status']); ?>
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Teams -->
                                        <div style="margin-bottom: 1rem;">
                                            <!-- Team 1 -->
                                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.75rem; background: #f9fafb; border-radius: 6px; margin-bottom: 0.5rem; <?php echo ($match['status'] === 'completed' && $match['winner_id'] == $match['team1_id']) ? 'border-left: 4px solid #10b981;' : ''; ?>">
                                                <div style="display: flex; align-items: center; gap: 0.5rem;">
                                                    <div style="width: 16px; height: 16px; background: <?php echo $match['team1_color'] ?? '#6b7280'; ?>; border-radius: 4px;"></div>
                                                    <span style="font-weight: 500;"><?php echo htmlspecialchars($match['team1_name'] ?: 'TBD'); ?></span>
                                                    <?php if ($match['status'] === 'completed' && $match['winner_id'] == $match['team1_id']): ?>
                                                        <i class="fas fa-crown" style="color: #f59e0b; font-size: 0.8rem;"></i>
                                                    <?php endif; ?>
                                                </div>
                                                <?php if ($match['team1_score'] !== null): ?>
                                                    <div style="font-weight: 700; font-size: 1.2rem; color: #374151;">
                                                        <?php echo $match['team1_score']; ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <!-- VS Divider -->
                                            <div style="text-align: center; color: #6b7280; font-size: 0.9rem; margin: 0.5rem 0;">
                                                VS
                                            </div>

                                            <!-- Team 2 -->
                                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.75rem; background: #f9fafb; border-radius: 6px; <?php echo ($match['status'] === 'completed' && $match['winner_id'] == $match['team2_id']) ? 'border-left: 4px solid #10b981;' : ''; ?>">
                                                <div style="display: flex; align-items: center; gap: 0.5rem;">
                                                    <div style="width: 16px; height: 16px; background: <?php echo $match['team2_color'] ?? '#6b7280'; ?>; border-radius: 4px;"></div>
                                                    <span style="font-weight: 500;"><?php echo htmlspecialchars($match['team2_name'] ?: 'TBD'); ?></span>
                                                    <?php if ($match['status'] === 'completed' && $match['winner_id'] == $match['team2_id']): ?>
                                                        <i class="fas fa-crown" style="color: #f59e0b; font-size: 0.8rem;"></i>
                                                    <?php endif; ?>
                                                </div>
                                                <?php if ($match['team2_score'] !== null): ?>
                                                    <div style="font-weight: 700; font-size: 1.2rem; color: #374151;">
                                                        <?php echo $match['team2_score']; ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <!-- Match Info -->
                                        <?php if ($match['scheduled_time']): ?>
                                            <div style="text-align: center; color: #6b7280; font-size: 0.9rem;">
                                                <i class="fas fa-clock"></i>
                                                <?php echo date('M j, Y g:i A', strtotime($match['scheduled_time'])); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Standings Tab -->
        <div id="standings-tab" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-trophy"></i> Current Standings
                    </h2>
                    <span class="btn btn-secondary btn-sm">
                        <?php echo count($standings); ?> Teams
                    </span>
                </div>

                <?php if (empty($standings)): ?>
                    <div style="text-align: center; padding: 3rem; color: #6b7280;">
                        <i class="fas fa-trophy" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                        <p>No standings available yet</p>
                        <p style="font-size: 0.9rem;">Standings will appear here once matches are completed.</p>
                    </div>
                <?php else: ?>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Team</th>
                                    <th>Matches</th>
                                    <th>Wins</th>
                                    <th>Losses</th>
                                    <th>Points</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($standings as $standing): ?>
                                    <tr>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                                <span style="font-weight: 700; font-size: 1.2rem;">
                                                    <?php echo $standing['rank']; ?>
                                                </span>
                                                <?php if ($standing['rank'] <= 3): ?>
                                                    <i class="fas fa-medal" style="color:
                                                        <?php
                                                            switch($standing['rank']) {
                                                                case 1: echo '#ffd700'; break;
                                                                case 2: echo '#c0c0c0'; break;
                                                                case 3: echo '#cd7f32'; break;
                                                            }
                                                        ?>;"></i>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                                <div style="width: 20px; height: 20px; background: <?php echo $standing['color_code'] ?? '#6b7280'; ?>; border-radius: 4px;"></div>
                                                <div>
                                                    <div style="font-weight: 500;"><?php echo htmlspecialchars($standing['team_name']); ?></div>
                                                    <div style="font-size: 0.8rem; color: #6b7280;"><?php echo htmlspecialchars($standing['department_abbr']); ?></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo $standing['matches_played']; ?></td>
                                        <td style="color: #10b981; font-weight: 500;"><?php echo $standing['wins']; ?></td>
                                        <td style="color: #ef4444; font-weight: 500;"><?php echo $standing['losses']; ?></td>
                                        <td>
                                            <span style="background: #3b82f6; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-weight: 500;">
                                                <?php echo $standing['points']; ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
            </div>
        </div>
    </div>



    <!-- Tournament Format Edit Modal -->
    <div id="tournamentFormatModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Edit Tournament Format</h2>
                <button class="modal-close" onclick="closeTournamentFormatModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="tournamentFormatForm">
                <div class="form-group">
                    <label class="form-label" for="tournamentFormatSelect">Tournament Format</label>
                    <select id="tournamentFormatSelect" class="form-select" required>
                        <option value="">Loading formats...</option>
                    </select>
                    <div id="formatDescription" class="form-help" style="margin-top: 0.5rem; font-size: 0.9rem; color: #6b7280;"></div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="seedingMethod">Seeding Method</label>
                    <select id="seedingMethod" class="form-select">
                        <option value="random">Random Seeding</option>
                        <option value="ranking">Ranking-based Seeding</option>
                        <option value="manual">Manual Seeding</option>
                        <option value="hybrid">Hybrid Seeding</option>
                    </select>
                    <div class="form-help">How participants will be seeded in the tournament bracket</div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="maxTeams">Maximum Teams</label>
                    <input type="number" id="maxTeams" class="form-input" min="2" max="64"
                           value="<?php echo $category['max_participants'] ?? 8; ?>">
                    <div class="form-help">Maximum number of teams that can participate</div>
                </div>

                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeTournamentFormatModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>

    <?php include 'includes/admin-scripts.php'; ?>

    <script>
        // Tab switching functionality
        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName + '-tab').classList.add('active');

            // Add active class to clicked tab button
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        }

        // Tournament Format Modal Functions
        async function openTournamentFormatModal() {
            // Load current tournament format data
            await loadCurrentTournamentFormat();

            // Load available tournament formats
            await loadAvailableTournamentFormats();

            // Show modal
            document.getElementById('tournamentFormatModal').classList.add('active');
        }

        function closeTournamentFormatModal() {
            document.getElementById('tournamentFormatModal').classList.remove('active');
        }

        async function loadCurrentTournamentFormat() {
            try {
                const response = await fetch('ajax/category-management.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=get_current_format&event_sport_id=<?php echo $category['event_sport_id']; ?>'
                });

                const result = await response.json();

                if (result.success && result.format) {
                    // Pre-select current format when formats are loaded
                    window.currentFormatId = result.format.tournament_format_id;
                    document.getElementById('maxTeams').value = result.format.max_teams || 8;
                }
            } catch (error) {
                console.error('Error loading current format:', error);
            }
        }

        async function loadAvailableTournamentFormats() {
            try {
                const response = await fetch('ajax/tournament-management.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=get_sport_formats&sport_id=<?php echo $sport_id; ?>'
                });

                const result = await response.json();

                if (result.success) {
                    const formatSelect = document.getElementById('tournamentFormatSelect');
                    formatSelect.innerHTML = '<option value="">Select tournament format...</option>';

                    result.formats.forEach(format => {
                        const option = document.createElement('option');
                        option.value = format.id;
                        option.textContent = format.name;
                        option.dataset.description = format.description || '';
                        option.dataset.minParticipants = format.min_participants || 2;
                        option.dataset.maxParticipants = format.max_participants || 'unlimited';
                        option.dataset.advancementType = format.advancement_type || 'elimination';

                        // Pre-select current format
                        if (window.currentFormatId && format.id == window.currentFormatId) {
                            option.selected = true;
                        }

                        formatSelect.appendChild(option);
                    });

                    // Trigger change event to show description
                    formatSelect.dispatchEvent(new Event('change'));

                    // Add change event listener for format description
                    formatSelect.addEventListener('change', function() {
                        const selectedOption = this.options[this.selectedIndex];
                        const descriptionDiv = document.getElementById('formatDescription');

                        if (selectedOption.value) {
                            const minPart = selectedOption.dataset.minParticipants;
                            const maxPart = selectedOption.dataset.maxParticipants;
                            const description = selectedOption.dataset.description;
                            const advancementType = selectedOption.dataset.advancementType;

                            descriptionDiv.innerHTML = `
                                <div style="margin-bottom: 0.5rem;"><strong>Description:</strong> ${description}</div>
                                <div style="font-size: 0.8rem; color: #9ca3af;">
                                    <strong>Participants:</strong> ${minPart} - ${maxPart === 'unlimited' ? '∞' : maxPart} |
                                    <strong>Type:</strong> ${advancementType}
                                </div>
                            `;
                        } else {
                            descriptionDiv.innerHTML = '';
                        }
                    });
                } else {
                    showNotification('Failed to load tournament formats', 'error');
                }
            } catch (error) {
                console.error('Error loading tournament formats:', error);
                showNotification('Error loading tournament formats', 'error');
            }
        }

        // Handle tournament format form submission
        document.getElementById('tournamentFormatForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData();
            formData.append('action', 'update_tournament_format');
            formData.append('event_sport_id', <?php echo $category['event_sport_id']; ?>);
            formData.append('tournament_format_id', document.getElementById('tournamentFormatSelect').value);
            formData.append('seeding_method', document.getElementById('seedingMethod').value);
            formData.append('max_teams', document.getElementById('maxTeams').value);

            try {
                const response = await fetch('ajax/category-management.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('Tournament format updated successfully!', 'success');
                    closeTournamentFormatModal();

                    // Reload page to show updated format
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showNotification(result.message || 'Failed to update tournament format', 'error');
                }
            } catch (error) {
                console.error('Error updating tournament format:', error);
                showNotification('An error occurred while updating tournament format', 'error');
            }
        });

        // Close modal when clicking outside
        document.getElementById('tournamentFormatModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeTournamentFormatModal();
            }
        });





        // Show notification function
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 1000;
                animation: slideIn 0.3s ease;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
            `;
            notification.textContent = message;

            // Add to page
            document.body.appendChild(notification);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
