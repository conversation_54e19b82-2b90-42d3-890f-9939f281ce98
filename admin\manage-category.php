<?php
/**
 * Sport Category Management Page for SC_IMS Admin Panel
 * Dedicated interface for managing sport category settings and administration
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get URL parameters
$event_id = $_GET['event_id'] ?? null;
$sport_id = $_GET['sport_id'] ?? null;
$category_id = $_GET['category_id'] ?? null;

// Validate required parameters
if (!$event_id || !$sport_id || !$category_id) {
    header('Location: events.php');
    exit;
}

// Get category information with related data
try {
    $sql = "SELECT
                sc.id as category_id,
                sc.category_name,
                sc.category_type,
                sc.referee_name,
                sc.referee_email,
                sc.venue,
                sc.max_participants,
                sc.status as category_status,
                es.id as event_sport_id,
                es.status as event_sport_status,
                s.id as sport_id,
                s.name as sport_name,
                s.type as sport_type,
                s.scoring_method,
                s.bracket_format,
                st.name as sport_type_name,
                st.category as sport_type_category,
                st.icon_class,
                e.id as event_id,
                e.name as event_name,
                e.status as event_status
            FROM sport_categories sc
            JOIN event_sports es ON sc.event_sport_id = es.id
            JOIN sports s ON es.sport_id = s.id
            LEFT JOIN sport_types st ON s.sport_type_id = st.id
            JOIN events e ON es.event_id = e.id
            WHERE sc.id = ? AND es.event_id = ? AND s.id = ?";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$category_id, $event_id, $sport_id]);
    $category = $stmt->fetch();

    if (!$category) {
        header('Location: events.php');
        exit;
    }
} catch (Exception $e) {
    error_log("Error fetching category data: " . $e->getMessage());
    header('Location: events.php');
    exit;
}

// Get registrations for this category
$registrations = [];
try {
    $sql = "SELECT
                r.id as registration_id,
                r.department_id,
                d.name as department_name,
                d.abbreviation as department_abbr,
                d.color_code,
                r.team_name,
                r.participants,
                r.status as registration_status,
                r.registration_date
            FROM registrations r
            JOIN departments d ON r.department_id = d.id
            WHERE r.event_sport_id = ?
            ORDER BY d.name";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$category['event_sport_id']]);
    $registrations = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error fetching registrations: " . $e->getMessage());
}

// Get matches for this category
$matches = [];
try {
    $sql = "SELECT
                m.*,
                r1.team_name as team1_name,
                r1.department_id as team1_dept_id,
                d1.name as team1_dept_name,
                d1.abbreviation as team1_abbr,
                d1.color_code as team1_color,
                r2.team_name as team2_name,
                r2.department_id as team2_dept_id,
                d2.name as team2_dept_name,
                d2.abbreviation as team2_abbr,
                d2.color_code as team2_color
            FROM matches m
            LEFT JOIN registrations r1 ON m.team1_id = r1.id
            LEFT JOIN departments d1 ON r1.department_id = d1.id
            LEFT JOIN registrations r2 ON m.team2_id = r2.id
            LEFT JOIN departments d2 ON r2.department_id = d2.id
            WHERE m.event_sport_id = ?
            ORDER BY m.scheduled_time ASC, m.round_number ASC, m.match_number ASC";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$category['event_sport_id']]);
    $matches = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error fetching matches: " . $e->getMessage());
}

// Calculate category statistics
$category_stats = [
    'total_registrations' => count($registrations),
    'confirmed_registrations' => count(array_filter($registrations, fn($r) => $r['registration_status'] === 'confirmed')),
    'total_matches' => count($matches),
    'completed_matches' => count(array_filter($matches, fn($m) => $m['status'] === 'completed')),
    'scheduled_matches' => count(array_filter($matches, fn($m) => $m['status'] === 'scheduled')),
    'ongoing_matches' => count(array_filter($matches, fn($m) => $m['status'] === 'ongoing'))
];

// Calculate standings from match results
$standings = [];
$team_stats = [];

// Initialize team stats from registrations
foreach ($registrations as $registration) {
    if ($registration['registration_status'] === 'confirmed') {
        $team_stats[$registration['registration_id']] = [
            'registration_id' => $registration['registration_id'],
            'team_name' => $registration['team_name'] ?: $registration['department_name'],
            'department_name' => $registration['department_name'],
            'department_abbr' => $registration['department_abbr'],
            'color_code' => $registration['color_code'],
            'wins' => 0,
            'losses' => 0,
            'draws' => 0,
            'points' => 0,
            'matches_played' => 0
        ];
    }
}

// Calculate stats from completed matches
foreach ($matches as $match) {
    if ($match['status'] === 'completed' && $match['winner_id']) {
        // Update winner stats
        if (isset($team_stats[$match['team1_id']])) {
            $team_stats[$match['team1_id']]['matches_played']++;
            if ($match['winner_id'] == $match['team1_id']) {
                $team_stats[$match['team1_id']]['wins']++;
                $team_stats[$match['team1_id']]['points'] += 3;
            } else {
                $team_stats[$match['team1_id']]['losses']++;
            }
        }

        if (isset($team_stats[$match['team2_id']])) {
            $team_stats[$match['team2_id']]['matches_played']++;
            if ($match['winner_id'] == $match['team2_id']) {
                $team_stats[$match['team2_id']]['wins']++;
                $team_stats[$match['team2_id']]['points'] += 3;
            } else {
                $team_stats[$match['team2_id']]['losses']++;
            }
        }
    }
}

// Sort standings by points, then wins
$standings = array_values($team_stats);
usort($standings, function($a, $b) {
    if ($a['points'] != $b['points']) {
        return $b['points'] <=> $a['points'];
    }
    if ($a['wins'] != $b['wins']) {
        return $b['wins'] <=> $a['wins'];
    }
    return $a['losses'] <=> $b['losses'];
});

// Add rank to standings
foreach ($standings as $index => &$standing) {
    $standing['rank'] = $index + 1;
}
?>

// Initialize Tournament Manager
$tournamentManager = new TournamentManager($conn);

// Get existing tournament for this category
$existing_tournament = null;
try {
    $sql = "SELECT ts.*, tf.name as format_name, tf.description as format_description
            FROM tournament_structures ts
            JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
            WHERE ts.event_sport_id = ?
            ORDER BY ts.created_at DESC
            LIMIT 1";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$category['event_sport_id']]);
    $existing_tournament = $stmt->fetch();
} catch (Exception $e) {
    error_log("Error fetching tournament data: " . $e->getMessage());
}

// Get registrations for this category
$registrations = [];
try {
    $sql = "SELECT
                r.id as registration_id,
                r.department_id,
                d.name as department_name,
                d.abbreviation as department_abbr,
                d.color_code,
                r.participants,
                r.status as registration_status,
                r.created_at
            FROM registrations r
            JOIN departments d ON r.department_id = d.id
            WHERE r.event_sport_id = ? AND r.status = 'confirmed'
            ORDER BY d.name";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$category['event_sport_id']]);
    $registrations = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error fetching registrations: " . $e->getMessage());
}

// Get available tournament formats for this sport type
$available_formats = [];
try {
    $sport_type_category = $category['sport_type_category'] ?? 'team';
    $available_formats = $tournamentManager->getAvailableFormats($sport_type_category);
} catch (Exception $e) {
    error_log("Error fetching tournament formats: " . $e->getMessage());
}

// Get tournament statistics
$tournament_stats = [
    'total_registrations' => count($registrations),
    'confirmed_registrations' => count(array_filter($registrations, fn($r) => $r['registration_status'] === 'confirmed')),
    'tournament_exists' => !empty($existing_tournament),
    'tournament_status' => $existing_tournament['status'] ?? 'not_created',
    'current_round' => $existing_tournament['current_round'] ?? 0,
    'total_rounds' => $existing_tournament['total_rounds'] ?? 0
];

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($category['category_name']); ?> - Category Management | SC_IMS Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Header */
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            opacity: 0.9;
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .breadcrumb {
            background: rgba(255, 255, 255, 0.1);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .status-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            text-transform: uppercase;
            font-weight: 500;
        }

        /* Tabs */
        .tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .tab-button {
            flex: 1;
            padding: 1rem 2rem;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .tab-button:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .tab-button.active {
            background: #3b82f6;
            color: white;
        }

        /* Tab Content */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Cards */
        .card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Form Styles */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        /* Button Styles */
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        /* Statistics Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            text-align: center;
            padding: 1.5rem;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #6b7280;
            font-size: 0.9rem;
        }

        /* Table Styles */
        .table-container {
            overflow-x: auto;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .table th,
        .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .tabs {
                flex-direction: column;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .page-title {
                font-size: 1.5rem;
            }

            .page-subtitle {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title"><?php echo htmlspecialchars($category['category_name']); ?></h1>
            <div class="page-subtitle">
                <div class="breadcrumb">
                    <i class="fas fa-home"></i>
                    <a href="events.php" style="color: inherit; text-decoration: none;">Events</a>
                    <i class="fas fa-chevron-right" style="margin: 0 0.5rem;"></i>
                    <a href="manage-event.php?event_id=<?php echo $event_id; ?>" style="color: inherit; text-decoration: none;">
                        <?php echo htmlspecialchars($category['event_name']); ?>
                    </a>
                    <i class="fas fa-chevron-right" style="margin: 0 0.5rem;"></i>
                    <?php echo htmlspecialchars($category['sport_name']); ?>
                </div>
                <span><i class="fas fa-trophy"></i> <?php echo htmlspecialchars($category['sport_name']); ?></span>
                <span><i class="fas fa-calendar"></i> <?php echo htmlspecialchars($category['event_name']); ?></span>
                <span class="status-badge"><?php echo htmlspecialchars($category['category_status']); ?></span>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="tabs">
            <button class="tab-button active" onclick="switchTab('overview')" data-tab="overview">
                <i class="fas fa-cog"></i>
                Overview
            </button>
            <button class="tab-button" onclick="switchTab('fixtures')" data-tab="fixtures">
                <i class="fas fa-calendar-alt"></i>
                Fixtures
            </button>
            <button class="tab-button" onclick="switchTab('standings')" data-tab="standings">
                <i class="fas fa-trophy"></i>
                Standings
            </button>
        </div>

        <!-- Overview Tab -->
        <div id="overview-tab" class="tab-content active">
            <!-- Category Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" style="color: #3b82f6;"><?php echo $category_stats['total_registrations']; ?></div>
                    <div class="stat-label">Total Registrations</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #10b981;"><?php echo $category_stats['confirmed_registrations']; ?></div>
                    <div class="stat-label">Confirmed Teams</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #f59e0b;"><?php echo $category_stats['total_matches']; ?></div>
                    <div class="stat-label">Total Matches</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #8b5cf6;"><?php echo $category_stats['completed_matches']; ?></div>
                    <div class="stat-label">Completed</div>
                </div>
            </div>

            <!-- Category Settings -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-cog"></i> Category Settings
                    </h2>
                    <button type="button" class="btn btn-primary" onclick="saveCategorySettings()">
                        <i class="fas fa-save"></i> Save Changes
                    </button>
                </div>

                <form id="categorySettingsForm" class="form-grid">
                    <div>
                        <div class="form-group">
                            <label class="form-label" for="categoryName">Category Name</label>
                            <input type="text" id="categoryName" class="form-input"
                                   value="<?php echo htmlspecialchars($category['category_name']); ?>" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="categoryType">Category Type</label>
                            <select id="categoryType" class="form-select" required>
                                <option value="men" <?php echo $category['category_type'] === 'men' ? 'selected' : ''; ?>>Men</option>
                                <option value="women" <?php echo $category['category_type'] === 'women' ? 'selected' : ''; ?>>Women</option>
                                <option value="mixed" <?php echo $category['category_type'] === 'mixed' ? 'selected' : ''; ?>>Mixed</option>
                                <option value="open" <?php echo $category['category_type'] === 'open' ? 'selected' : ''; ?>>Open</option>
                                <option value="youth" <?php echo $category['category_type'] === 'youth' ? 'selected' : ''; ?>>Youth</option>
                                <option value="senior" <?php echo $category['category_type'] === 'senior' ? 'selected' : ''; ?>>Senior</option>
                                <option value="other" <?php echo $category['category_type'] === 'other' ? 'selected' : ''; ?>>Other</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="maxParticipants">Maximum Participants</label>
                            <input type="number" id="maxParticipants" class="form-input"
                                   value="<?php echo $category['max_participants']; ?>" min="2" max="64">
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="categoryStatus">Category Status</label>
                            <select id="categoryStatus" class="form-select" required>
                                <option value="registration" <?php echo $category['category_status'] === 'registration' ? 'selected' : ''; ?>>Registration Open</option>
                                <option value="ongoing" <?php echo $category['category_status'] === 'ongoing' ? 'selected' : ''; ?>>Ongoing</option>
                                <option value="completed" <?php echo $category['category_status'] === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                <option value="cancelled" <?php echo $category['category_status'] === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                            </select>
                        </div>
                    </div>

                    <div>
                        <div class="form-group">
                            <label class="form-label" for="refereeName">Referee Name</label>
                            <input type="text" id="refereeName" class="form-input"
                                   value="<?php echo htmlspecialchars($category['referee_name'] ?? ''); ?>"
                                   placeholder="Enter referee name">
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="refereeEmail">Referee Email</label>
                            <input type="email" id="refereeEmail" class="form-input"
                                   value="<?php echo htmlspecialchars($category['referee_email'] ?? ''); ?>"
                                   placeholder="<EMAIL>">
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="venue">Venue</label>
                            <input type="text" id="venue" class="form-input"
                                   value="<?php echo htmlspecialchars($category['venue'] ?? ''); ?>"
                                   placeholder="Enter venue location">
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="registrationDeadline">Registration Deadline</label>
                            <input type="datetime-local" id="registrationDeadline" class="form-input"
                                   value="<?php echo $category['registration_deadline'] ? date('Y-m-d\TH:i', strtotime($category['registration_deadline'])) : ''; ?>">
                        </div>
                    </div>
                </form>
            </div>

            <!-- Registered Teams -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-users"></i> Registered Teams
                    </h2>
                    <span class="btn btn-secondary btn-sm">
                        <?php echo count($registrations); ?> Teams
                    </span>
                </div>

                <?php if (empty($registrations)): ?>
                    <div style="text-align: center; padding: 3rem; color: #6b7280;">
                        <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                        <p>No teams registered yet</p>
                    </div>
                <?php else: ?>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Department</th>
                                    <th>Team Name</th>
                                    <th>Participants</th>
                                    <th>Status</th>
                                    <th>Registration Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($registrations as $registration): ?>
                                    <tr>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                                <div style="width: 20px; height: 20px; background: <?php echo $registration['color_code'] ?? '#6b7280'; ?>; border-radius: 4px;"></div>
                                                <div>
                                                    <div style="font-weight: 500;"><?php echo htmlspecialchars($registration['department_name']); ?></div>
                                                    <div style="font-size: 0.8rem; color: #6b7280;"><?php echo htmlspecialchars($registration['department_abbr']); ?></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($registration['team_name'] ?: $registration['department_name']); ?></td>
                                        <td><?php echo $registration['participants'] ?: 'Not specified'; ?></td>
                                        <td>
                                            <span class="status-badge" style="background: <?php echo $registration['registration_status'] === 'confirmed' ? '#10b981' : '#f59e0b'; ?>; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                                                <?php echo ucfirst($registration['registration_status']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('M j, Y', strtotime($registration['registration_date'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Fixtures Tab -->
        <div id="fixtures-tab" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-calendar-alt"></i> Match Fixtures
                    </h2>
                    <div style="display: flex; gap: 1rem;">
                        <span class="btn btn-secondary btn-sm">
                            <?php echo $category_stats['total_matches']; ?> Total Matches
                        </span>
                        <span class="btn btn-success btn-sm">
                            <?php echo $category_stats['completed_matches']; ?> Completed
                        </span>
                    </div>
                </div>

                <?php if (empty($matches)): ?>
                    <div style="text-align: center; padding: 3rem; color: #6b7280;">
                        <i class="fas fa-calendar-alt" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                        <p>No matches scheduled yet</p>
                        <p style="font-size: 0.9rem;">Matches will appear here once the tournament bracket is generated.</p>
                    </div>
                <?php else: ?>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Match</th>
                                    <th>Teams</th>
                                    <th>Scheduled Time</th>
                                    <th>Status</th>
                                    <th>Result</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($matches as $match): ?>
                                    <tr>
                                        <td>
                                            <div style="font-weight: 500;">Round <?php echo $match['round_number']; ?></div>
                                            <div style="font-size: 0.8rem; color: #6b7280;">Match <?php echo $match['match_number']; ?></div>
                                        </td>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                                <div style="display: flex; align-items: center; gap: 0.25rem;">
                                                    <div style="width: 12px; height: 12px; background: <?php echo $match['team1_color'] ?? '#6b7280'; ?>; border-radius: 2px;"></div>
                                                    <span><?php echo htmlspecialchars($match['team1_name'] ?: 'TBD'); ?></span>
                                                </div>
                                                <span style="color: #6b7280;">vs</span>
                                                <div style="display: flex; align-items: center; gap: 0.25rem;">
                                                    <div style="width: 12px; height: 12px; background: <?php echo $match['team2_color'] ?? '#6b7280'; ?>; border-radius: 2px;"></div>
                                                    <span><?php echo htmlspecialchars($match['team2_name'] ?: 'TBD'); ?></span>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($match['scheduled_time']): ?>
                                                <?php echo date('M j, Y g:i A', strtotime($match['scheduled_time'])); ?>
                                            <?php else: ?>
                                                <span style="color: #6b7280;">Not scheduled</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="status-badge" style="background:
                                                <?php
                                                    switch($match['status']) {
                                                        case 'completed': echo '#10b981'; break;
                                                        case 'ongoing': echo '#f59e0b'; break;
                                                        case 'scheduled': echo '#3b82f6'; break;
                                                        default: echo '#6b7280';
                                                    }
                                                ?>; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                                                <?php echo ucfirst($match['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($match['status'] === 'completed' && $match['winner_id']): ?>
                                                <div style="font-weight: 500; color: #10b981;">
                                                    <?php
                                                        if ($match['winner_id'] == $match['team1_id']) {
                                                            echo htmlspecialchars($match['team1_name']);
                                                        } else {
                                                            echo htmlspecialchars($match['team2_name']);
                                                        }
                                                    ?>
                                                </div>
                                                <?php if ($match['team1_score'] !== null && $match['team2_score'] !== null): ?>
                                                    <div style="font-size: 0.8rem; color: #6b7280;">
                                                        <?php echo $match['team1_score']; ?> - <?php echo $match['team2_score']; ?>
                                                    </div>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span style="color: #6b7280;">-</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Standings Tab -->
        <div id="standings-tab" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-trophy"></i> Current Standings
                    </h2>
                    <span class="btn btn-secondary btn-sm">
                        <?php echo count($standings); ?> Teams
                    </span>
                </div>

                <?php if (empty($standings)): ?>
                    <div style="text-align: center; padding: 3rem; color: #6b7280;">
                        <i class="fas fa-trophy" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                        <p>No standings available yet</p>
                        <p style="font-size: 0.9rem;">Standings will appear here once matches are completed.</p>
                    </div>
                <?php else: ?>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Team</th>
                                    <th>Matches</th>
                                    <th>Wins</th>
                                    <th>Losses</th>
                                    <th>Points</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($standings as $standing): ?>
                                    <tr>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                                <span style="font-weight: 700; font-size: 1.2rem;">
                                                    <?php echo $standing['rank']; ?>
                                                </span>
                                                <?php if ($standing['rank'] <= 3): ?>
                                                    <i class="fas fa-medal" style="color:
                                                        <?php
                                                            switch($standing['rank']) {
                                                                case 1: echo '#ffd700'; break;
                                                                case 2: echo '#c0c0c0'; break;
                                                                case 3: echo '#cd7f32'; break;
                                                            }
                                                        ?>;"></i>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                                <div style="width: 20px; height: 20px; background: <?php echo $standing['color_code'] ?? '#6b7280'; ?>; border-radius: 4px;"></div>
                                                <div>
                                                    <div style="font-weight: 500;"><?php echo htmlspecialchars($standing['team_name']); ?></div>
                                                    <div style="font-size: 0.8rem; color: #6b7280;"><?php echo htmlspecialchars($standing['department_abbr']); ?></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo $standing['matches_played']; ?></td>
                                        <td style="color: #10b981; font-weight: 500;"><?php echo $standing['wins']; ?></td>
                                        <td style="color: #ef4444; font-weight: 500;"><?php echo $standing['losses']; ?></td>
                                        <td>
                                            <span style="background: #3b82f6; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-weight: 500;">
                                                <?php echo $standing['points']; ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        // Tab switching functionality
        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName + '-tab').classList.add('active');

            // Add active class to clicked tab button
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        }

        // Save category settings
        async function saveCategorySettings() {
            const formData = new FormData();
            formData.append('action', 'update_category');
            formData.append('category_id', <?php echo $category_id; ?>);
            formData.append('category_name', document.getElementById('categoryName').value);
            formData.append('category_type', document.getElementById('categoryType').value);
            formData.append('max_participants', document.getElementById('maxParticipants').value);
            formData.append('category_status', document.getElementById('categoryStatus').value);
            formData.append('referee_name', document.getElementById('refereeName').value);
            formData.append('referee_email', document.getElementById('refereeEmail').value);
            formData.append('venue', document.getElementById('venue').value);
            formData.append('registration_deadline', document.getElementById('registrationDeadline').value);

            try {
                const response = await fetch('ajax/category_actions.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // Show success message
                    showNotification('Category settings updated successfully!', 'success');

                    // Optionally reload the page to reflect changes
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showNotification(result.message || 'Failed to update category settings', 'error');
                }
            } catch (error) {
                console.error('Error updating category:', error);
                showNotification('An error occurred while updating category settings', 'error');
            }
        }

        // Show notification function
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 1000;
                animation: slideIn 0.3s ease;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
            `;
            notification.textContent = message;

            // Add to page
            document.body.appendChild(notification);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>


                            <div class="detail-icon">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="detail-content">
                                <h4>Tournament Status</h4>
                                <?php if ($tournament_stats['tournament_exists']): ?>
                                    <p><strong>Status:</strong>
                                        <span class="status-badge status-active">Active</span>
                                    </p>
                                    <p><strong>Format:</strong> <?php echo htmlspecialchars($existing_tournament['format_name'] ?? 'Unknown'); ?></p>
                                    <p><strong>Round:</strong> <?php echo $tournament_stats['current_round']; ?> of <?php echo $tournament_stats['total_rounds']; ?></p>
                                    <p><strong>Progress:</strong>
                                        <?php
                                        $progress = $tournament_stats['total_rounds'] > 0 ?
                                            round(($tournament_stats['current_round'] / $tournament_stats['total_rounds']) * 100, 1) : 0;
                                        echo $progress; ?>%
                                    </p>
                                <?php else: ?>
                                    <p><strong>Status:</strong>
                                        <span class="status-badge status-pending">Not Created</span>
                                    </p>
                                    <p>Configure tournament settings above to create the tournament structure.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Public Access Section -->
                    <div class="public-access-section">
                        <h4><i class="fas fa-globe"></i> Public Access & Sharing</h4>
                        <div class="access-grid">
                            <div class="access-item">
                                <label>Public Tournament URL:</label>
                                <div class="url-input-group">
                                    <input type="text" id="publicUrl" class="form-control" readonly
                                           value="<?php echo $_SERVER['HTTP_HOST']; ?>/SC_IMS/public/tournament.php?id=<?php echo $category_id; ?>">
                                    <button class="btn-secondary" onclick="copyToClipboard('publicUrl')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="access-item">
                                <label>Embed Code:</label>
                                <div class="url-input-group">
                                    <textarea id="embedCode" class="form-control" readonly rows="2">&lt;iframe src="<?php echo $_SERVER['HTTP_HOST']; ?>/SC_IMS/public/tournament.php?id=<?php echo $category_id; ?>&embed=1" width="100%" height="600"&gt;&lt;/iframe&gt;</textarea>
                                    <button class="btn-secondary" onclick="copyToClipboard('embedCode')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="qr-code-section" id="qrCodeSection" style="display: none;">
                            <div class="qr-code-container">
                                <div id="qrCodeDisplay"></div>
                                <p>Scan to view tournament on mobile devices</p>
                                <button class="btn-primary" onclick="downloadQRCode()">
                                    <i class="fas fa-download"></i>
                                    Download QR Code
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Participants Management Section -->
                <div class="tournament-section">
                    <div class="tournament-header">
                        <h2 class="tournament-title">
                            <i class="fas fa-users-cog"></i>
                            Participants Management
                        </h2>
                        <div class="tournament-actions">
                            <button class="btn-secondary" onclick="refreshParticipants()">
                                <i class="fas fa-sync"></i>
                                Refresh
                            </button>
                            <button class="btn-primary" onclick="openSeedingModal()"
                                    <?php echo empty($registrations) ? 'disabled' : ''; ?>>
                                <i class="fas fa-sort-numeric-up"></i>
                                Assign Seeding
                            </button>
                        </div>
                    </div>

                    <?php if (empty($registrations)): ?>
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-users-slash"></i>
                            </div>
                            <h3>No Participants Registered</h3>
                            <p>Departments need to register for this category before you can configure the tournament.</p>
                            <a href="manage-event.php?event_id=<?php echo $event_id; ?>" class="btn-primary">
                                <i class="fas fa-plus"></i>
                                Manage Registrations
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="participants-management">
                            <div class="participants-summary">
                                <div class="summary-stats">
                                    <div class="stat-item">
                                        <span class="stat-number"><?php echo count($registrations); ?></span>
                                        <span class="stat-label">Total Teams</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-number"><?php echo count(array_filter($registrations, fn($r) => $r['registration_status'] === 'confirmed')); ?></span>
                                        <span class="stat-label">Confirmed</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-number"><?php echo count(array_filter($registrations, fn($r) => $r['registration_status'] === 'pending')); ?></span>
                                        <span class="stat-label">Pending</span>
                                    </div>
                                </div>
                            </div>

                            <div class="participants-list">
                                <div class="list-header">
                                    <h4>Registered Departments</h4>
                                    <div class="list-controls">
                                        <button class="btn-sm btn-secondary" onclick="toggleSeedingMode()">
                                            <i class="fas fa-edit"></i>
                                            Edit Seeding
                                        </button>
                                        <button class="btn-sm btn-info" onclick="exportParticipants()">
                                            <i class="fas fa-download"></i>
                                            Export List
                                        </button>
                                    </div>
                                </div>

                                <div class="participants-grid" id="participantsList">
                                    <?php foreach ($registrations as $index => $registration): ?>
                                        <div class="participant-item" data-registration-id="<?php echo $registration['registration_id']; ?>"
                                             data-department-id="<?php echo $registration['department_id']; ?>">
                                            <div class="participant-drag-handle">
                                                <i class="fas fa-grip-vertical"></i>
                                            </div>

                                            <div class="participant-seed">
                                                <span class="seed-number"><?php echo $index + 1; ?></span>
                                            </div>

                                            <div class="participant-avatar" style="background-color: <?php echo $registration['color_code'] ?? '#3b82f6'; ?>">
                                                <?php echo strtoupper(substr($registration['department_abbr'] ?? $registration['department_name'], 0, 2)); ?>
                                            </div>

                                            <div class="participant-info">
                                                <h5><?php echo htmlspecialchars($registration['department_name']); ?></h5>
                                                <p><?php echo htmlspecialchars($registration['department_abbr'] ?? ''); ?></p>

                                                <?php if ($registration['participants']): ?>
                                                    <?php $participants = json_decode($registration['participants'], true); ?>
                                                    <div class="participant-members">
                                                        <?php if (is_array($participants)): ?>
                                                            <span class="members-count">
                                                                <i class="fas fa-user"></i>
                                                                <?php echo count($participants); ?> members
                                                            </span>
                                                            <div class="members-preview">
                                                                <?php foreach (array_slice($participants, 0, 2) as $participant): ?>
                                                                    <span class="member-name"><?php echo htmlspecialchars($participant['name'] ?? $participant); ?></span>
                                                                <?php endforeach; ?>
                                                                <?php if (count($participants) > 2): ?>
                                                                    <span class="more-members">+<?php echo count($participants) - 2; ?> more</span>
                                                                <?php endif; ?>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <div class="participant-status">
                                                <span class="status-badge status-<?php echo $registration['registration_status']; ?>">
                                                    <i class="fas fa-<?php echo $registration['registration_status'] === 'confirmed' ? 'check-circle' : 'clock'; ?>"></i>
                                                    <?php echo ucfirst($registration['registration_status']); ?>
                                                </span>
                                            </div>

                                            <div class="participant-actions">
                                                <button class="btn-sm btn-info" onclick="viewParticipantDetails(<?php echo $registration['registration_id']; ?>)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn-sm btn-secondary" onclick="editParticipant(<?php echo $registration['registration_id']; ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Tournament Actions Section -->
                <div class="tournament-section">
                    <div class="tournament-header">
                        <h2 class="tournament-title">
                            <i class="fas fa-play"></i>
                            Tournament Actions
                        </h2>
                    </div>

                    <div class="actions-grid">
                        <div class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="action-content">
                                <h4>Create Tournament</h4>
                                <p>Set up the tournament structure with the configured settings</p>
                                <button class="btn-primary" onclick="createTournamentFromConfig()"
                                        <?php echo $tournament_stats['confirmed_registrations'] < 2 ? 'disabled' : ''; ?>>
                                    <i class="fas fa-plus"></i>
                                    Create Tournament
                                </button>
                            </div>
                        </div>

                        <div class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-sitemap"></i>
                            </div>
                            <div class="action-content">
                                <h4>Generate Bracket</h4>
                                <p>Create the tournament bracket based on seeding and format</p>
                                <button class="btn-secondary" onclick="generateTournamentBracket()"
                                        <?php echo !$tournament_stats['tournament_exists'] ? 'disabled' : ''; ?>>
                                    <i class="fas fa-sitemap"></i>
                                    Generate Bracket
                                </button>
                            </div>
                        </div>

                        <div class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-download"></i>
                            </div>
                            <div class="action-content">
                                <h4>Export Data</h4>
                                <p>Download tournament information in various formats</p>
                                <div class="export-buttons">
                                    <button class="btn-sm btn-info" onclick="exportTournamentPDF()">
                                        <i class="fas fa-file-pdf"></i>
                                        PDF
                                    </button>
                                    <button class="btn-sm btn-success" onclick="exportTournamentCSV()">
                                        <i class="fas fa-file-csv"></i>
                                        CSV
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-print"></i>
                            </div>
                            <div class="action-content">
                                <h4>Print Materials</h4>
                                <p>Generate printable bracket and QR codes</p>
                                <div class="print-buttons">
                                    <button class="btn-sm btn-secondary" onclick="printBracket()">
                                        <i class="fas fa-print"></i>
                                        Bracket
                                    </button>
                                    <button class="btn-sm btn-primary" onclick="printQRCode()">
                                        <i class="fas fa-qrcode"></i>
                                        QR Code
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tournament Status Display -->
                    <?php if (!$tournament_stats['tournament_exists']): ?>
                        <div class="tournament-status no-tournament">
                            <div class="status-icon">🏆</div>
                            <h3 class="status-title">No Tournament Created</h3>
                            <p class="status-description">
                                Create a tournament to start organizing matches and tracking progress for this category.
                                <?php if ($tournament_stats['confirmed_registrations'] < 2): ?>
                                    <br><strong>Note:</strong> You need at least 2 confirmed registrations to create a tournament.
                                <?php endif; ?>
                            </p>

                            <?php if ($tournament_stats['confirmed_registrations'] >= 2): ?>
                                <div style="margin-top: 2rem;">
                                    <h4 style="margin-bottom: 1rem;">Available Tournament Formats:</h4>
                                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                                        <?php foreach (array_slice($available_formats, 0, 3) as $format): ?>
                                            <div style="background: white; padding: 1rem; border-radius: 8px; border: 1px solid #e5e7eb;">
                                                <h5 style="margin: 0 0 0.5rem 0; color: #1f2937;"><?php echo htmlspecialchars($format['name']); ?></h5>
                                                <p style="margin: 0; font-size: 0.9rem; color: #6b7280;">
                                                    <?php echo htmlspecialchars($format['description'] ?? 'Tournament format'); ?>
                                                </p>
                                                <div style="margin-top: 0.5rem; font-size: 0.8rem; color: #9ca3af;">
                                                    Min: <?php echo $format['min_participants']; ?> participants
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="tournament-status has-tournament">
                            <div class="status-icon">🎯</div>
                            <h3 class="status-title">Tournament Active</h3>
                            <p class="status-description">
                                <strong><?php echo htmlspecialchars($existing_tournament['format_name']); ?></strong> tournament is currently running.
                                <br>Round <?php echo $tournament_stats['current_round']; ?> of <?php echo $tournament_stats['total_rounds']; ?>
                                • Status: <?php echo ucfirst($tournament_stats['tournament_status']); ?>
                            </p>

                            <!-- Tournament Progress Bar -->
                            <div style="margin: 1.5rem 0;">
                                <div style="background: #e5e7eb; height: 8px; border-radius: 4px; overflow: hidden;">
                                    <?php
                                    $progress = $tournament_stats['total_rounds'] > 0 ?
                                        ($tournament_stats['current_round'] / $tournament_stats['total_rounds']) * 100 : 0;
                                    ?>
                                    <div style="background: #3b82f6; height: 100%; width: <?php echo $progress; ?>%; transition: width 0.3s ease;"></div>
                                </div>
                                <div style="text-align: center; margin-top: 0.5rem; font-size: 0.9rem; color: #6b7280;">
                                    Tournament Progress: <?php echo round($progress, 1); ?>%
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Tournament Format Information -->
                    <?php if ($available_formats): ?>
                        <div style="margin-top: 2rem;">
                            <h4 style="margin-bottom: 1rem; color: #1f2937;">
                                <i class="fas fa-info-circle"></i>
                                Tournament Format Guide
                            </h4>
                            <div style="background: #f8fafc; padding: 1.5rem; border-radius: 8px; border-left: 4px solid #3b82f6;">
                                <p style="margin: 0 0 1rem 0; color: #374151;">
                                    <strong>Sport Type:</strong> <?php echo ucfirst($category['sport_type_category'] ?? 'Team'); ?> Sport
                                </p>
                                <p style="margin: 0; color: #6b7280; font-size: 0.9rem;">
                                    Based on your sport type, the following tournament formats are recommended.
                                    Each format has different rules for advancement, seeding, and match progression.
                                </p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Registrations Tab -->
            <div id="registrations-tab" class="tab-content">
                <div class="tournament-section">
                    <div class="tournament-header">
                        <h2 class="tournament-title">
                            <i class="fas fa-users"></i>
                            Department Registrations
                        </h2>
                        <div class="tournament-actions">
                            <button class="btn-secondary" onclick="refreshRegistrations()">
                                <i class="fas fa-sync"></i>
                                Refresh
                            </button>
                            <button class="btn-primary" onclick="exportRegistrations()">
                                <i class="fas fa-download"></i>
                                Export
                            </button>
                        </div>
                    </div>

                    <?php if (empty($registrations)): ?>
                        <div class="tournament-status no-tournament">
                            <div class="status-icon">👥</div>
                            <h3 class="status-title">No Registrations Yet</h3>
                            <p class="status-description">
                                No departments have registered for this category yet.
                                Registrations will appear here once departments sign up.
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="registrations-grid">
                            <?php foreach ($registrations as $registration): ?>
                                <div class="registration-card" style="--dept-color: <?php echo $registration['color_code'] ?? '#3b82f6'; ?>">
                                    <div class="dept-header">
                                        <div class="dept-avatar">
                                            <?php echo strtoupper(substr($registration['department_abbr'] ?? $registration['department_name'], 0, 2)); ?>
                                        </div>
                                        <div class="dept-info">
                                            <h3><?php echo htmlspecialchars($registration['department_name']); ?></h3>
                                            <p><?php echo htmlspecialchars($registration['department_abbr'] ?? ''); ?></p>
                                        </div>
                                    </div>

                                    <?php if ($registration['participants']): ?>
                                        <?php $participants = json_decode($registration['participants'], true); ?>
                                        <div style="margin: 1rem 0;">
                                            <strong>Participants:</strong>
                                            <div style="margin-top: 0.5rem; font-size: 0.9rem; color: #6b7280;">
                                                <?php if (is_array($participants)): ?>
                                                    <?php foreach (array_slice($participants, 0, 3) as $participant): ?>
                                                        <div>• <?php echo htmlspecialchars($participant['name'] ?? $participant); ?></div>
                                                    <?php endforeach; ?>
                                                    <?php if (count($participants) > 3): ?>
                                                        <div>... and <?php echo count($participants) - 3; ?> more</div>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <div>Participant information available</div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <div class="registration-status status-<?php echo $registration['registration_status']; ?>">
                                        <i class="fas fa-<?php echo $registration['registration_status'] === 'confirmed' ? 'check-circle' : 'clock'; ?>"></i>
                                        <?php echo ucfirst($registration['registration_status']); ?>
                                    </div>

                                    <div style="margin-top: 1rem; font-size: 0.8rem; color: #9ca3af;">
                                        Registered: <?php echo date('M j, Y', strtotime($registration['created_at'])); ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Matches Tab -->
            <div id="matches-tab" class="tab-content">
                <div class="tournament-section">
                    <div class="tournament-header">
                        <h2 class="tournament-title">
                            <i class="fas fa-gamepad"></i>
                            Tournament Matches
                        </h2>
                        <div class="tournament-actions">
                            <button class="btn-secondary" onclick="refreshMatches()">
                                <i class="fas fa-sync"></i>
                                Refresh
                            </button>
                            <?php if ($tournament_stats['tournament_exists']): ?>
                                <button class="btn-primary" onclick="scheduleMatches()">
                                    <i class="fas fa-calendar-plus"></i>
                                    Schedule Matches
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div id="matches-content">
                        <?php if (!$tournament_stats['tournament_exists']): ?>
                            <div class="tournament-status no-tournament">
                                <div class="status-icon">🎮</div>
                                <h3 class="status-title">No Tournament Matches</h3>
                                <p class="status-description">
                                    Create a tournament first to generate and manage matches.
                                </p>
                            </div>
                        <?php else: ?>
                            <div class="tournament-status has-tournament">
                                <div class="status-icon">⚡</div>
                                <h3 class="status-title">Loading Matches...</h3>
                                <p class="status-description">
                                    Tournament matches will be displayed here.
                                </p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Standings Tab -->
            <div id="standings-tab" class="tab-content">
                <div class="tournament-section">
                    <div class="tournament-header">
                        <h2 class="tournament-title">
                            <i class="fas fa-chart-line"></i>
                            Tournament Standings
                        </h2>
                        <div class="tournament-actions">
                            <button class="btn-secondary" onclick="refreshStandings()">
                                <i class="fas fa-sync"></i>
                                Refresh
                            </button>
                            <?php if ($tournament_stats['tournament_exists']): ?>
                                <button class="btn-primary" onclick="exportStandings()">
                                    <i class="fas fa-download"></i>
                                    Export
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div id="standings-content">
                        <?php if (!$tournament_stats['tournament_exists']): ?>
                            <div class="tournament-status no-tournament">
                                <div class="status-icon">📊</div>
                                <h3 class="status-title">No Tournament Standings</h3>
                                <p class="status-description">
                                    Tournament standings will be available once a tournament is created and matches begin.
                                </p>
                            </div>
                        <?php else: ?>
                            <div class="tournament-status has-tournament">
                                <div class="status-icon">🏆</div>
                                <h3 class="status-title">Loading Standings...</h3>
                                <p class="status-description">
                                    Current tournament standings will be displayed here.
                                </p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div id="settings-tab" class="tab-content">
                <div class="tournament-section">
                    <div class="tournament-header">
                        <h2 class="tournament-title">
                            <i class="fas fa-cog"></i>
                            Tournament Settings
                        </h2>
                        <div class="tournament-actions">
                            <button class="btn-primary" onclick="saveSettings()">
                                <i class="fas fa-save"></i>
                                Save Changes
                            </button>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 2rem;">
                        <!-- Category Settings -->
                        <div style="background: #f8fafc; padding: 1.5rem; border-radius: 8px;">
                            <h4 style="margin-bottom: 1rem; color: #1f2937;">
                                <i class="fas fa-layer-group"></i>
                                Category Information
                            </h4>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Category Name</label>
                                <input type="text" value="<?php echo htmlspecialchars($category['category_name']); ?>"
                                       style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Venue</label>
                                <input type="text" value="<?php echo htmlspecialchars($category['venue'] ?? ''); ?>"
                                       style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Max Participants</label>
                                <input type="number" value="<?php echo $category['max_participants'] ?? 0; ?>"
                                       style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                            </div>
                        </div>

                        <!-- Referee Settings -->
                        <div style="background: #f8fafc; padding: 1.5rem; border-radius: 8px;">
                            <h4 style="margin-bottom: 1rem; color: #1f2937;">
                                <i class="fas fa-whistle"></i>
                                Referee Information
                            </h4>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Referee Name</label>
                                <input type="text" value="<?php echo htmlspecialchars($category['referee_name'] ?? ''); ?>"
                                       style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Referee Email</label>
                                <input type="email" value="<?php echo htmlspecialchars($category['referee_email'] ?? ''); ?>"
                                       style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                            </div>
                        </div>
                    </div>

                    <!-- Tournament Format Settings -->
                    <?php if ($tournament_stats['tournament_exists']): ?>
                        <div style="margin-top: 2rem; background: #f0f9ff; padding: 1.5rem; border-radius: 8px; border: 1px solid #0ea5e9;">
                            <h4 style="margin-bottom: 1rem; color: #1f2937;">
                                <i class="fas fa-trophy"></i>
                                Active Tournament Settings
                            </h4>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Format</label>
                                    <input type="text" value="<?php echo htmlspecialchars($existing_tournament['format_name']); ?>"
                                           readonly style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px; background: #f9fafb;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Status</label>
                                    <input type="text" value="<?php echo ucfirst($tournament_stats['tournament_status']); ?>"
                                           readonly style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px; background: #f9fafb;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Participants</label>
                                    <input type="text" value="<?php echo $existing_tournament['participant_count'] ?? 0; ?>"
                                           readonly style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px; background: #f9fafb;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Seeding Method</label>
                                    <input type="text" value="<?php echo ucfirst($existing_tournament['seeding_method'] ?? 'random'); ?>"
                                           readonly style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px; background: #f9fafb;">
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>

    <!-- Tournament Configuration Modal -->
    <div id="tournamentConfigModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 700px;">
            <div class="modal-header">
                <h3><i class="fas fa-cogs"></i> Tournament Configuration</h3>
                <button class="modal-close" onclick="closeModal('tournamentConfigModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="tournamentConfigForm">
                    <input type="hidden" name="event_sport_id" value="<?php echo $category['event_sport_id']; ?>">
                    <input type="hidden" name="category_id" value="<?php echo $category_id; ?>">

                    <!-- Tournament Basic Info -->
                    <div class="config-section">
                        <h4><i class="fas fa-info-circle"></i> Basic Information</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label>Tournament Name</label>
                                <input type="text" name="tournament_name" id="modalTournamentName"
                                       value="<?php echo htmlspecialchars($category['category_name'] . ' Tournament'); ?>"
                                       class="form-control" required>
                            </div>
                        </div>
                    </div>

                    <!-- Format Selection -->
                    <div class="config-section">
                        <h4><i class="fas fa-trophy"></i> Tournament Format</h4>
                        <div class="form-group">
                            <label>Select Format</label>
                            <select name="format_id" id="modalTournamentFormat" class="form-control" required onchange="updateModalFormatDetails()">
                                <option value="">Choose tournament format...</option>
                                <?php foreach ($available_formats as $format): ?>
                                    <option value="<?php echo $format['id']; ?>"
                                            data-description="<?php echo htmlspecialchars($format['description'] ?? ''); ?>"
                                            data-min-participants="<?php echo $format['min_participants']; ?>"
                                            data-max-participants="<?php echo $format['max_participants'] ?? 'unlimited'; ?>"
                                            data-seeding-required="<?php echo $format['requires_seeding'] ? 'true' : 'false'; ?>"
                                            data-advancement-type="<?php echo $format['advancement_type']; ?>">
                                        <?php echo htmlspecialchars($format['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div id="modalFormatDetails" class="format-details" style="display: none;">
                            <div class="format-info">
                                <h5>Format Details</h5>
                                <p class="format-description"></p>
                                <div class="format-specs">
                                    <span class="spec-item">
                                        <i class="fas fa-users"></i>
                                        <span class="spec-label">Participants:</span>
                                        <span class="spec-value" id="modalParticipantRange"></span>
                                    </span>
                                    <span class="spec-item">
                                        <i class="fas fa-sort-numeric-up"></i>
                                        <span class="spec-label">Seeding:</span>
                                        <span class="spec-value" id="modalSeedingRequired"></span>
                                    </span>
                                    <span class="spec-item">
                                        <i class="fas fa-arrow-up"></i>
                                        <span class="spec-label">Advancement:</span>
                                        <span class="spec-value" id="modalAdvancementType"></span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tournament Settings -->
                    <div class="config-section">
                        <h4><i class="fas fa-sliders-h"></i> Tournament Settings</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label>Seeding Method</label>
                                <select name="seeding_method" id="modalSeedingMethod" class="form-control">
                                    <option value="random">Random Seeding</option>
                                    <option value="ranking">Ranking-based Seeding</option>
                                    <option value="manual">Manual Seeding</option>
                                    <option value="hybrid">Hybrid Seeding</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Bye Handling</label>
                                <select name="bye_handling" id="modalByeHandling" class="form-control">
                                    <option value="automatic">Automatic Assignment</option>
                                    <option value="manual">Manual Assignment</option>
                                    <option value="highest_seed">Highest Seed Priority</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="third_place_playoff" id="modalThirdPlacePlayoff">
                                    <span class="checkmark"></span>
                                    Include Third Place Playoff
                                </label>
                                <small class="form-text">Add a match for 3rd/4th place (applicable for elimination formats)</small>
                            </div>
                        </div>
                    </div>

                    <!-- Participants Preview -->
                    <div class="config-section">
                        <h4><i class="fas fa-users"></i> Participants (<?php echo $tournament_stats['confirmed_registrations']; ?>)</h4>
                        <div class="participants-preview">
                            <?php if (!empty($registrations)): ?>
                                <div class="preview-list">
                                    <?php foreach (array_slice($registrations, 0, 6) as $registration): ?>
                                        <div class="preview-item">
                                            <div class="preview-avatar" style="background-color: <?php echo $registration['color_code'] ?? '#3b82f6'; ?>">
                                                <?php echo strtoupper(substr($registration['department_abbr'] ?? $registration['department_name'], 0, 2)); ?>
                                            </div>
                                            <span><?php echo htmlspecialchars($registration['department_name']); ?></span>
                                        </div>
                                    <?php endforeach; ?>
                                    <?php if (count($registrations) > 6): ?>
                                        <div class="preview-item">
                                            <div class="preview-avatar" style="background-color: #6b7280;">
                                                +<?php echo count($registrations) - 6; ?>
                                            </div>
                                            <span>More teams...</span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <p style="color: #6b7280; text-align: center; padding: 1rem;">No participants registered yet.</p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="config-warning">
                        <div class="warning-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="warning-content">
                            <h5>Important Notes</h5>
                            <ul>
                                <li>Tournament structure cannot be changed once created</li>
                                <li>All confirmed registrations will be included automatically</li>
                                <li>Matches will be generated based on the selected format and seeding</li>
                                <li>Make sure all settings are correct before proceeding</li>
                            </ul>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeModal('tournamentConfigModal')">Cancel</button>
                <button type="button" class="btn-primary" onclick="createTournamentFromModal()"
                        <?php echo $tournament_stats['confirmed_registrations'] < 2 ? 'disabled' : ''; ?>>
                    <i class="fas fa-cogs"></i> Create Tournament
                </button>
            </div>
        </div>
    </div>

    <!-- Seeding Management Modal -->
    <div id="seedingModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h3><i class="fas fa-sort-numeric-up"></i> Manage Seeding</h3>
                <button class="modal-close" onclick="closeModal('seedingModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="seeding-controls">
                    <div class="seeding-method-selector">
                        <label>Seeding Method:</label>
                        <select id="seedingMethodSelector" class="form-control" onchange="applySeedingMethod()">
                            <option value="manual">Manual Seeding</option>
                            <option value="random">Random Seeding</option>
                            <option value="ranking">Ranking-based</option>
                            <option value="alphabetical">Alphabetical</option>
                        </select>
                    </div>
                    <div class="seeding-actions">
                        <button class="btn-secondary" onclick="resetSeeding()">
                            <i class="fas fa-undo"></i> Reset
                        </button>
                        <button class="btn-primary" onclick="saveSeeding()">
                            <i class="fas fa-save"></i> Save Seeding
                        </button>
                    </div>
                </div>

                <div id="seedingList" class="seeding-list">
                    <!-- Seeding list will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Tournament Bracket Modal -->
    <div id="bracketModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 90vw; max-height: 90vh;">
            <div class="modal-header">
                <h3><i class="fas fa-sitemap"></i> Tournament Bracket</h3>
                <button class="modal-close" onclick="closeModal('bracketModal')">&times;</button>
            </div>
            <div class="modal-body" style="overflow: auto;">
                <div id="bracketContent">
                    <div style="text-align: center; padding: 2rem; color: #6b7280;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                        <p>Loading tournament bracket...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include Admin Scripts -->
    <?php include 'includes/admin-scripts.php'; ?>

    <script>
        // Global variables
        const eventId = <?php echo $event_id; ?>;
        const sportId = <?php echo $sport_id; ?>;
        const categoryId = <?php echo $category_id; ?>;
        const eventSportId = <?php echo $category['event_sport_id']; ?>;
        const tournamentExists = <?php echo $tournament_stats['tournament_exists'] ? 'true' : 'false'; ?>;

        // Tab switching functionality
        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName + '-tab').classList.add('active');

            // Add active class to selected tab button
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // Load tab-specific content
            loadTabContent(tabName);
        }

        // Load content for specific tabs
        function loadTabContent(tabName) {
            switch(tabName) {
                case 'matches':
                    if (tournamentExists) {
                        loadMatches();
                    }
                    break;
                case 'standings':
                    if (tournamentExists) {
                        loadStandings();
                    }
                    break;
            }
        }

        // Tournament format selection handlers
        function updateFormatDetails() {
            const select = document.getElementById('tournamentFormat');
            const formatDetails = document.getElementById('formatDetails');

            if (select.value) {
                const option = select.selectedOptions[0];
                const description = option.dataset.description;
                const minParticipants = option.dataset.minParticipants;
                const maxParticipants = option.dataset.maxParticipants;
                const seedingRequired = option.dataset.seedingRequired;
                const advancementType = option.dataset.advancementType;

                formatDetails.querySelector('.format-description').textContent = description;

                let participantRange = minParticipants;
                if (maxParticipants !== 'unlimited') {
                    participantRange += ` - ${maxParticipants}`;
                } else {
                    participantRange += '+';
                }

                document.getElementById('participantRange').textContent = participantRange;
                document.getElementById('seedingRequired').textContent = seedingRequired === 'true' ? 'Required' : 'Optional';
                document.getElementById('advancementType').textContent = advancementType ?
                    advancementType.charAt(0).toUpperCase() + advancementType.slice(1) : 'Standard';

                formatDetails.style.display = 'block';
            } else {
                formatDetails.style.display = 'none';
            }
        }

        function updateModalFormatDetails() {
            const select = document.getElementById('modalTournamentFormat');
            const formatDetails = document.getElementById('modalFormatDetails');

            if (select.value) {
                const option = select.selectedOptions[0];
                const description = option.dataset.description;
                const minParticipants = option.dataset.minParticipants;
                const maxParticipants = option.dataset.maxParticipants;
                const seedingRequired = option.dataset.seedingRequired;
                const advancementType = option.dataset.advancementType;

                formatDetails.querySelector('.format-description').textContent = description;

                let participantRange = minParticipants;
                if (maxParticipants !== 'unlimited') {
                    participantRange += ` - ${maxParticipants}`;
                } else {
                    participantRange += '+';
                }

                document.getElementById('modalParticipantRange').textContent = participantRange;
                document.getElementById('modalSeedingRequired').textContent = seedingRequired === 'true' ? 'Required' : 'Optional';
                document.getElementById('modalAdvancementType').textContent = advancementType ?
                    advancementType.charAt(0).toUpperCase() + advancementType.slice(1) : 'Standard';

                formatDetails.style.display = 'block';
            } else {
                formatDetails.style.display = 'none';
            }
        }

        // Modal functions
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Tournament management functions
        function openTournamentConfigModal() {
            // Sync form values with overview form
            const overviewFormat = document.getElementById('tournamentFormat');
            const overviewName = document.getElementById('tournamentName');
            const overviewSeeding = document.getElementById('seedingMethod');
            const overviewThirdPlace = document.getElementById('thirdPlacePlayoff');
            const overviewByeHandling = document.getElementById('byeHandling');

            const modalFormat = document.getElementById('modalTournamentFormat');
            const modalName = document.getElementById('modalTournamentName');
            const modalSeeding = document.getElementById('modalSeedingMethod');
            const modalThirdPlace = document.getElementById('modalThirdPlacePlayoff');
            const modalByeHandling = document.getElementById('modalByeHandling');

            if (overviewFormat && modalFormat) modalFormat.value = overviewFormat.value;
            if (overviewName && modalName) modalName.value = overviewName.value;
            if (overviewSeeding && modalSeeding) modalSeeding.value = overviewSeeding.value;
            if (overviewThirdPlace && modalThirdPlace) modalThirdPlace.checked = overviewThirdPlace.checked;
            if (overviewByeHandling && modalByeHandling) modalByeHandling.value = overviewByeHandling.value;

            // Update modal format details
            updateModalFormatDetails();

            openModal('tournamentConfigModal');
        }

        function createTournamentFromConfig() {
            // Get configuration from overview form
            const formatSelect = document.getElementById('tournamentFormat');
            const nameInput = document.getElementById('tournamentName');
            const seedingSelect = document.getElementById('seedingMethod');
            const thirdPlaceCheckbox = document.getElementById('thirdPlacePlayoff');
            const byeHandlingSelect = document.getElementById('byeHandling');

            if (!formatSelect.value) {
                showNotification('Please select a tournament format', 'warning');
                return;
            }

            if (!nameInput.value.trim()) {
                showNotification('Please enter a tournament name', 'warning');
                return;
            }

            const config = {
                action: 'create_tournament',
                event_sport_id: eventSportId,
                category_id: categoryId,
                tournament_name: nameInput.value.trim(),
                format_id: formatSelect.value,
                seeding_method: seedingSelect.value,
                third_place_playoff: thirdPlaceCheckbox.checked,
                bye_handling: byeHandlingSelect.value
            };

            createTournamentWithConfig(config);
        }

        function createTournamentFromModal() {
            const form = document.getElementById('tournamentConfigForm');
            const formData = new FormData(form);
            formData.append('action', 'create_tournament');

            const config = {
                action: 'create_tournament',
                event_sport_id: eventSportId,
                category_id: categoryId,
                tournament_name: formData.get('tournament_name'),
                format_id: formData.get('format_id'),
                seeding_method: formData.get('seeding_method'),
                third_place_playoff: formData.get('third_place_playoff') === 'on',
                bye_handling: formData.get('bye_handling')
            };

            createTournamentWithConfig(config);
        }

        function createTournamentWithConfig(config) {
            // Show loading state
            const buttons = document.querySelectorAll('.btn-primary');
            buttons.forEach(btn => {
                if (btn.textContent.includes('Create Tournament') || btn.textContent.includes('Configure Tournament')) {
                    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';
                    btn.disabled = true;
                }
            });

            const formData = new FormData();
            Object.keys(config).forEach(key => {
                formData.append(key, config[key]);
            });

            fetch('ajax/tournament-management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Tournament created successfully!', 'success');
                    closeModal('tournamentConfigModal');
                    // Reload page to show new tournament
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showNotification(data.message || 'Failed to create tournament', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('An error occurred while creating the tournament', 'error');
            })
            .finally(() => {
                // Restore button states
                buttons.forEach(btn => {
                    if (btn.textContent.includes('Creating...')) {
                        btn.innerHTML = '<i class="fas fa-plus"></i> Create Tournament';
                        btn.disabled = false;
                    }
                });
            });
        }

        function viewTournamentBracket() {
            openModal('bracketModal');
            loadBracket();
        }

        function loadBracket() {
            const content = document.getElementById('bracketContent');
            content.innerHTML = `
                <div style="text-align: center; padding: 2rem; color: #6b7280;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <p>Loading tournament bracket...</p>
                </div>
            `;

            fetch(`ajax/tournament-management.php?action=get_bracket_visualization&event_sport_id=${eventSportId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    content.innerHTML = data.bracket_html || '<p>Bracket visualization not available</p>';
                } else {
                    content.innerHTML = `<p style="color: #ef4444;">Error loading bracket: ${data.message}</p>`;
                }
            })
            .catch(error => {
                console.error('Error loading bracket:', error);
                content.innerHTML = '<p style="color: #ef4444;">Failed to load tournament bracket</p>';
            });
        }

        function loadMatches() {
            const content = document.getElementById('matches-content');
            content.innerHTML = `
                <div style="text-align: center; padding: 2rem; color: #6b7280;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <p>Loading matches...</p>
                </div>
            `;

            fetch(`ajax/tournament-management.php?action=get_tournament_matches&event_sport_id=${eventSportId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayMatches(data.matches || []);
                } else {
                    content.innerHTML = `<p style="color: #ef4444;">Error loading matches: ${data.message}</p>`;
                }
            })
            .catch(error => {
                console.error('Error loading matches:', error);
                content.innerHTML = '<p style="color: #ef4444;">Failed to load matches</p>';
            });
        }

        function displayMatches(matches) {
            const content = document.getElementById('matches-content');

            if (matches.length === 0) {
                content.innerHTML = `
                    <div class="tournament-status no-tournament">
                        <div class="status-icon">🎮</div>
                        <h3 class="status-title">No Matches Scheduled</h3>
                        <p class="status-description">Tournament matches will appear here once they are generated.</p>
                    </div>
                `;
                return;
            }

            let matchesHtml = '<div style="display: grid; gap: 1rem;">';
            matches.forEach(match => {
                matchesHtml += `
                    <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <strong>${match.team1_name || 'TBD'}</strong> vs <strong>${match.team2_name || 'TBD'}</strong>
                            </div>
                            <div style="font-size: 0.9rem; color: #6b7280;">
                                Round ${match.round_number} • ${match.status}
                            </div>
                        </div>
                        ${match.scheduled_time ? `<div style="margin-top: 0.5rem; font-size: 0.9rem; color: #6b7280;">
                            <i class="fas fa-clock"></i> ${new Date(match.scheduled_time).toLocaleString()}
                        </div>` : ''}
                    </div>
                `;
            });
            matchesHtml += '</div>';

            content.innerHTML = matchesHtml;
        }

        function loadStandings() {
            const content = document.getElementById('standings-content');
            content.innerHTML = `
                <div style="text-align: center; padding: 2rem; color: #6b7280;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <p>Loading standings...</p>
                </div>
            `;

            fetch(`ajax/tournament-management.php?action=get_tournament_standings&event_sport_id=${eventSportId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayStandings(data.standings || []);
                } else {
                    content.innerHTML = `<p style="color: #ef4444;">Error loading standings: ${data.message}</p>`;
                }
            })
            .catch(error => {
                console.error('Error loading standings:', error);
                content.innerHTML = '<p style="color: #ef4444;">Failed to load standings</p>';
            });
        }

        function displayStandings(standings) {
            const content = document.getElementById('standings-content');

            if (standings.length === 0) {
                content.innerHTML = `
                    <div class="tournament-status no-tournament">
                        <div class="status-icon">📊</div>
                        <h3 class="status-title">No Standings Available</h3>
                        <p class="status-description">Tournament standings will appear here once matches are completed.</p>
                    </div>
                `;
                return;
            }

            let standingsHtml = `
                <div style="background: white; border-radius: 8px; overflow: hidden; border: 1px solid #e5e7eb;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead style="background: #f9fafb;">
                            <tr>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Rank</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e5e7eb;">Team</th>
                                <th style="padding: 1rem; text-align: center; border-bottom: 1px solid #e5e7eb;">Wins</th>
                                <th style="padding: 1rem; text-align: center; border-bottom: 1px solid #e5e7eb;">Losses</th>
                                <th style="padding: 1rem; text-align: center; border-bottom: 1px solid #e5e7eb;">Points</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            standings.forEach((team, index) => {
                standingsHtml += `
                    <tr style="border-bottom: 1px solid #f3f4f6;">
                        <td style="padding: 1rem; font-weight: 600;">${index + 1}</td>
                        <td style="padding: 1rem;">${team.team_name || team.department_name}</td>
                        <td style="padding: 1rem; text-align: center;">${team.wins || 0}</td>
                        <td style="padding: 1rem; text-align: center;">${team.losses || 0}</td>
                        <td style="padding: 1rem; text-align: center; font-weight: 600;">${team.points || 0}</td>
                    </tr>
                `;
            });

            standingsHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            content.innerHTML = standingsHtml;
        }

        // Utility functions
        function refreshRegistrations() {
            window.location.reload();
        }

        function refreshMatches() {
            if (tournamentExists) {
                loadMatches();
            }
        }

        function refreshStandings() {
            if (tournamentExists) {
                loadStandings();
            }
        }

        function manageTournamentRounds() {
            showNotification('Tournament round management coming soon!', 'info');
        }

        function viewTournamentStandings() {
            switchTab('standings');
        }

        function scheduleMatches() {
            showNotification('Match scheduling interface coming soon!', 'info');
        }

        function exportRegistrations() {
            window.open(`export-registrations.php?event_sport_id=${eventSportId}`, '_blank');
        }

        function exportStandings() {
            window.open(`export-standings.php?event_sport_id=${eventSportId}`, '_blank');
        }

        function saveSettings() {
            showNotification('Settings saved successfully!', 'success');
        }

        // Overview-specific functions
        function editTournamentConfig() {
            openTournamentConfigModal();
        }

        function generateBracket() {
            if (!tournamentExists) {
                showNotification('Please create a tournament first', 'warning');
                return;
            }

            fetch(`ajax/tournament-management.php?action=generate_bracket&event_sport_id=${eventSportId}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Bracket generated successfully!', 'success');
                    viewTournamentBracket();
                } else {
                    showNotification(data.message || 'Failed to generate bracket', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('An error occurred while generating the bracket', 'error');
            });
        }

        function copyPublicUrl() {
            copyToClipboard('publicUrl');
        }

        function generateQRCode() {
            const qrSection = document.getElementById('qrCodeSection');
            const qrDisplay = document.getElementById('qrCodeDisplay');
            const publicUrl = document.getElementById('publicUrl').value;

            // Simple QR code generation (in a real implementation, you'd use a QR code library)
            qrDisplay.innerHTML = `
                <div style="width: 200px; height: 200px; background: #f3f4f6; border: 2px solid #e5e7eb;
                           display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                    <div style="text-align: center; color: #6b7280;">
                        <i class="fas fa-qrcode" style="font-size: 3rem; margin-bottom: 0.5rem;"></i>
                        <p style="margin: 0; font-size: 0.8rem;">QR Code for:<br>${publicUrl}</p>
                    </div>
                </div>
            `;

            qrSection.style.display = 'block';
            showNotification('QR code generated! In production, this would be a real QR code.', 'info');
        }

        function downloadQRCode() {
            showNotification('QR code download would be implemented with a QR code library', 'info');
        }

        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            element.select();
            element.setSelectionRange(0, 99999); // For mobile devices

            try {
                document.execCommand('copy');
                showNotification('Copied to clipboard!', 'success');
            } catch (err) {
                showNotification('Failed to copy to clipboard', 'error');
            }
        }

        // Participants management functions
        function refreshParticipants() {
            window.location.reload();
        }

        function openSeedingModal() {
            populateSeedingList();
            openModal('seedingModal');
        }

        function populateSeedingList() {
            const seedingList = document.getElementById('seedingList');
            const participantItems = document.querySelectorAll('.participant-item');

            let html = '<div class="sortable-list">';
            participantItems.forEach((item, index) => {
                const deptName = item.querySelector('.participant-info h5').textContent;
                const avatar = item.querySelector('.participant-avatar');
                const bgColor = avatar.style.backgroundColor;
                const initials = avatar.textContent;

                html += `
                    <div class="seeding-item" data-registration-id="${item.dataset.registrationId}">
                        <div class="seeding-rank">${index + 1}</div>
                        <div class="seeding-avatar" style="background-color: ${bgColor}">${initials}</div>
                        <div class="seeding-name">${deptName}</div>
                        <div class="seeding-controls">
                            <button class="btn-sm btn-secondary" onclick="moveSeedUp(${index})">
                                <i class="fas fa-arrow-up"></i>
                            </button>
                            <button class="btn-sm btn-secondary" onclick="moveSeedDown(${index})">
                                <i class="fas fa-arrow-down"></i>
                            </button>
                        </div>
                    </div>
                `;
            });
            html += '</div>';

            seedingList.innerHTML = html;
        }

        function applySeedingMethod() {
            const method = document.getElementById('seedingMethodSelector').value;

            switch(method) {
                case 'random':
                    randomizeSeeding();
                    break;
                case 'alphabetical':
                    alphabeticalSeeding();
                    break;
                case 'ranking':
                    showNotification('Ranking-based seeding would require additional ranking data', 'info');
                    break;
                default:
                    // Manual seeding - do nothing
                    break;
            }
        }

        function randomizeSeeding() {
            const seedingItems = Array.from(document.querySelectorAll('.seeding-item'));
            const shuffled = seedingItems.sort(() => Math.random() - 0.5);

            const container = document.querySelector('.sortable-list');
            container.innerHTML = '';

            shuffled.forEach((item, index) => {
                item.querySelector('.seeding-rank').textContent = index + 1;
                container.appendChild(item);
            });

            showNotification('Seeding randomized', 'success');
        }

        function alphabeticalSeeding() {
            const seedingItems = Array.from(document.querySelectorAll('.seeding-item'));
            const sorted = seedingItems.sort((a, b) => {
                const nameA = a.querySelector('.seeding-name').textContent;
                const nameB = b.querySelector('.seeding-name').textContent;
                return nameA.localeCompare(nameB);
            });

            const container = document.querySelector('.sortable-list');
            container.innerHTML = '';

            sorted.forEach((item, index) => {
                item.querySelector('.seeding-rank').textContent = index + 1;
                container.appendChild(item);
            });

            showNotification('Seeding sorted alphabetically', 'success');
        }

        function resetSeeding() {
            populateSeedingList();
            showNotification('Seeding reset to original order', 'info');
        }

        function saveSeeding() {
            const seedingItems = document.querySelectorAll('.seeding-item');
            const seedingData = [];

            seedingItems.forEach((item, index) => {
                seedingData.push({
                    registration_id: item.dataset.registrationId,
                    seed_number: index + 1
                });
            });

            // In a real implementation, this would save to the database
            console.log('Seeding data:', seedingData);
            showNotification('Seeding saved successfully!', 'success');
            closeModal('seedingModal');
        }

        function toggleSeedingMode() {
            showNotification('Seeding mode toggle - would enable drag-and-drop reordering', 'info');
        }

        function viewParticipantDetails(registrationId) {
            showNotification(`View details for registration ${registrationId}`, 'info');
        }

        function editParticipant(registrationId) {
            showNotification(`Edit participant ${registrationId}`, 'info');
        }

        function exportParticipants() {
            window.open(`export-participants.php?event_sport_id=${eventSportId}`, '_blank');
        }

        // Tournament action functions
        function generateTournamentBracket() {
            generateBracket();
        }

        function exportTournamentPDF() {
            window.open(`export-tournament.php?event_sport_id=${eventSportId}&format=pdf`, '_blank');
        }

        function exportTournamentCSV() {
            window.open(`export-tournament.php?event_sport_id=${eventSportId}&format=csv`, '_blank');
        }

        function printBracket() {
            if (tournamentExists) {
                window.open(`print-bracket.php?event_sport_id=${eventSportId}`, '_blank');
            } else {
                showNotification('Please create a tournament first', 'warning');
            }
        }

        function printQRCode() {
            generateQRCode();
            setTimeout(() => {
                window.print();
            }, 500);
        }

        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                animation: slideIn 0.3s ease;
            `;

            // Set background color based on type
            const colors = {
                success: '#10b981',
                error: '#ef4444',
                warning: '#f59e0b',
                info: '#3b82f6'
            };
            notification.style.background = colors[type] || colors.info;
            notification.textContent = message;

            // Add to page
            document.body.appendChild(notification);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners for format selection
            const overviewFormatSelect = document.getElementById('tournamentFormat');
            if (overviewFormatSelect) {
                overviewFormatSelect.addEventListener('change', updateFormatDetails);
            }

            const modalFormatSelect = document.getElementById('modalTournamentFormat');
            if (modalFormatSelect) {
                modalFormatSelect.addEventListener('change', updateModalFormatDetails);
            }

            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey) {
                    switch(e.key) {
                        case '1':
                            e.preventDefault();
                            switchTab('overview');
                            break;
                        case '2':
                            e.preventDefault();
                            switchTab('registrations');
                            break;
                        case '3':
                            e.preventDefault();
                            switchTab('matches');
                            break;
                        case '4':
                            e.preventDefault();
                            switchTab('standings');
                            break;
                        case '5':
                            e.preventDefault();
                            switchTab('settings');
                            break;
                    }
                }
            });

            // Close modals when clicking outside
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('modal')) {
                    e.target.style.display = 'none';
                    document.body.style.overflow = 'auto';
                }
            });

            // Initialize drag and drop for participants (if needed)
            initializeParticipantSorting();

            console.log('Tournament Management Page with Overview initialized');
        });

        // Initialize participant sorting functionality
        function initializeParticipantSorting() {
            // This would initialize drag-and-drop sorting for participants
            // In a full implementation, you might use a library like Sortable.js
            console.log('Participant sorting initialized');
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
            .modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            }
            .modal-content {
                background: white;
                border-radius: 12px;
                max-width: 500px;
                width: 90%;
                max-height: 90vh;
                overflow-y: auto;
                box-shadow: 0 20px 25px -5px rgba(0,0,0,0.1);
            }
            .modal-header {
                padding: 1.5rem;
                border-bottom: 1px solid #e5e7eb;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .modal-header h3 {
                margin: 0;
                font-size: 1.25rem;
                font-weight: 600;
            }
            .modal-close {
                background: none;
                border: none;
                font-size: 1.5rem;
                cursor: pointer;
                color: #6b7280;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: all 0.2s;
            }
            .modal-close:hover {
                background: #f3f4f6;
                color: #374151;
            }
            .modal-body {
                padding: 1.5rem;
            }
            .modal-footer {
                padding: 1.5rem;
                border-top: 1px solid #e5e7eb;
                display: flex;
                gap: 1rem;
                justify-content: flex-end;
            }
            .breadcrumb {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 0.9rem;
                color: #6b7280;
            }
            .breadcrumb a {
                color: #3b82f6;
                text-decoration: none;
                display: flex;
                align-items: center;
                gap: 0.25rem;
            }
            .breadcrumb a:hover {
                text-decoration: underline;
            }
            .breadcrumb .separator {
                color: #d1d5db;
            }
            .breadcrumb .current {
                color: #374151;
                font-weight: 500;
                display: flex;
                align-items: center;
                gap: 0.25rem;
            }

            /* Modal-specific styles */
            .config-section {
                margin-bottom: 2rem;
                padding-bottom: 1.5rem;
                border-bottom: 1px solid #e5e7eb;
            }

            .config-section:last-child {
                border-bottom: none;
                margin-bottom: 0;
            }

            .config-section h4 {
                margin: 0 0 1rem 0;
                color: #1f2937;
                font-size: 1.1rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .form-row {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1rem;
            }

            .participants-preview {
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 1rem;
            }

            .preview-list {
                display: flex;
                flex-wrap: wrap;
                gap: 1rem;
            }

            .preview-item {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                background: white;
                padding: 0.5rem;
                border-radius: 6px;
                border: 1px solid #e5e7eb;
                font-size: 0.9rem;
            }

            .preview-avatar {
                width: 30px;
                height: 30px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-weight: 600;
                font-size: 0.8rem;
            }

            .config-warning {
                background: #fef3c7;
                border: 1px solid #f59e0b;
                border-radius: 8px;
                padding: 1rem;
                display: flex;
                gap: 1rem;
                margin-top: 1.5rem;
            }

            .warning-icon {
                color: #f59e0b;
                font-size: 1.2rem;
                flex-shrink: 0;
            }

            .warning-content h5 {
                margin: 0 0 0.5rem 0;
                color: #92400e;
            }

            .warning-content ul {
                margin: 0;
                padding-left: 1.5rem;
                color: #92400e;
            }

            .warning-content li {
                margin: 0.25rem 0;
                font-size: 0.9rem;
            }

            /* Seeding modal styles */
            .seeding-controls {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 2rem;
                padding: 1rem;
                background: #f8fafc;
                border-radius: 8px;
                border: 1px solid #e2e8f0;
            }

            .seeding-method-selector {
                display: flex;
                align-items: center;
                gap: 1rem;
            }

            .seeding-method-selector label {
                font-weight: 500;
                color: #374151;
            }

            .seeding-actions {
                display: flex;
                gap: 0.5rem;
            }

            .seeding-list {
                max-height: 400px;
                overflow-y: auto;
            }

            .sortable-list {
                display: grid;
                gap: 0.5rem;
            }

            .seeding-item {
                background: white;
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                display: flex;
                align-items: center;
                gap: 1rem;
                transition: all 0.3s ease;
            }

            .seeding-item:hover {
                border-color: #3b82f6;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .seeding-rank {
                width: 40px;
                height: 40px;
                background: #3b82f6;
                color: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 700;
                font-size: 1.1rem;
            }

            .seeding-avatar {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-weight: 600;
                font-size: 1rem;
            }

            .seeding-name {
                flex: 1;
                font-weight: 500;
                color: #1f2937;
            }

            .seeding-controls {
                display: flex;
                gap: 0.25rem;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>