<?php
/**
 * Comprehensive Category Management Page for SC_IMS Admin Panel
 * Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get parameters
$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;
$sport_id = isset($_GET['sport_id']) ? (int)$_GET['sport_id'] : 0;
$category_id = isset($_GET['category_id']) ? (int)$_GET['category_id'] : 0;

if (!$event_id || !$sport_id || !$category_id) {
    header('Location: events.php');
    exit;
}

// Get category details with event and sport information
$sql = "SELECT
            sc.*,
            es.id as event_sport_id,
            es.event_id,
            es.sport_id,
            es.tournament_format_id,
            es.max_teams,
            es.venue as event_venue,
            es.status as event_sport_status,
            e.name as event_name,
            e.start_date as event_start_date,
            e.end_date as event_end_date,
            s.name as sport_name,
            s.type as sport_type,
            s.scoring_method,
            s.bracket_format,
            st.name as sport_type_name,
            st.color_code,
            st.icon_class,
            tf.name as tournament_format_name,
            tf.description as tournament_format_description
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        LEFT JOIN sport_types st ON s.sport_type_id = st.id
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE sc.id = ? AND es.event_id = ? AND es.sport_id = ?";

$stmt = $conn->prepare($sql);
$stmt->execute([$category_id, $event_id, $sport_id]);
$category = $stmt->fetch();

if (!$category) {
    header('Location: sport-categories.php?event_id=' . $event_id . '&sport_id=' . $sport_id);
    exit;
}

// Get registration statistics from unified department registration system
$registration_stats = null;
try {
    // First try the new unified registration system
    $sql = "SELECT
                COUNT(DISTINCT dsp.id) as total_registrations,
                COUNT(DISTINCT CASE WHEN dsp.status = 'confirmed' THEN dsp.id END) as confirmed_registrations,
                COUNT(DISTINCT CASE WHEN dsp.status = 'registered' THEN dsp.id END) as pending_registrations,
                COUNT(DISTINCT edr.department_id) as total_departments
            FROM event_department_registrations edr
            JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
            WHERE dsp.event_sport_id = ? AND edr.status IN ('approved', 'pending')";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$category['event_sport_id']]);
    $registration_stats = $stmt->fetch();
} catch (Exception $e) {
    // Tables don't exist, continue with fallback
}

// Fallback to old registrations table if new tables don't exist or have no data
if (!$registration_stats || $registration_stats['total_registrations'] == 0) {
    $sql = "SELECT
                COUNT(r.id) as total_registrations,
                COUNT(CASE WHEN r.status = 'confirmed' THEN 1 END) as confirmed_registrations,
                COUNT(CASE WHEN r.status = 'registered' THEN 1 END) as pending_registrations,
                COUNT(DISTINCT r.department_id) as total_departments
            FROM registrations r
            WHERE r.event_sport_id = ?";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$category['event_sport_id']]);
    $registration_stats = $stmt->fetch();
}

// Get tournament structure if exists (check if table exists first)
$tournament = null;
try {
    $stmt = $conn->prepare("SHOW TABLES LIKE 'tournament_structures'");
    $stmt->execute();
    $table_exists = $stmt->fetch();

    if ($table_exists) {
        $sql = "SELECT ts.*, tf.name as format_name, tf.description as format_description
                FROM tournament_structures ts
                LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
                WHERE ts.event_sport_id = ?
                ORDER BY ts.created_at DESC
                LIMIT 1";

        $stmt = $conn->prepare($sql);
        $stmt->execute([$category['event_sport_id']]);
        $tournament = $stmt->fetch();
    }
} catch (Exception $e) {
    // Table doesn't exist, continue without tournament data
    $tournament = null;
}

// Get the tournament format for display
$display_format_name = 'Single Elimination'; // Default fallback

if (!empty($category['tournament_format_name'])) {
    // Use the format from the database
    $display_format_name = $category['tournament_format_name'];
} else {
    // Include tournament format detection helper for fallback
    require_once __DIR__ . '/../includes/tournament_format_helper.php';

    // Get the display format (existing tournament or default)
    $display_format_name = getTournamentFormatDisplay($conn, $category['event_sport_id']);
}

// Get matches for this category
$sql = "SELECT 
            m.*,
            r1.team_name as team1_name,
            r1.department_id as team1_dept_id,
            d1.name as team1_dept_name,
            r2.team_name as team2_name,
            r2.department_id as team2_dept_id,
            d2.name as team2_dept_name,
            rw.team_name as winner_name,
            rw.department_id as winner_dept_id,
            dw.name as winner_dept_name,
            s.team1_score,
            s.team2_score
        FROM matches m
        LEFT JOIN registrations r1 ON m.team1_id = r1.id
        LEFT JOIN departments d1 ON r1.department_id = d1.id
        LEFT JOIN registrations r2 ON m.team2_id = r2.id
        LEFT JOIN departments d2 ON r2.department_id = d2.id
        LEFT JOIN registrations rw ON m.winner_id = rw.id
        LEFT JOIN departments dw ON rw.department_id = dw.id
        LEFT JOIN scores s ON m.id = s.match_id AND s.is_final = 1
        WHERE m.event_sport_id = ?
        ORDER BY m.round_number ASC, m.match_number ASC";

$stmt = $conn->prepare($sql);
$stmt->execute([$category['event_sport_id']]);
$matches = $stmt->fetchAll();

// Get current standings/rankings - try unified system first, then fallback
$standings = [];
try {
    // Try unified department registration system first
    $sql = "SELECT
                standings.*,
                (standings.total_points_scored - standings.total_points_conceded) as point_difference
            FROM (
                SELECT
                    dsp.id,
                    COALESCE(dsp.team_name, d.name) as team_name,
                    edr.department_id,
                    d.name as department_name,
                    COUNT(DISTINCT m1.id) as matches_played,
                    COUNT(DISTINCT CASE WHEN m1.winner_id = r.id THEN m1.id END) as wins,
                    COUNT(DISTINCT CASE WHEN m1.status = 'completed' AND m1.winner_id != r.id AND m1.winner_id IS NOT NULL THEN m1.id END) as losses,
                    COUNT(DISTINCT CASE WHEN m1.status = 'completed' AND m1.winner_id IS NULL THEN m1.id END) as draws,
                    COALESCE(SUM(CASE WHEN s.team1_score IS NOT NULL AND m1.team1_id = r.id THEN s.team1_score
                                     WHEN s.team2_score IS NOT NULL AND m1.team2_id = r.id THEN s.team2_score
                                     ELSE 0 END), 0) as total_points_scored,
                    COALESCE(SUM(CASE WHEN s.team1_score IS NOT NULL AND m1.team2_id = r.id THEN s.team1_score
                                     WHEN s.team2_score IS NOT NULL AND m1.team1_id = r.id THEN s.team2_score
                                     ELSE 0 END), 0) as total_points_conceded
                FROM event_department_registrations edr
                JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
                JOIN departments d ON edr.department_id = d.id
                LEFT JOIN registrations r ON dsp.id = r.department_sport_participation_id
                LEFT JOIN matches m1 ON (m1.team1_id = r.id OR m1.team2_id = r.id) AND m1.event_sport_id = dsp.event_sport_id
                LEFT JOIN scores s ON m1.id = s.match_id AND s.is_final = 1
                WHERE dsp.event_sport_id = ? AND edr.status IN ('approved', 'pending')
                GROUP BY dsp.id, dsp.team_name, edr.department_id, d.name
            ) as standings
            ORDER BY standings.wins DESC, point_difference DESC, standings.total_points_scored DESC";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$category['event_sport_id']]);
    $standings = $stmt->fetchAll();
} catch (Exception $e) {
    // Tables don't exist, continue with fallback
}

// Fallback to old registrations table if new tables don't exist or have no data
if (empty($standings)) {
    $sql = "SELECT
                standings.*,
                (standings.total_points_scored - standings.total_points_conceded) as point_difference
            FROM (
                SELECT
                    r.id,
                    COALESCE(r.team_name, d.name) as team_name,
                    r.department_id,
                    d.name as department_name,
                    COUNT(DISTINCT m1.id) as matches_played,
                    COUNT(DISTINCT CASE WHEN m1.winner_id = r.id THEN m1.id END) as wins,
                    COUNT(DISTINCT CASE WHEN m1.status = 'completed' AND m1.winner_id != r.id AND m1.winner_id IS NOT NULL THEN m1.id END) as losses,
                    COUNT(DISTINCT CASE WHEN m1.status = 'completed' AND m1.winner_id IS NULL THEN m1.id END) as draws,
                    COALESCE(SUM(CASE WHEN s.team1_score IS NOT NULL AND m1.team1_id = r.id THEN s.team1_score
                                     WHEN s.team2_score IS NOT NULL AND m1.team2_id = r.id THEN s.team2_score
                                     ELSE 0 END), 0) as total_points_scored,
                    COALESCE(SUM(CASE WHEN s.team1_score IS NOT NULL AND m1.team2_id = r.id THEN s.team1_score
                                     WHEN s.team2_score IS NOT NULL AND m1.team1_id = r.id THEN s.team2_score
                                     ELSE 0 END), 0) as total_points_conceded
                FROM registrations r
                JOIN departments d ON r.department_id = d.id
                LEFT JOIN matches m1 ON (m1.team1_id = r.id OR m1.team2_id = r.id) AND m1.event_sport_id = r.event_sport_id
                LEFT JOIN scores s ON m1.id = s.match_id AND s.is_final = 1
                WHERE r.event_sport_id = ?
                GROUP BY r.id, r.team_name, r.department_id, d.name
            ) as standings
            ORDER BY standings.wins DESC, point_difference DESC, standings.total_points_scored DESC";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$category['event_sport_id']]);
    $standings = $stmt->fetchAll();
}

// Get scoring system for department points (check if table exists first)
$scoring_system = null;
try {
    $stmt = $conn->prepare("SHOW TABLES LIKE 'scoring_systems'");
    $stmt->execute();
    $table_exists = $stmt->fetch();

    if ($table_exists) {
        $sql = "SELECT * FROM scoring_systems WHERE event_id = ? AND is_active = 1 LIMIT 1";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$event_id]);
        $scoring_system = $stmt->fetch();
    }
} catch (Exception $e) {
    // Table doesn't exist, create a default scoring system
    $scoring_system = [
        'name' => 'Default Scoring System',
        'description' => 'Standard point-based scoring: 1st=10pts, 2nd=8pts, 3rd=6pts, 4th=4pts, 5th=2pts, participation=1pt',
        'position_points' => '{"1": 10, "2": 8, "3": 6, "4": 4, "5": 2, "participation": 1}',
        'participation_bonus' => 1.0,
        'winner_bonus' => 2.0
    ];
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($category['category_name']); ?> - <?php echo htmlspecialchars($category['sport_name']); ?> | SC_IMS Admin</title>
    <?php include 'includes/admin-styles.php'; ?>
    <style>
        /* Modern Category Management Styles */
        .category-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2.5rem;
            border-radius: 16px;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .category-hero::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(50px, -50px);
        }

        .category-header {
            position: relative;
            z-index: 2;
        }

        .category-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .category-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-left: 1rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4CAF50;
        }

        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.15);
            padding: 1.5rem;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 2.2rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            display: block;
        }

        .stat-label {
            font-size: 0.95rem;
            opacity: 0.9;
            font-weight: 500;
        }

        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            padding: 6px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-x: auto;
        }

        .nav-tab {
            flex: 1;
            padding: 14px 24px;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            white-space: nowrap;
            min-width: 140px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            color: #6c757d;
        }

        .tab-button.active {
            background: #007bff;
            color: white;
            box-shadow: 0 2px 4px rgba(0,123,255,0.3);
        }

        .tab-button:hover:not(.active) {
            background: #e9ecef;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .matches-grid {
            display: grid;
            gap: 1rem;
        }

        .match-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            transition: all 0.2s ease;
        }

        .match-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .match-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .match-teams {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex: 1;
        }

        .team-info {
            flex: 1;
            text-align: center;
        }

        .vs-divider {
            font-weight: 700;
            color: #6c757d;
        }

        .standings-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .standings-header {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
            display: grid;
            grid-template-columns: 50px 1fr 80px 80px 80px 100px 100px;
            gap: 1rem;
            align-items: center;
        }

        .standings-row {
            padding: 1rem;
            border-bottom: 1px solid #f1f3f4;
            display: grid;
            grid-template-columns: 50px 1fr 80px 80px 80px 100px 100px;
            gap: 1rem;
            align-items: center;
            transition: background-color 0.2s ease;
        }

        .standings-row:hover {
            background-color: #f8f9fa;
        }

        .rank-badge {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            color: white;
        }

        .rank-1 { background: #ffd700; color: #333; }
        .rank-2 { background: #c0c0c0; color: #333; }
        .rank-3 { background: #cd7f32; color: white; }
        .rank-other { background: #6c757d; }

        /* Responsive Design */
        @media (max-width: 768px) {
            .category-title {
                font-size: 2rem;
            }

            .quick-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .nav-tabs {
                flex-direction: column;
            }

            .nav-tab {
                min-width: auto;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            .standings-header,
            .standings-row {
                grid-template-columns: 40px 1fr 60px 60px 60px;
                gap: 0.5rem;
                font-size: 0.9rem;
            }

            .standings-header .hide-mobile,
            .standings-row .hide-mobile {
                display: none;
            }

            .action-buttons {
                flex-direction: column;
            }

            .btn {
                justify-content: center;
            }
        }

        /* Loading States */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .loading .btn {
            position: relative;
        }

        .loading .btn::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            margin: auto;
            border: 2px solid transparent;
            border-top-color: #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* Tooltips */
        .tooltip {
            position: relative;
            cursor: help;
        }

        .tooltip::before {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s;
            z-index: 1000;
        }

        .tooltip:hover::before {
            opacity: 1;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content Area -->
    <div class="admin-main">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="breadcrumb">
                    <div class="breadcrumb-item">
                        <a href="events.php"><i class="fas fa-calendar"></i> Events</a>
                    </div>
                    <div class="breadcrumb-separator">></div>
                    <div class="breadcrumb-item">
                        <a href="manage-event.php?id=<?php echo $event_id; ?>"><?php echo htmlspecialchars($category['event_name']); ?></a>
                    </div>
                    <div class="breadcrumb-separator">></div>
                    <div class="breadcrumb-item">
                        <a href="sport-categories.php?event_id=<?php echo $event_id; ?>&sport_id=<?php echo $sport_id; ?>"><?php echo htmlspecialchars($category['sport_name']); ?> Categories</a>
                    </div>
                    <div class="breadcrumb-separator">></div>
                    <div class="breadcrumb-item active">
                        <i class="fas fa-layer-group"></i>
                        <span><?php echo htmlspecialchars($category['category_name']); ?></span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span class="user-name">
                        <i class="fas fa-user-shield"></i>
                        <?php echo htmlspecialchars($current_admin['username']); ?>
                    </span>
                    <a href="logout.php" class="logout-btn" title="Logout">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Category Hero Section -->
            <div class="category-hero">
                <div class="category-header">
                    <div class="category-title">
                        <?php if ($category['sport_type_name']): ?>
                            <i class="<?php echo $category['icon_class'] ?? 'fas fa-layer-group'; ?>"></i>
                        <?php else: ?>
                            <i class="fas fa-layer-group"></i>
                        <?php endif; ?>
                        <?php echo htmlspecialchars($category['category_name']); ?>
                        <div class="status-indicator">
                            <div class="status-dot"></div>
                            <?php echo ucfirst($category['status']); ?>
                        </div>
                    </div>
                    <div class="category-subtitle">
                        <i class="fas fa-trophy"></i>
                        <?php echo htmlspecialchars($category['sport_name']); ?>
                        <span>•</span>
                        <i class="fas fa-calendar"></i>
                        <?php echo htmlspecialchars($category['event_name']); ?>
                    </div>

                    <div class="quick-stats">
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $registration_stats['total_registrations'] ?? 0; ?></div>
                            <div class="stat-label">Registered Teams</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo count($matches); ?></div>
                            <div class="stat-label">Total Matches</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">
                                <?php
                                $max = $category['max_participants'] ?: 'Unlimited';
                                echo $max === 'Unlimited' ? '∞' : $max;
                                ?>
                            </div>
                            <div class="stat-label">Max Teams</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">
                                <?php echo htmlspecialchars($display_format_name); ?>
                            </div>
                            <div class="stat-label">Tournament Format</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Tabs -->
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="showTab('overview')">
                    <i class="fas fa-info-circle"></i>
                    <span>Overview</span>
                </button>
                <button class="nav-tab" onclick="showTab('tournament')">
                    <i class="fas fa-sitemap"></i>
                    <span>Tournament</span>
                </button>
                <button class="nav-tab" onclick="showTab('matches')">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Matches</span>
                </button>
                <button class="nav-tab" onclick="showTab('standings')">
                    <i class="fas fa-trophy"></i>
                    <span>Rankings</span>
                </button>
                <button class="nav-tab" onclick="showTab('settings')">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </button>
            </div>

            <!-- Tab Content Sections -->

            <!-- Overview Tab -->
            <div id="overview-tab" class="tab-content active">
                <div class="info-grid">
                    <div class="info-section">
                        <h4><i class="fas fa-info-circle"></i> Category Information</h4>
                        <div class="detail-item">
                            <span class="detail-label">Category Name</span>
                            <span class="detail-value"><?php echo htmlspecialchars($category['category_name']); ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Category Type</span>
                            <span class="detail-value">
                                <span class="badge badge-info">
                                    <?php
                                    if ($category['category_type'] === 'other' && !empty($category['category_type_custom'])) {
                                        echo htmlspecialchars($category['category_type_custom']);
                                    } else {
                                        echo ucfirst($category['category_type']);
                                    }
                                    ?>
                                </span>
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Max Participants</span>
                            <span class="detail-value"><?php echo $category['max_participants'] ?: 'Unlimited'; ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Current Status</span>
                            <span class="detail-value">
                                <span class="badge badge-success">
                                    <?php echo ucfirst($category['status']); ?>
                                </span>
                            </span>
                        </div>
                    </div>

                    <div class="info-section">
                        <h4><i class="fas fa-user-tie"></i> Management Details</h4>
                        <div class="detail-item">
                            <span class="detail-label">Referee</span>
                            <span class="detail-value"><?php echo htmlspecialchars($category['referee_name'] ?? 'Not assigned'); ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Referee Contact</span>
                            <span class="detail-value">
                                <?php if (!empty($category['referee_email'])): ?>
                                    <a href="mailto:<?php echo htmlspecialchars($category['referee_email']); ?>" class="text-primary">
                                        <?php echo htmlspecialchars($category['referee_email']); ?>
                                    </a>
                                <?php else: ?>
                                    Not provided
                                <?php endif; ?>
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Venue</span>
                            <span class="detail-value"><?php echo htmlspecialchars($category['venue'] ?? $category['event_venue'] ?? 'Not specified'); ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Registration Deadline</span>
                            <span class="detail-value">
                                <?php
                                if ($category['registration_deadline']) {
                                    echo date('M j, Y g:i A', strtotime($category['registration_deadline']));
                                } else {
                                    echo 'Not set';
                                }
                                ?>
                            </span>
                        </div>
                    </div>
                </div>

                <div class="content-card">
                    <div class="content-card-header">
                        <h3><i class="fas fa-chart-bar"></i> Registration Analytics</h3>
                    </div>
                    <div class="content-card-body">
                        <div class="quick-stats">
                            <div class="stat-card">
                                <div class="stat-number"><?php echo $registration_stats['total_registrations'] ?? 0; ?></div>
                                <div class="stat-label">Total Registrations</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number"><?php echo $registration_stats['confirmed_registrations'] ?? 0; ?></div>
                                <div class="stat-label">Confirmed</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number"><?php echo $registration_stats['pending_registrations'] ?? 0; ?></div>
                                <div class="stat-label">Pending</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number"><?php echo $registration_stats['total_departments'] ?? 0; ?></div>
                                <div class="stat-label">Departments</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">
                                    <?php
                                    $total = $registration_stats['total_registrations'] ?? 0;
                                    $max = $category['max_participants'];
                                    if ($max > 0 && $total > 0) {
                                        echo round(($total / $max) * 100) . '%';
                                    } else {
                                        echo $max > 0 ? '0%' : 'N/A';
                                    }
                                    ?>
                                </div>
                                <div class="stat-label">Capacity</div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <a href="registrations.php?event_sport_id=<?php echo $category['event_sport_id']; ?>" class="btn btn-primary">
                                <i class="fas fa-users"></i> Manage Registrations
                            </a>
                            <button class="btn btn-secondary" onclick="exportRegistrations()">
                                <i class="fas fa-download"></i> Export Data
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tournament Tab -->
            <div id="tournament-tab" class="tab-content">
                <div class="content-card">
                    <div class="content-card-header">
                        <h3><i class="fas fa-sitemap"></i> Tournament Management</h3>
                    </div>
                    <div class="content-card-body">
                        <?php if ($tournament): ?>
                            <div class="info-grid">
                                <div class="info-section">
                                    <h4><i class="fas fa-trophy"></i> Tournament Details</h4>
                                    <div class="detail-item">
                                        <span class="detail-label">Format</span>
                                        <span class="detail-value">
                                            <span class="badge badge-info"><?php echo htmlspecialchars($tournament['format_name']); ?></span>
                                        </span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Status</span>
                                        <span class="detail-value">
                                            <span class="badge badge-success"><?php echo ucfirst($tournament['status']); ?></span>
                                        </span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Progress</span>
                                        <span class="detail-value">Round <?php echo $tournament['current_round']; ?> of <?php echo $tournament['total_rounds']; ?></span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Participants</span>
                                        <span class="detail-value"><?php echo $tournament['total_participants'] ?? 0; ?> teams</span>
                                    </div>
                                </div>

                                <div class="info-section">
                                    <h4><i class="fas fa-cogs"></i> Tournament Actions</h4>
                                    <div class="action-buttons">
                                        <button class="btn btn-primary" onclick="viewBracket()">
                                            <i class="fas fa-eye"></i> View Bracket
                                        </button>
                                        <button class="btn btn-warning" onclick="manageTournament()">
                                            <i class="fas fa-edit"></i> Manage Tournament
                                        </button>
                                        <button class="btn btn-secondary" onclick="exportBracket()">
                                            <i class="fas fa-download"></i> Export Bracket
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>Tournament Active:</strong> <?php echo htmlspecialchars($tournament['format_description'] ?? 'Tournament is currently running.'); ?>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-sitemap fa-3x"></i>
                                <h4>No Tournament Created</h4>
                                <p>Set up a tournament bracket to organize matches and track progress.</p>

                                <?php
                                $total_teams = $registration_stats['total_registrations'] ?? 0;
                                $min_teams = 2;
                                ?>

                                <?php if ($total_teams >= $min_teams): ?>
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle"></i>
                                        <strong><?php echo $total_teams; ?> teams registered!</strong>
                                        Ready to create tournament bracket.
                                    </div>

                                    <div class="action-buttons">
                                        <button class="btn btn-primary" onclick="createTournament()" data-tooltip="Create tournament with current registrations">
                                            <i class="fas fa-plus"></i> Create Tournament
                                        </button>
                                        <button class="btn btn-secondary" onclick="previewTournament()" data-tooltip="Preview tournament structure">
                                            <i class="fas fa-eye"></i> Preview Structure
                                        </button>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        Need at least <?php echo $min_teams; ?> teams to create tournament.
                                        Currently have <?php echo $total_teams; ?> teams.
                                    </div>

                                    <div class="action-buttons">
                                        <button class="btn btn-secondary" disabled data-tooltip="Need more teams to create tournament">
                                            <i class="fas fa-plus"></i> Create Tournament
                                        </button>
                                        <a href="registrations.php?event_sport_id=<?php echo $category['event_sport_id']; ?>" class="btn btn-primary">
                                            <i class="fas fa-users"></i> Manage Registrations
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Matches Tab -->
            <div id="matches-tab" class="tab-content">
                <div class="content-card">
                    <div class="content-card-header">
                        <h3><i class="fas fa-calendar-alt"></i> Matches Management</h3>
                    </div>
                    <div class="content-card-body">
                        <?php if (!empty($matches)): ?>
                            <div class="matches-grid">
                                <?php foreach ($matches as $match): ?>
                                    <div class="match-card">
                                        <div class="match-header">
                                            <div class="match-info">
                                                <span class="match-round">Round <?php echo $match['round_number']; ?></span>
                                                <span class="match-number">Match #<?php echo $match['match_number']; ?></span>
                                            </div>
                                            <div class="match-status">
                                                <span class="status-badge status-<?php echo $match['status']; ?>">
                                                    <?php echo ucfirst($match['status']); ?>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="match-teams">
                                            <div class="team-info">
                                                <div class="team-name">
                                                    <?php echo htmlspecialchars($match['team1_name'] ?? $match['team1_dept_name'] ?? 'TBD'); ?>
                                                </div>
                                                <?php if ($match['status'] === 'completed' && isset($match['team1_score'])): ?>
                                                    <div class="team-score"><?php echo $match['team1_score']; ?></div>
                                                <?php endif; ?>
                                            </div>

                                            <div class="vs-divider">VS</div>

                                            <div class="team-info">
                                                <div class="team-name">
                                                    <?php echo htmlspecialchars($match['team2_name'] ?? $match['team2_dept_name'] ?? 'TBD'); ?>
                                                </div>
                                                <?php if ($match['status'] === 'completed' && isset($match['team2_score'])): ?>
                                                    <div class="team-score"><?php echo $match['team2_score']; ?></div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <?php if ($match['scheduled_time']): ?>
                                            <div class="match-time">
                                                <i class="fas fa-clock"></i>
                                                <?php echo date('M j, Y g:i A', strtotime($match['scheduled_time'])); ?>
                                            </div>
                                        <?php endif; ?>

                                        <?php if ($match['venue']): ?>
                                            <div class="match-venue">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <?php echo htmlspecialchars($match['venue']); ?>
                                            </div>
                                        <?php endif; ?>

                                        <div class="match-actions">
                                            <?php if ($match['status'] !== 'completed'): ?>
                                                <button class="btn btn-sm btn-primary" onclick="editMatch(<?php echo $match['id']; ?>)">
                                                    <i class="fas fa-edit"></i> Edit
                                                </button>
                                                <button class="btn btn-sm btn-success" onclick="recordScore(<?php echo $match['id']; ?>)">
                                                    <i class="fas fa-plus-circle"></i> Record Score
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-sm btn-info" onclick="viewMatch(<?php echo $match['id']; ?>)">
                                                    <i class="fas fa-eye"></i> View Details
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-calendar-alt fa-3x"></i>
                                <h4>No Matches Scheduled</h4>
                                <p>No matches have been scheduled for this category yet.</p>
                                <button class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Schedule First Match
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Rankings/Standings Tab -->
            <div id="standings-tab" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-trophy"></i> Current Rankings</h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($standings)): ?>
                            <div class="standings-table">
                                <div class="standings-header">
                                    <div>Rank</div>
                                    <div>Team</div>
                                    <div>Played</div>
                                    <div>Won</div>
                                    <div>Lost</div>
                                    <div class="hide-mobile">Points For</div>
                                    <div class="hide-mobile">Points Against</div>
                                </div>

                                <?php foreach ($standings as $index => $team): ?>
                                    <div class="standings-row">
                                        <div>
                                            <div class="rank-badge rank-<?php echo $index < 3 ? $index + 1 : 'other'; ?>">
                                                <?php echo $index + 1; ?>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="team-name"><?php echo htmlspecialchars($team['team_name'] ?? $team['department_name']); ?></div>
                                            <div class="department-name"><?php echo htmlspecialchars($team['department_name']); ?></div>
                                        </div>
                                        <div><?php echo $team['matches_played']; ?></div>
                                        <div><?php echo $team['wins']; ?></div>
                                        <div><?php echo $team['losses']; ?></div>
                                        <div class="hide-mobile"><?php echo $team['total_points_scored']; ?></div>
                                        <div class="hide-mobile"><?php echo $team['total_points_conceded']; ?></div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-trophy fa-3x"></i>
                                <h4>No Rankings Available</h4>
                                <p>Rankings will appear once matches have been completed.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div id="settings-tab" class="tab-content">
                <div class="content-card">
                    <div class="content-card-header">
                        <h3><i class="fas fa-cog"></i> Category Settings</h3>
                    </div>
                    <div class="content-card-body">
                        <div class="info-grid">
                            <div class="info-section">
                                <h4><i class="fas fa-edit"></i> Quick Actions</h4>
                                <div class="action-buttons">
                                    <button class="btn btn-primary" onclick="editCategory()">
                                        <i class="fas fa-edit"></i> Edit Category
                                    </button>
                                    <button class="btn btn-warning" onclick="resetTournament()">
                                        <i class="fas fa-redo"></i> Reset Tournament
                                    </button>
                                    <button class="btn btn-secondary" onclick="duplicateCategory()">
                                        <i class="fas fa-copy"></i> Duplicate Category
                                    </button>
                                </div>
                            </div>

                            <div class="info-section">
                                <h4><i class="fas fa-download"></i> Export Options</h4>
                                <div class="action-buttons">
                                    <button class="btn btn-success" onclick="exportRegistrations()">
                                        <i class="fas fa-users"></i> Export Registrations
                                    </button>
                                    <button class="btn btn-success" onclick="exportMatches()">
                                        <i class="fas fa-calendar"></i> Export Matches
                                    </button>
                                    <button class="btn btn-success" onclick="exportStandings()">
                                        <i class="fas fa-trophy"></i> Export Standings
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-keyboard"></i>
                            <strong>Keyboard Shortcuts:</strong>
                            <br>Ctrl+1-5: Switch between tabs
                            <br>Use these shortcuts for faster navigation!
                        </div>
                    </div>
                </div>
            </div>

            <!-- Department Points Tab (Legacy) -->
            <div id="points-tab" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-calculator"></i> Department Points Calculation</h3>
                    </div>
                    <div class="card-body">
                        <?php if ($scoring_system): ?>
                            <div class="scoring-system-info">
                                <h4><?php echo htmlspecialchars($scoring_system['name']); ?></h4>
                                <p><?php echo htmlspecialchars($scoring_system['description']); ?></p>

                                <div class="points-breakdown">
                                    <h5>Point Distribution:</h5>
                                    <?php
                                    $position_points = json_decode($scoring_system['position_points'], true);
                                    if ($position_points):
                                    ?>
                                        <div class="points-grid">
                                            <?php foreach ($position_points as $position => $points): ?>
                                                <div class="point-item">
                                                    <span class="position">
                                                        <?php
                                                        if ($position === 'participation') {
                                                            echo 'Participation';
                                                        } else {
                                                            $suffix = 'th';
                                                            if ($position == 1) $suffix = 'st';
                                                            elseif ($position == 2) $suffix = 'nd';
                                                            elseif ($position == 3) $suffix = 'rd';
                                                            echo $position . $suffix . ' Place';
                                                        }
                                                        ?>
                                                    </span>
                                                    <span class="points"><?php echo $points; ?> pts</span>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($scoring_system['participation_bonus'] > 0): ?>
                                        <div class="bonus-info">
                                            <strong>Participation Bonus:</strong> <?php echo $scoring_system['participation_bonus']; ?> points
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($scoring_system['winner_bonus'] > 0): ?>
                                        <div class="bonus-info">
                                            <strong>Winner Bonus:</strong> <?php echo $scoring_system['winner_bonus']; ?> additional points for 1st place
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Current Points Calculation -->
                            <div class="current-points">
                                <h5>Current Department Points (Projected):</h5>
                                <div class="points-table">
                                    <?php if (!empty($standings)): ?>
                                        <div class="points-header">
                                            <div>Rank</div>
                                            <div>Department</div>
                                            <div>Position Points</div>
                                            <div>Bonus Points</div>
                                            <div>Total Points</div>
                                        </div>

                                        <?php foreach ($standings as $index => $team): ?>
                                            <?php
                                            $position = $index + 1;
                                            $position_points_earned = isset($position_points[$position]) ? $position_points[$position] : 0;
                                            $participation_bonus = $scoring_system['participation_bonus'];
                                            $winner_bonus = ($position === 1) ? $scoring_system['winner_bonus'] : 0;
                                            $total_points = $position_points_earned + $participation_bonus + $winner_bonus;
                                            ?>
                                            <div class="points-row">
                                                <div>
                                                    <div class="rank-badge rank-<?php echo $position <= 3 ? $position : 'other'; ?>">
                                                        <?php echo $position; ?>
                                                    </div>
                                                </div>
                                                <div><?php echo htmlspecialchars($team['department_name']); ?></div>
                                                <div><?php echo $position_points_earned; ?></div>
                                                <div><?php echo $participation_bonus + $winner_bonus; ?></div>
                                                <div><strong><?php echo $total_points; ?></strong></div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <p>Points will be calculated once tournament results are available.</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-calculator fa-3x"></i>
                                <h4>No Scoring System</h4>
                                <p>Department points scoring system has not been configured for this event.</p>
                                <button class="btn btn-primary">
                                    <i class="fas fa-cog"></i> Configure Scoring
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Additional CSS for category management -->
    <style>
        /* Detail groups */
        .detail-group {
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .detail-group label {
            font-weight: 600;
            min-width: 140px;
            color: #495057;
        }

        .detail-group span {
            flex: 1;
        }

        /* Registration stats grid */
        .registration-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .stat-content .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
        }

        .stat-content .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
        }

        /* Empty states */
        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #6c757d;
        }

        .empty-state i {
            color: #dee2e6;
            margin-bottom: 1rem;
        }

        .empty-state h4 {
            margin-bottom: 0.5rem;
            color: #495057;
        }

        /* Bracket placeholder */
        .bracket-placeholder {
            text-align: center;
            padding: 3rem;
            background: #f8f9fa;
            border-radius: 8px;
            margin-top: 2rem;
        }

        .bracket-placeholder i {
            color: #007bff;
            margin-bottom: 1rem;
        }

        /* Tournament info */
        .tournament-info {
            background: #e3f2fd;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }

        .tournament-status {
            display: flex;
            gap: 1rem;
            align-items: center;
            margin-top: 1rem;
        }

        .tournament-progress {
            background: rgba(255,255,255,0.8);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* Points grid */
        .points-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .point-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 1rem;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }

        .point-item .position {
            font-weight: 500;
        }

        .point-item .points {
            font-weight: 700;
            color: #007bff;
        }

        /* Points table */
        .points-table {
            margin-top: 1rem;
        }

        .points-header {
            display: grid;
            grid-template-columns: 60px 1fr 120px 120px 120px;
            gap: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px 8px 0 0;
            font-weight: 600;
        }

        .points-row {
            display: grid;
            grid-template-columns: 60px 1fr 120px 120px 120px;
            gap: 1rem;
            padding: 1rem;
            border-bottom: 1px solid #f1f3f4;
            align-items: center;
        }

        .points-row:hover {
            background-color: #f8f9fa;
        }

        /* Bonus info */
        .bonus-info {
            margin: 0.5rem 0;
            padding: 0.5rem 1rem;
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            border-radius: 4px;
        }

        /* Status badges */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-registration { background: #cce5ff; color: #0066cc; }
        .status-ongoing { background: #fff3cd; color: #856404; }
        .status-completed { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        .status-scheduled { background: #e2e3e5; color: #383d41; }
        .status-in_progress { background: #fff3cd; color: #856404; }

        /* Badge styles */
        .badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .badge-men { background: #007bff; color: white; }
        .badge-women { background: #e83e8c; color: white; }
        .badge-mixed { background: #6f42c1; color: white; }
        .badge-open { background: #28a745; color: white; }
        .badge-youth { background: #ffc107; color: #212529; }
        .badge-senior { background: #6c757d; color: white; }
        .badge-other { background: #17a2b8; color: white; }

        /* Alert styles */
        .alert {
            padding: 0.75rem 1rem;
            border-radius: 6px;
            border: 1px solid transparent;
            font-size: 0.9rem;
        }

        .alert-info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }

        .alert-warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }

        .alert i {
            margin-right: 0.5rem;
        }
    </style>

    <!-- Enhanced JavaScript for modern functionality -->
    <script>
        // Enhanced tab functionality with animations and state management
        function showTab(tabName) {
            // Hide all tab contents with fade effect
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all nav tabs
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content with animation
            const selectedTab = document.getElementById(tabName + '-tab');
            if (selectedTab) {
                setTimeout(() => {
                    selectedTab.classList.add('active');
                }, 50);
            }

            // Add active class to clicked button
            const clickedButton = event.target.closest('.nav-tab');
            if (clickedButton) {
                clickedButton.classList.add('active');
            }

            // Update URL hash for bookmarking
            history.replaceState(null, null, '#' + tabName);
        }

        // Load tab from URL hash on page load
        function loadTabFromHash() {
            const hash = window.location.hash.substring(1);
            if (hash && document.getElementById(hash + '-tab')) {
                showTab(hash);
            }
        }

        // Match management functions (placeholders)
        function editMatch(matchId) {
            alert('Edit match functionality will be implemented');
        }

        function recordScore(matchId) {
            alert('Record score functionality will be implemented');
        }

        function viewMatch(matchId) {
            alert('View match details functionality will be implemented');
        }

        // Tournament management functions
        function createTournament() {
            if (confirm('Create tournament bracket for this category? This will automatically generate matches based on registered teams.')) {

                // Show loading state
                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Tournament...';
                button.disabled = true;

                // Prepare form data
                const formData = new FormData();
                formData.append('event_sport_id', '<?php echo $category['event_sport_id']; ?>');
                formData.append('tournament_name', '<?php echo $category['sport_name'] . ' - ' . $category['category_name']; ?> Tournament');
                formData.append('format_id', '<?php echo $default_format_id; ?>'); // Dynamic format ID based on sport type
                formData.append('seeding_method', 'random');

                // Make AJAX request
                fetch('ajax/create-tournament.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Tournament created successfully!\n\n' +
                              'Tournament: ' + data.tournament_name + '\n' +
                              'Participants: ' + data.participants_count + '\n' +
                              'Format: ' + data.format_name);

                        // Reload the page to show the new tournament
                        window.location.reload();
                    } else {
                        alert('Error creating tournament: ' + data.message);

                        // Restore button state
                        button.innerHTML = originalText;
                        button.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while creating the tournament. Please try again.');

                    // Restore button state
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
            }
        }

        // Additional tournament management functions
        function previewTournament() {
            showNotification('Tournament preview feature coming soon!', 'info');
        }

        function viewBracket() {
            window.open('tournament-bracket.php?category_id=<?php echo $category_id; ?>', '_blank');
        }

        function manageTournament() {
            window.location.href = 'tournament-management.php?category_id=<?php echo $category_id; ?>';
        }

        function exportBracket() {
            window.open('export-bracket.php?category_id=<?php echo $category_id; ?>&format=pdf', '_blank');
        }

        function exportRegistrations() {
            window.open('export-registrations.php?category_id=<?php echo $category_id; ?>&format=excel', '_blank');
        }

        function exportMatches() {
            window.open('export-matches.php?category_id=<?php echo $category_id; ?>&format=excel', '_blank');
        }

        function exportStandings() {
            window.open('export-standings.php?category_id=<?php echo $category_id; ?>&format=pdf', '_blank');
        }

        function editCategory() {
            window.location.href = 'edit-category.php?id=<?php echo $category_id; ?>';
        }

        function resetTournament() {
            if (confirm('Are you sure you want to reset the tournament? This will delete all matches and bracket data.')) {
                showNotification('Reset tournament feature coming soon!', 'warning');
            }
        }

        function duplicateCategory() {
            if (confirm('Create a copy of this category?')) {
                showNotification('Duplicate category feature coming soon!', 'info');
            }
        }

        // UI Helper Functions
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type}`;
            notification.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 10000;
                min-width: 300px; animation: slideIn 0.3s ease;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                ${message}
                <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; font-size: 1.2rem; cursor: pointer;">&times;</button>
            `;

            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOut 0.3s ease';
                    setTimeout(() => notification.remove(), 300);
                }
            }, 5000);
        }

        // Initialize page with enhanced functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Enhanced category management page loaded');

            // Load tab from URL hash
            loadTabFromHash();

            // Set up sidebar toggle
            const sidebarToggle = document.getElementById('sidebarToggle');
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    document.body.classList.toggle('sidebar-collapsed');
                });
            }

            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case '1':
                            e.preventDefault();
                            showTab('overview');
                            break;
                        case '2':
                            e.preventDefault();
                            showTab('tournament');
                            break;
                        case '3':
                            e.preventDefault();
                            showTab('matches');
                            break;
                        case '4':
                            e.preventDefault();
                            showTab('standings');
                            break;
                        case '5':
                            e.preventDefault();
                            showTab('settings');
                            break;
                    }
                }
            });

            // Add CSS animations
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOut {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        });
    </script>

    <?php include 'includes/admin-scripts.php'; ?>
</body>
</html>
