<?php
/**
 * Sport Category Management Page for SC_IMS Admin Panel
 * Dedicated interface for managing sport category settings and administration
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get URL parameters
$event_id = $_GET['event_id'] ?? null;
$sport_id = $_GET['sport_id'] ?? null;
$category_id = $_GET['category_id'] ?? null;

// Validate required parameters
if (!$event_id || !$sport_id || !$category_id) {
    header('Location: events.php');
    exit;
}

// Get category information with related data
try {
    $sql = "SELECT
                sc.id as category_id,
                sc.category_name,
                sc.category_type,
                sc.referee_name,
                sc.referee_email,
                sc.venue,
                sc.max_participants,
                sc.status as category_status,
                es.id as event_sport_id,
                es.status as event_sport_status,
                s.id as sport_id,
                s.name as sport_name,
                s.type as sport_type,
                s.scoring_method,
                st.name as sport_type_name,
                st.category as sport_type_category,
                st.icon_class,
                e.id as event_id,
                e.name as event_name,
                e.status as event_status,
                e.start_date,
                e.end_date
            FROM sport_categories sc
            JOIN event_sports es ON sc.event_sport_id = es.id
            JOIN sports s ON es.sport_id = s.id
            LEFT JOIN sport_types st ON s.sport_type_id = st.id
            JOIN events e ON es.event_id = e.id
            WHERE sc.id = ? AND es.event_id = ? AND s.id = ?";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$category_id, $event_id, $sport_id]);
    $category = $stmt->fetch();

    if (!$category) {
        header('Location: events.php');
        exit;
    }
} catch (Exception $e) {
    error_log("Error fetching category data: " . $e->getMessage());
    header('Location: events.php');
    exit;
}

// Get registrations for this category
$registrations = [];
try {
    $sql = "SELECT
                r.id as registration_id,
                r.department_id,
                d.name as department_name,
                d.abbreviation as department_abbr,
                d.color_code,
                r.team_name,
                r.participants,
                r.status as registration_status,
                r.registration_date
            FROM registrations r
            JOIN departments d ON r.department_id = d.id
            WHERE r.event_sport_id = ?
            ORDER BY d.name";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$category['event_sport_id']]);
    $registrations = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error fetching registrations: " . $e->getMessage());
}

// Get matches for this category
$matches = [];
try {
    $sql = "SELECT
                m.*,
                r1.team_name as team1_name,
                r1.department_id as team1_dept_id,
                d1.name as team1_dept_name,
                d1.abbreviation as team1_abbr,
                d1.color_code as team1_color,
                r2.team_name as team2_name,
                r2.department_id as team2_dept_id,
                d2.name as team2_dept_name,
                d2.abbreviation as team2_abbr,
                d2.color_code as team2_color
            FROM matches m
            LEFT JOIN registrations r1 ON m.team1_id = r1.id
            LEFT JOIN departments d1 ON r1.department_id = d1.id
            LEFT JOIN registrations r2 ON m.team2_id = r2.id
            LEFT JOIN departments d2 ON r2.department_id = d2.id
            WHERE m.event_sport_id = ?
            ORDER BY m.scheduled_time ASC, m.round_number ASC, m.match_number ASC";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$category['event_sport_id']]);
    $matches = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error fetching matches: " . $e->getMessage());
}

// Calculate category statistics
$category_stats = [
    'total_registrations' => count($registrations),
    'confirmed_registrations' => count(array_filter($registrations, fn($r) => $r['registration_status'] === 'confirmed')),
    'total_matches' => count($matches),
    'completed_matches' => count(array_filter($matches, fn($m) => $m['status'] === 'completed')),
    'scheduled_matches' => count(array_filter($matches, fn($m) => $m['status'] === 'scheduled')),
    'ongoing_matches' => count(array_filter($matches, fn($m) => $m['status'] === 'ongoing'))
];

// Calculate standings from match results
$standings = [];
$team_stats = [];

// Initialize team stats from registrations
foreach ($registrations as $registration) {
    if ($registration['registration_status'] === 'confirmed') {
        $team_stats[$registration['registration_id']] = [
            'registration_id' => $registration['registration_id'],
            'team_name' => $registration['team_name'] ?: $registration['department_name'],
            'department_name' => $registration['department_name'],
            'department_abbr' => $registration['department_abbr'],
            'color_code' => $registration['color_code'],
            'wins' => 0,
            'losses' => 0,
            'draws' => 0,
            'points' => 0,
            'matches_played' => 0
        ];
    }
}

// Calculate stats from completed matches
foreach ($matches as $match) {
    if ($match['status'] === 'completed' && $match['winner_id']) {
        // Update winner stats
        if (isset($team_stats[$match['team1_id']])) {
            $team_stats[$match['team1_id']]['matches_played']++;
            if ($match['winner_id'] == $match['team1_id']) {
                $team_stats[$match['team1_id']]['wins']++;
                $team_stats[$match['team1_id']]['points'] += 3;
            } else {
                $team_stats[$match['team1_id']]['losses']++;
            }
        }
        
        if (isset($team_stats[$match['team2_id']])) {
            $team_stats[$match['team2_id']]['matches_played']++;
            if ($match['winner_id'] == $match['team2_id']) {
                $team_stats[$match['team2_id']]['wins']++;
                $team_stats[$match['team2_id']]['points'] += 3;
            } else {
                $team_stats[$match['team2_id']]['losses']++;
            }
        }
    }
}

// Sort standings by points, then wins
$standings = array_values($team_stats);
usort($standings, function($a, $b) {
    if ($a['points'] != $b['points']) {
        return $b['points'] <=> $a['points'];
    }
    if ($a['wins'] != $b['wins']) {
        return $b['wins'] <=> $a['wins'];
    }
    return $a['losses'] <=> $b['losses'];
});

// Add rank to standings
foreach ($standings as $index => &$standing) {
    $standing['rank'] = $index + 1;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($category['category_name']); ?> - Category Management | SC_IMS Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Header */
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            opacity: 0.9;
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .breadcrumb {
            background: rgba(255, 255, 255, 0.1);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .status-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            text-transform: uppercase;
            font-weight: 500;
        }

        /* Tabs */
        .tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .tab-button {
            flex: 1;
            padding: 1rem 2rem;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .tab-button:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .tab-button.active {
            background: #3b82f6;
            color: white;
        }

        /* Tab Content */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Cards */
        .card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        /* Form Styles */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        /* Button Styles */
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        /* Statistics Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            text-align: center;
            padding: 1.5rem;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #6b7280;
            font-size: 0.9rem;
        }

        /* Table Styles */
        .table-container {
            overflow-x: auto;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .table th,
        .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .tabs {
                flex-direction: column;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .page-title {
                font-size: 1.5rem;
            }

            .page-subtitle {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title"><?php echo htmlspecialchars($category['category_name']); ?></h1>
            <div class="page-subtitle">
                <div class="breadcrumb">
                    <i class="fas fa-home"></i>
                    <a href="events.php" style="color: inherit; text-decoration: none;">Events</a>
                    <i class="fas fa-chevron-right" style="margin: 0 0.5rem;"></i>
                    <a href="manage-event.php?event_id=<?php echo $event_id; ?>" style="color: inherit; text-decoration: none;">
                        <?php echo htmlspecialchars($category['event_name']); ?>
                    </a>
                    <i class="fas fa-chevron-right" style="margin: 0 0.5rem;"></i>
                    <?php echo htmlspecialchars($category['sport_name']); ?>
                </div>
                <span><i class="fas fa-trophy"></i> <?php echo htmlspecialchars($category['sport_name']); ?></span>
                <span><i class="fas fa-calendar"></i> <?php echo htmlspecialchars($category['event_name']); ?></span>
                <span class="status-badge"><?php echo htmlspecialchars($category['category_status']); ?></span>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="tabs">
            <button class="tab-button active" onclick="switchTab('overview')" data-tab="overview">
                <i class="fas fa-cog"></i>
                Overview
            </button>
            <button class="tab-button" onclick="switchTab('fixtures')" data-tab="fixtures">
                <i class="fas fa-calendar-alt"></i>
                Fixtures
            </button>
            <button class="tab-button" onclick="switchTab('standings')" data-tab="standings">
                <i class="fas fa-trophy"></i>
                Standings
            </button>
        </div>

        <!-- Overview Tab -->
        <div id="overview-tab" class="tab-content active">
            <!-- Category Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" style="color: #3b82f6;"><?php echo $category_stats['total_registrations']; ?></div>
                    <div class="stat-label">Total Registrations</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #10b981;"><?php echo $category_stats['confirmed_registrations']; ?></div>
                    <div class="stat-label">Confirmed Teams</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #f59e0b;"><?php echo $category_stats['total_matches']; ?></div>
                    <div class="stat-label">Total Matches</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #8b5cf6;"><?php echo $category_stats['completed_matches']; ?></div>
                    <div class="stat-label">Completed</div>
                </div>
            </div>

            <!-- Category Settings -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-cog"></i> Category Settings
                    </h2>
                    <button type="button" class="btn btn-primary" onclick="saveCategorySettings()">
                        <i class="fas fa-save"></i> Save Changes
                    </button>
                </div>

                <form id="categorySettingsForm" class="form-grid">
                    <div>
                        <div class="form-group">
                            <label class="form-label" for="categoryName">Category Name</label>
                            <input type="text" id="categoryName" class="form-input"
                                   value="<?php echo htmlspecialchars($category['category_name']); ?>" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="categoryType">Category Type</label>
                            <select id="categoryType" class="form-select" required>
                                <option value="men" <?php echo $category['category_type'] === 'men' ? 'selected' : ''; ?>>Men</option>
                                <option value="women" <?php echo $category['category_type'] === 'women' ? 'selected' : ''; ?>>Women</option>
                                <option value="mixed" <?php echo $category['category_type'] === 'mixed' ? 'selected' : ''; ?>>Mixed</option>
                                <option value="open" <?php echo $category['category_type'] === 'open' ? 'selected' : ''; ?>>Open</option>
                                <option value="youth" <?php echo $category['category_type'] === 'youth' ? 'selected' : ''; ?>>Youth</option>
                                <option value="senior" <?php echo $category['category_type'] === 'senior' ? 'selected' : ''; ?>>Senior</option>
                                <option value="other" <?php echo $category['category_type'] === 'other' ? 'selected' : ''; ?>>Other</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="maxParticipants">Maximum Participants</label>
                            <input type="number" id="maxParticipants" class="form-input"
                                   value="<?php echo $category['max_participants']; ?>" min="2" max="64">
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="categoryStatus">Category Status</label>
                            <select id="categoryStatus" class="form-select" required>
                                <option value="registration" <?php echo $category['category_status'] === 'registration' ? 'selected' : ''; ?>>Registration Open</option>
                                <option value="ongoing" <?php echo $category['category_status'] === 'ongoing' ? 'selected' : ''; ?>>Ongoing</option>
                                <option value="completed" <?php echo $category['category_status'] === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                <option value="cancelled" <?php echo $category['category_status'] === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                            </select>
                        </div>
                    </div>

                    <div>
                        <div class="form-group">
                            <label class="form-label" for="refereeName">Referee Name</label>
                            <input type="text" id="refereeName" class="form-input"
                                   value="<?php echo htmlspecialchars($category['referee_name'] ?? ''); ?>"
                                   placeholder="Enter referee name">
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="refereeEmail">Referee Email</label>
                            <input type="email" id="refereeEmail" class="form-input"
                                   value="<?php echo htmlspecialchars($category['referee_email'] ?? ''); ?>"
                                   placeholder="<EMAIL>">
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="venue">Venue</label>
                            <input type="text" id="venue" class="form-input"
                                   value="<?php echo htmlspecialchars($category['venue'] ?? ''); ?>"
                                   placeholder="Enter venue location">
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="registrationDeadline">Registration Deadline</label>
                            <input type="datetime-local" id="registrationDeadline" class="form-input"
                                   value="">
                        </div>
                    </div>
                </form>
            </div>

            <!-- Registered Teams -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-users"></i> Registered Teams
                    </h2>
                    <span class="btn btn-secondary btn-sm">
                        <?php echo count($registrations); ?> Teams
                    </span>
                </div>

                <?php if (empty($registrations)): ?>
                    <div style="text-align: center; padding: 3rem; color: #6b7280;">
                        <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                        <p>No teams registered yet</p>
                    </div>
                <?php else: ?>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Department</th>
                                    <th>Team Name</th>
                                    <th>Participants</th>
                                    <th>Status</th>
                                    <th>Registration Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($registrations as $registration): ?>
                                    <tr>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                                <div style="width: 20px; height: 20px; background: <?php echo $registration['color_code'] ?? '#6b7280'; ?>; border-radius: 4px;"></div>
                                                <div>
                                                    <div style="font-weight: 500;"><?php echo htmlspecialchars($registration['department_name']); ?></div>
                                                    <div style="font-size: 0.8rem; color: #6b7280;"><?php echo htmlspecialchars($registration['department_abbr']); ?></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($registration['team_name'] ?: $registration['department_name']); ?></td>
                                        <td><?php echo $registration['participants'] ?: 'Not specified'; ?></td>
                                        <td>
                                            <span class="status-badge" style="background: <?php echo $registration['registration_status'] === 'confirmed' ? '#10b981' : '#f59e0b'; ?>; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                                                <?php echo ucfirst($registration['registration_status']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('M j, Y', strtotime($registration['registration_date'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Fixtures Tab -->
        <div id="fixtures-tab" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-calendar-alt"></i> Match Fixtures
                    </h2>
                    <div style="display: flex; gap: 1rem;">
                        <span class="btn btn-secondary btn-sm">
                            <?php echo $category_stats['total_matches']; ?> Total Matches
                        </span>
                        <span class="btn btn-success btn-sm">
                            <?php echo $category_stats['completed_matches']; ?> Completed
                        </span>
                    </div>
                </div>

                <?php if (empty($matches)): ?>
                    <div style="text-align: center; padding: 3rem; color: #6b7280;">
                        <i class="fas fa-calendar-alt" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                        <p>No matches scheduled yet</p>
                        <p style="font-size: 0.9rem;">Matches will appear here once the tournament bracket is generated.</p>
                    </div>
                <?php else: ?>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Match</th>
                                    <th>Teams</th>
                                    <th>Scheduled Time</th>
                                    <th>Status</th>
                                    <th>Result</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($matches as $match): ?>
                                    <tr>
                                        <td>
                                            <div style="font-weight: 500;">Round <?php echo $match['round_number']; ?></div>
                                            <div style="font-size: 0.8rem; color: #6b7280;">Match <?php echo $match['match_number']; ?></div>
                                        </td>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                                <div style="display: flex; align-items: center; gap: 0.25rem;">
                                                    <div style="width: 12px; height: 12px; background: <?php echo $match['team1_color'] ?? '#6b7280'; ?>; border-radius: 2px;"></div>
                                                    <span><?php echo htmlspecialchars($match['team1_name'] ?: 'TBD'); ?></span>
                                                </div>
                                                <span style="color: #6b7280;">vs</span>
                                                <div style="display: flex; align-items: center; gap: 0.25rem;">
                                                    <div style="width: 12px; height: 12px; background: <?php echo $match['team2_color'] ?? '#6b7280'; ?>; border-radius: 2px;"></div>
                                                    <span><?php echo htmlspecialchars($match['team2_name'] ?: 'TBD'); ?></span>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($match['scheduled_time']): ?>
                                                <?php echo date('M j, Y g:i A', strtotime($match['scheduled_time'])); ?>
                                            <?php else: ?>
                                                <span style="color: #6b7280;">Not scheduled</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="status-badge" style="background:
                                                <?php
                                                    switch($match['status']) {
                                                        case 'completed': echo '#10b981'; break;
                                                        case 'ongoing': echo '#f59e0b'; break;
                                                        case 'scheduled': echo '#3b82f6'; break;
                                                        default: echo '#6b7280';
                                                    }
                                                ?>; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                                                <?php echo ucfirst($match['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($match['status'] === 'completed' && $match['winner_id']): ?>
                                                <div style="font-weight: 500; color: #10b981;">
                                                    <?php
                                                        if ($match['winner_id'] == $match['team1_id']) {
                                                            echo htmlspecialchars($match['team1_name']);
                                                        } else {
                                                            echo htmlspecialchars($match['team2_name']);
                                                        }
                                                    ?>
                                                </div>
                                                <?php if ($match['team1_score'] !== null && $match['team2_score'] !== null): ?>
                                                    <div style="font-size: 0.8rem; color: #6b7280;">
                                                        <?php echo $match['team1_score']; ?> - <?php echo $match['team2_score']; ?>
                                                    </div>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span style="color: #6b7280;">-</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Standings Tab -->
        <div id="standings-tab" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-trophy"></i> Current Standings
                    </h2>
                    <span class="btn btn-secondary btn-sm">
                        <?php echo count($standings); ?> Teams
                    </span>
                </div>

                <?php if (empty($standings)): ?>
                    <div style="text-align: center; padding: 3rem; color: #6b7280;">
                        <i class="fas fa-trophy" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                        <p>No standings available yet</p>
                        <p style="font-size: 0.9rem;">Standings will appear here once matches are completed.</p>
                    </div>
                <?php else: ?>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Team</th>
                                    <th>Matches</th>
                                    <th>Wins</th>
                                    <th>Losses</th>
                                    <th>Points</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($standings as $standing): ?>
                                    <tr>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                                <span style="font-weight: 700; font-size: 1.2rem;">
                                                    <?php echo $standing['rank']; ?>
                                                </span>
                                                <?php if ($standing['rank'] <= 3): ?>
                                                    <i class="fas fa-medal" style="color:
                                                        <?php
                                                            switch($standing['rank']) {
                                                                case 1: echo '#ffd700'; break;
                                                                case 2: echo '#c0c0c0'; break;
                                                                case 3: echo '#cd7f32'; break;
                                                            }
                                                        ?>;"></i>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                                <div style="width: 20px; height: 20px; background: <?php echo $standing['color_code'] ?? '#6b7280'; ?>; border-radius: 4px;"></div>
                                                <div>
                                                    <div style="font-weight: 500;"><?php echo htmlspecialchars($standing['team_name']); ?></div>
                                                    <div style="font-size: 0.8rem; color: #6b7280;"><?php echo htmlspecialchars($standing['department_abbr']); ?></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo $standing['matches_played']; ?></td>
                                        <td style="color: #10b981; font-weight: 500;"><?php echo $standing['wins']; ?></td>
                                        <td style="color: #ef4444; font-weight: 500;"><?php echo $standing['losses']; ?></td>
                                        <td>
                                            <span style="background: #3b82f6; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-weight: 500;">
                                                <?php echo $standing['points']; ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        // Tab switching functionality
        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName + '-tab').classList.add('active');

            // Add active class to clicked tab button
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        }

        // Save category settings
        async function saveCategorySettings() {
            const formData = new FormData();
            formData.append('action', 'update_category');
            formData.append('category_id', <?php echo $category_id; ?>);
            formData.append('category_name', document.getElementById('categoryName').value);
            formData.append('category_type', document.getElementById('categoryType').value);
            formData.append('max_participants', document.getElementById('maxParticipants').value);
            formData.append('category_status', document.getElementById('categoryStatus').value);
            formData.append('referee_name', document.getElementById('refereeName').value);
            formData.append('referee_email', document.getElementById('refereeEmail').value);
            formData.append('venue', document.getElementById('venue').value);
            formData.append('registration_deadline', document.getElementById('registrationDeadline').value);

            try {
                const response = await fetch('ajax/category_actions.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // Show success message
                    showNotification('Category settings updated successfully!', 'success');

                    // Optionally reload the page to reflect changes
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showNotification(result.message || 'Failed to update category settings', 'error');
                }
            } catch (error) {
                console.error('Error updating category:', error);
                showNotification('An error occurred while updating category settings', 'error');
            }
        }

        // Show notification function
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 1000;
                animation: slideIn 0.3s ease;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
            `;
            notification.textContent = message;

            // Add to page
            document.body.appendChild(notification);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
