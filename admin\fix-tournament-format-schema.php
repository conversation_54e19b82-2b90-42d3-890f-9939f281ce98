<?php
/**
 * Direct Database Schema Fix for Tournament Format Issues
 * Run this script to fix all tournament format related database issues
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "Starting tournament format schema fix...\n\n";

try {
    // Step 1: Check if tournament_formats table exists
    echo "Step 1: Checking tournament_formats table...\n";
    $stmt = $conn->prepare("SHOW TABLES LIKE 'tournament_formats'");
    $stmt->execute();
    $table_exists = $stmt->fetch();
    
    if (!$table_exists) {
        echo "Creating tournament_formats table...\n";
        $sql = "CREATE TABLE tournament_formats (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            code VARCHAR(50) NOT NULL UNIQUE,
            description TEXT,
            sport_types TEXT,
            min_participants INT DEFAULT 2,
            max_participants INT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $conn->exec($sql);
        echo "✓ Created tournament_formats table\n";
    } else {
        echo "✓ tournament_formats table exists\n";
    }
    
    // Step 2: Ensure we have basic tournament formats
    echo "\nStep 2: Checking tournament formats data...\n";
    $stmt = $conn->prepare("SELECT COUNT(*) FROM tournament_formats");
    $stmt->execute();
    $format_count = $stmt->fetchColumn();
    
    if ($format_count == 0) {
        echo "Creating basic tournament formats...\n";
        $formats = [
            ['Single Elimination', 'single_elimination', 'Standard single elimination tournament', 'team,individual', 2, NULL],
            ['Double Elimination', 'double_elimination', 'Double elimination with winner and loser brackets', 'team,individual', 2, NULL],
            ['Round Robin', 'round_robin', 'All teams play against each other', 'team,individual', 2, 16],
            ['Swiss System', 'swiss_system', 'Swiss system tournament format', 'academic,individual', 4, NULL],
            ['Judged Rounds', 'judged_rounds', 'Performance-based judged competition', 'judged,performance', 1, NULL]
        ];
        
        foreach ($formats as $format) {
            $stmt = $conn->prepare("INSERT INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->execute($format);
        }
        echo "✓ Created " . count($formats) . " tournament formats\n";
    } else {
        echo "✓ Tournament formats exist ($format_count found)\n";
    }
    
    // Step 3: Check if tournament_format_id column exists in event_sports
    echo "\nStep 3: Checking event_sports table structure...\n";
    $stmt = $conn->prepare("SHOW COLUMNS FROM event_sports LIKE 'tournament_format_id'");
    $stmt->execute();
    $column_exists = $stmt->fetch();
    
    if (!$column_exists) {
        echo "Adding tournament_format_id column to event_sports...\n";
        $stmt = $conn->prepare("ALTER TABLE event_sports ADD COLUMN tournament_format_id INT NULL AFTER sport_id");
        $stmt->execute();
        echo "✓ Added tournament_format_id column\n";
    } else {
        echo "✓ tournament_format_id column exists\n";
    }
    
    // Step 4: Update existing event_sports records with default tournament format
    echo "\nStep 4: Updating existing event_sports records...\n";
    $stmt = $conn->prepare("SELECT id FROM tournament_formats WHERE code = 'single_elimination' LIMIT 1");
    $stmt->execute();
    $default_format_id = $stmt->fetchColumn();
    
    if ($default_format_id) {
        $stmt = $conn->prepare("UPDATE event_sports SET tournament_format_id = ? WHERE tournament_format_id IS NULL");
        $stmt->execute([$default_format_id]);
        $updated = $stmt->rowCount();
        echo "✓ Updated $updated event_sports records with default tournament format\n";
    } else {
        echo "⚠ No default tournament format found\n";
    }
    
    // Step 5: Add foreign key constraint (if it doesn't exist)
    echo "\nStep 5: Checking foreign key constraint...\n";
    $stmt = $conn->prepare("
        SELECT CONSTRAINT_NAME 
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'event_sports' 
        AND COLUMN_NAME = 'tournament_format_id' 
        AND REFERENCED_TABLE_NAME = 'tournament_formats'
    ");
    $stmt->execute();
    $fk_exists = $stmt->fetch();
    
    if (!$fk_exists) {
        echo "Adding foreign key constraint...\n";
        try {
            $stmt = $conn->prepare("ALTER TABLE event_sports ADD CONSTRAINT fk_event_sports_tournament_format FOREIGN KEY (tournament_format_id) REFERENCES tournament_formats(id) ON DELETE SET NULL");
            $stmt->execute();
            echo "✓ Added foreign key constraint\n";
        } catch (Exception $e) {
            echo "⚠ Could not add foreign key constraint: " . $e->getMessage() . "\n";
            echo "This is usually not critical for functionality.\n";
        }
    } else {
        echo "✓ Foreign key constraint exists\n";
    }
    
    // Step 6: Verify the fix
    echo "\nStep 6: Verifying the fix...\n";
    $stmt = $conn->prepare("
        SELECT es.id, es.tournament_format_id, tf.name as format_name
        FROM event_sports es
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        LIMIT 5
    ");
    $stmt->execute();
    $results = $stmt->fetchAll();
    
    if (!empty($results)) {
        echo "✓ Verification successful. Sample data:\n";
        foreach ($results as $row) {
            echo "  Event Sport ID: {$row['id']}, Format ID: {$row['tournament_format_id']}, Format: {$row['format_name']}\n";
        }
    } else {
        echo "⚠ No event_sports records found for verification\n";
    }
    
    echo "\n✅ Tournament format schema fix completed successfully!\n";
    echo "\nNext steps:\n";
    echo "1. Test the category management page\n";
    echo "2. Add a new sport to an event to test tournament format saving\n";
    echo "3. Verify tournament format displays correctly on category pages\n";
    
} catch (Exception $e) {
    echo "\n❌ Error during schema fix: " . $e->getMessage() . "\n";
    echo "Please check the error and try again.\n";
}

// If running from web browser, add HTML formatting
if (isset($_SERVER['HTTP_HOST'])) {
    echo "<br><br><a href='test-category-fixes.php'>← Back to Test Page</a>";
    echo " | <a href='manage-category.php?event_id=1&sport_id=1&category_id=1'>Test Category Page</a>";
}
?>
